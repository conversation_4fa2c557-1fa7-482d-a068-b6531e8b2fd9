<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="Mozilla/5.0" version="21.6.8" type="device">
<diagram id="slot-processing-flow" name="槽位处理流程">
<mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
<root>
<mxCell id="0"/>
<mxCell id="1" parent="0"/>

<!-- 处理阶段 -->
<mxCell id="2" value="预处理阶段" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
<mxGeometry x="40" y="40" width="200" height="120" as="geometry"/>
</mxCell>

<mxCell id="3" value="- 文本规范化&#10;- 特殊字符处理&#10;- 空白字符处理&#10;- 数字标准化" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="2">
<mxGeometry y="30" width="200" height="90" as="geometry"/>
</mxCell>

<mxCell id="4" value="抽取阶段" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
<mxGeometry x="280" y="40" width="200" height="120" as="geometry"/>
</mxCell>

<mxCell id="5" value="- 专用抽取器并行处理&#10;- 通用关键词匹配&#10;- 槽位值识别&#10;- 位置信息记录" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="4">
<mxGeometry y="30" width="200" height="90" as="geometry"/>
</mxCell>

<mxCell id="6" value="后处理阶段" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
<mxGeometry x="520" y="40" width="200" height="120" as="geometry"/>
</mxCell>

<mxCell id="7" value="- 结果去重&#10;- 槽位冲突处理&#10;- 值标准化&#10;- 格式转换" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;" vertex="1" parent="6">
<mxGeometry y="30" width="200" height="90" as="geometry"/>
</mxCell>

<!-- 连接箭头 -->
<mxCell id="8" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="5">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

<mxCell id="9" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="7">
<mxGeometry width="50" height="50" relative="1" as="geometry"/>
</mxCell>

</root>
</mxGraphModel>
</diagram>
</mxfile>