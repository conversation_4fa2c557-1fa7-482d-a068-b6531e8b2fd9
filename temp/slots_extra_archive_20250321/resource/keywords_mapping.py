"""
关键词映射文件
包含所有槽位条件值对应的关键词

输入格式:
    text: str - 包含金额信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的金额信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot_value": str,       # 槽位值
        "value": float           # 
    }
"""

import re
import json 
from typing import Dict, List, Any

# 关键词映射
with open("resource/keywords.json", "r", encoding="utf-8") as f:
    KEYWORD_MAPPING = json.load(f)

# 创建编译后的关键词模式
def create_compiled_patterns():
    """
    将关键词编译为模式，用于文本匹配
    
    Returns:
        Dict: 槽位 -> 条件值 -> [(编译后的模式, 原关键词)]的嵌套字典
    """
    compiled_patterns = {}
    
    for slot, values in KEYWORD_MAPPING.items():
        compiled_patterns[slot] = {}
        
        for value, keywords in values.items():
            compiled_patterns[slot][value] = []
            
            for keyword in keywords:
                pattern = re.compile(re.escape(keyword))
                compiled_patterns[slot][value].append((pattern, keyword))
                
    return compiled_patterns

# 编译后的关键词模式
COMPILED_KEYWORD_PATTERNS = create_compiled_patterns()

# 获取所有槽位的键名
def get_all_slot_keys() -> List[str]:
    """获取所有槽位键名列表"""
    return list(KEYWORD_MAPPING.keys())

# 获取指定槽位的所有条件值键名
def get_slot_values(slot: str) -> List[str]:
    """获取指定槽位的所有条件值键名"""
    if slot in KEYWORD_MAPPING:
        return list(KEYWORD_MAPPING[slot].keys())
    return []

# 获取指定槽位和条件值的所有关键词
def get_keywords(slot: str, value: str) -> List[str]:
    """获取指定槽位和条件值的所有关键词"""
    if slot in KEYWORD_MAPPING and value in KEYWORD_MAPPING[slot]:
        return KEYWORD_MAPPING[slot][value]
    return [] 