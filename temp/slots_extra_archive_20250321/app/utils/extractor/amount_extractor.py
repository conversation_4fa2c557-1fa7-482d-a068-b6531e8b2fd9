"""
金额抽取器

专注于提取金融文本中的金额值

输入格式:
    text: str - 包含金额信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的金额信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,                 # 槽位名称
        "slot_value": str,       # 槽位值类型：'amount_range' 或 'specific_amount'
        # 对于 amount_range:
        "minValue": float,       # 范围最小值（元），可能为 None
        "maxValue": float,       # 范围最大值（元），可能为 None
        # 对于 specific_amount:
        "value": float           # 具体金额值（元）
    }
"""

import re
from typing import Dict, List, Any, Tuple, Optional, Pattern, ClassVar

class AmountExtractor:
    """
    金额抽取器
    专注于从文本中提取金额值信息（通常为元、万元等带有货币单位的值）
    """
    
    # 基础数值模式
    NUMBER_PATTERN: ClassVar[str] = r"(?:\d+(?:\.\d+)?)"
    
    # 货币单位模式
    CURRENCY_UNITS: ClassVar[Dict[str, List[str]]] = {
        "CNY": ["元", "块", "人民币", "rmb", "RMB", "￥"],
        "TEN_THOUSAND": ["万", "w", "W"],
        "HUNDRED_THOUSAND": ["十万"],
        "MILLION": ["百万", "佰万"],
        "TEN_MILLION": ["千万"],
        "HUNDRED_MILLION": ["亿"],
        "THOUSAND": ["千", "仟"],
        "HUNDRED": ["百", "佰"]
    }
    
    # 数值修饰词
    AMOUNT_MODIFIERS: ClassVar[Dict[str, List[str]]] = {
        "PREFIX": ["人民币", "约", "大约", "大概", "差不多", "接近", "将近", "快", "差", "至少", "最少", "最多", "最高"],
        "SUFFIX": ["左右", "上下", "出头", "不到", "多一点", "多点", "多些", "多", "几"]
    }
    
    # 范围连接词
    RANGE_CONNECTORS: ClassVar[List[str]] = ["到", "至", "-", "~", "—", "－", "→"]
    
    # 范围描述词
    RANGE_DESCRIPTORS: ClassVar[Dict[str, List[str]]] = {
        "ABOVE": ["以上", "起", "开外", "多", "往上", "起步", "打底", "为主", "及以上", "或以上", "或更多", "最低", "至少"],
        "BELOW": ["以下", "之内", "以内", "内", "下", "之下", "及以下", "或以下", "不超过", "最高", "封顶", "顶多", "至多"],
        "BETWEEN": ["之间", "上下", "左右", "前后", "范围内", "区间"]
    }
    
    # 中文数字到阿拉伯数字的映射
    CHINESE_DIGITS: ClassVar[Dict[str, str]] = {
        "零": "0", "一": "1", "二": "2", "两": "2", "三": "3", "四": "4", "五": "5",
        "六": "6", "七": "7", "八": "8", "九": "9", "十": "10",
        "百": "100", "佰": "100", "千": "1000", "仟": "1000", 
        "万": "10000", "十万": "100000", "百万": "1000000", "佰万": "1000000",
        "千万": "10000000", "亿": "100000000"
    }
    
    # 抽象金额描述
    ABSTRACT_AMOUNTS: ClassVar[Dict[str, Dict[str, Any]]] = {
        "LARGE": {
            "patterns": ["大额", "高额", "巨额", "大笔", "重金", "高价", "天价"],
            "range": (50000, None)
        },
        "SMALL": {
            "patterns": ["小额", "低额", "微额", "少量", "便宜", "低价", "廉价"],
            "range": (0, 10000)
        },
        "MEDIUM": {
            "patterns": ["中等额度", "中额", "适中", "一般", "正常价位"],
            "range": (10000, 50000)
        },
        "FLEXIBLE": {
            "patterns": ["灵活", "弹性", "不等", "视情况而定"],
            "range": (1000, None)
        }
    }
    
    # 特殊金额表达
    SPECIAL_AMOUNT_PATTERNS: ClassVar[Dict[str, List[Tuple[str, str]]]] = {
        "APPROXIMATE": [
            (r"几[十百千万亿]", "approximate_large"),  # 几十万、几百万等
            (r"数[十百千万亿]", "approximate_large"),  # 数十万、数百万等
            (r"[一两三四五六七八九十]些", "approximate_small"),  # 一些、两些等
            (r"若干", "approximate_medium")
        ]
    }
    
    # 编译好的正则表达式模式
    _PATTERNS: ClassVar[Dict[str, List[Tuple[Pattern, str]]]] = {}
    
    @classmethod
    def _ensure_patterns_compiled(cls) -> None:
        """确保正则表达式模式已编译"""
        if not cls._PATTERNS:
            cls._PATTERNS = cls._compile_patterns()
    
    @classmethod
    def _compile_patterns(cls) -> Dict[str, List[Tuple[Pattern, str]]]:
        """编译所有正则表达式模式"""
        patterns = {
            "range": [],
            "specific": [],
            "abstract": [],
            "special": []
        }
        
        # 1. 具体金额范围模式
        range_pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*(?:{cls._build_range_connector_pattern()})\\s*{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}"
        patterns["range"].append((re.compile(range_pattern), "explicit_range"))
        
        # 2. 以上/以下范围模式
        for direction, terms in cls.RANGE_DESCRIPTORS.items():
            for term in terms:
                if direction == "ABOVE":
                    pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "above"))
                elif direction == "BELOW":
                    pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "below"))
                elif direction == "BETWEEN":
                    pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "between"))
        
        # 3. 具体金额模式
        specific_pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}"
        patterns["specific"].append((re.compile(specific_pattern), "specific"))
        
        # 4. 中文数字金额模式
        for cn_num, value in cls.CHINESE_DIGITS.items():
            if int(value) >= 1000:  # 只处理千及以上的中文数字
                for unit in cls.CURRENCY_UNITS["CNY"]:
                    pattern = f"{cn_num}{unit}"
                    patterns["specific"].append((re.compile(pattern), "chinese_specific"))
        
        # 5. 抽象金额模式
        for amount_type, config in cls.ABSTRACT_AMOUNTS.items():
            for pattern in config["patterns"]:
                patterns["abstract"].append((re.compile(pattern), amount_type.lower()))
        
        # 6. 特殊金额表达模式
        for pattern_type, pattern_list in cls.SPECIAL_AMOUNT_PATTERNS.items():
            for pattern, pattern_name in pattern_list:
                patterns["special"].append((re.compile(pattern), pattern_name))
        
        return patterns
    
    @classmethod
    def _build_unit_pattern(cls) -> str:
        """构建单位模式"""
        all_units = []
        for units in cls.CURRENCY_UNITS.values():
            all_units.extend(units)
        return f"(?:{'|'.join(re.escape(unit) for unit in all_units)})"
    
    @classmethod
    def _build_range_connector_pattern(cls) -> str:
        """构建范围连接词模式"""
        return '|'.join(re.escape(connector) for connector in cls.RANGE_CONNECTORS)
    
    @classmethod
    def extract_amount(cls, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取金额信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的金额信息列表
        """
        cls._ensure_patterns_compiled()
        
        extracted_values = []
        covered_positions = set()  # 记录已覆盖的位置
        
        # 1. 处理范围金额
        range_values = cls._extract_range_amounts(text, covered_positions)
        extracted_values.extend(range_values)
        
        # 2. 处理具体金额
        specific_values = cls._extract_specific_amounts(text, covered_positions)
        extracted_values.extend(specific_values)
        
        # 3. 处理抽象金额描述
        abstract_values = cls._extract_abstract_amounts(text, covered_positions)
        extracted_values.extend(abstract_values)
        
        # 4. 处理特殊金额表达
        special_values = cls._extract_special_amounts(text, covered_positions)
        extracted_values.extend(special_values)
        
        return cls._filter_and_normalize_values(extracted_values)
    
    @classmethod
    def _extract_range_amounts(cls, text: str, covered_positions: set) -> List[Dict[str, Any]]:
        """提取范围金额"""
        extracted = []
        
        for pattern, pattern_type in cls._PATTERNS["range"]:
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                if cls._is_position_covered(start, end, covered_positions):
                    continue
                    
                try:
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "slot": "number",
                        "slot_value": "amount_range"
                    }
                    
                    if pattern_type == "explicit_range":
                        numbers = cls._extract_numbers_from_text(match_text)
                        if len(numbers) >= 2:
                            min_val, max_val = cls._normalize_range_values(numbers[0], numbers[1], match_text)
                            match_info.update({
                                "minValue": min_val,
                                "maxValue": max_val
                            })
                    elif pattern_type == "above":
                        number = cls._extract_numbers_from_text(match_text)[0]
                        value = cls._normalize_amount(number, match_text)
                        match_info.update({
                            "minValue": value,
                            "maxValue": None
                        })
                    elif pattern_type == "below":
                        number = cls._extract_numbers_from_text(match_text)[0]
                        value = cls._normalize_amount(number, match_text)
                        match_info.update({
                            "minValue": 0,
                            "maxValue": value
                        })
                    
                    cls._mark_covered_positions(start, end, covered_positions)
                    extracted.append(match_info)
                except Exception:
                    continue
                    
        return extracted
    
    @classmethod
    def _extract_specific_amounts(cls, text: str, covered_positions: set) -> List[Dict[str, Any]]:
        """提取具体金额"""
        extracted = []
        
        for pattern, pattern_type in cls._PATTERNS["specific"]:
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                if cls._is_position_covered(start, end, covered_positions):
                    continue
                    
                try:
                    value = cls._normalize_amount(match_text, match_text)
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "value": value,
                        "slot": "number",
                        "slot_value": "specific_amount"
                    }
                    
                    cls._mark_covered_positions(start, end, covered_positions)
                    extracted.append(match_info)
                except Exception:
                    continue
                    
        return extracted
    
    @classmethod
    def _extract_abstract_amounts(cls, text: str, covered_positions: set) -> List[Dict[str, Any]]:
        """提取抽象金额描述"""
        extracted = []
        
        for pattern, amount_type in cls._PATTERNS["abstract"]:
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                if cls._is_position_covered(start, end, covered_positions):
                    continue
                    
                try:
                    config = next(
                        (conf for type_, conf in cls.ABSTRACT_AMOUNTS.items() 
                         if type_.lower() == amount_type),
                        None
                    )
                    
                    if config:
                        match_info = {
                            "match": match_text,
                            "position": (start, end),
                            "slot": "number",
                            "slot_value": "amount_range",
                            "minValue": config["range"][0],
                            "maxValue": config["range"][1]
                        }
                        
                        cls._mark_covered_positions(start, end, covered_positions)
                        extracted.append(match_info)
                except Exception:
                    continue
                    
        return extracted
    
    @classmethod
    def _extract_special_amounts(cls, text: str, covered_positions: set) -> List[Dict[str, Any]]:
        """提取特殊金额表达"""
        extracted = []
        
        for pattern, pattern_type in cls._PATTERNS["special"]:
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                if cls._is_position_covered(start, end, covered_positions):
                    continue
                    
                try:
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "slot": "number",
                        "slot_value": "amount_range"
                    }
                    
                    # 根据不同的特殊表达类型设置范围
                    if pattern_type == "approximate_large":
                        base_unit = 10000  # 默认万为基本单位
                        if "百" in match_text or "佰" in match_text:
                            base_unit = 100000
                        elif "千" in match_text or "仟" in match_text:
                            base_unit = 1000000
                        elif "亿" in match_text:
                            base_unit = 100000000
                            
                        match_info.update({
                            "minValue": base_unit,
                            "maxValue": base_unit * 10
                        })
                    elif pattern_type == "approximate_small":
                        match_info.update({
                            "minValue": 100,
                            "maxValue": 10000
                        })
                    elif pattern_type == "approximate_medium":
                        match_info.update({
                            "minValue": 1000,
                            "maxValue": 50000
                        })
                    
                    cls._mark_covered_positions(start, end, covered_positions)
                    extracted.append(match_info)
                except Exception:
                    continue
                    
        return extracted
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos)
    
    @classmethod
    def _extract_numbers_from_text(cls, text: str) -> List[str]:
        """从文本中提取数字"""
        return re.findall(cls.NUMBER_PATTERN, text)
    
    @classmethod
    def _normalize_amount(cls, amount_text: str, context: str) -> float:
        """标准化金额值（转换为元）"""
        # 处理中文数字
        if amount_text in cls.CHINESE_DIGITS:
            return float(cls.CHINESE_DIGITS[amount_text])
            
        # 处理数字
        amount = float(re.sub(r'[^\d.]', '', amount_text))
        
        # 根据上下文判断单位
        if any(unit in context for unit in cls.CURRENCY_UNITS["HUNDRED_MILLION"]):
            amount *= 100000000
        elif any(unit in context for unit in cls.CURRENCY_UNITS["TEN_MILLION"]):
            amount *= 10000000
        elif any(unit in context for unit in cls.CURRENCY_UNITS["MILLION"]):
            amount *= 1000000
        elif any(unit in context for unit in cls.CURRENCY_UNITS["HUNDRED_THOUSAND"]):
            amount *= 100000
        elif any(unit in context for unit in cls.CURRENCY_UNITS["TEN_THOUSAND"]):
            amount *= 10000
        elif any(unit in context for unit in cls.CURRENCY_UNITS["THOUSAND"]):
            amount *= 1000
        elif any(unit in context for unit in cls.CURRENCY_UNITS["HUNDRED"]):
            amount *= 100
            
        return amount
    
    @classmethod
    def _normalize_range_values(cls, min_text: str, max_text: str, context: str) -> Tuple[float, float]:
        """标准化范围值"""
        min_val = cls._normalize_amount(min_text, context)
        max_val = cls._normalize_amount(max_text, context)
        return min_val, max_val
    
    @classmethod
    def _filter_and_normalize_values(cls, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤和标准化提取的值"""
        filtered_values = []
        
        for value in values:
            # 检查值的合理性
            is_valid = False
            if "value" in value and value["value"] >= 10:  # 金额至少10元
                is_valid = True
            elif "minValue" in value and "maxValue" in value:
                if value["minValue"] >= 0 and (value["maxValue"] is None or value["maxValue"] >= value["minValue"]):
                    is_valid = True
                    
            if is_valid:
                filtered_values.append(value)
                
        return filtered_values 