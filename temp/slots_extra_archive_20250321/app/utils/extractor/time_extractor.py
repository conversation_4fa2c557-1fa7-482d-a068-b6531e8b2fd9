"""
时间抽取器

专注于提取金融文本中的时间信息，抽取两类时间范围：
1. 相对时间范围：如"最近七天"、"过去一个月"、"过去三到六个月"等
2. 持续时间范围：如"半年"、"一年"、"90天"、"三个月"、"七天"等
3. 一年按366天计算，一个月按30天计算

输入格式:
    text: str - 包含时间信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'relative_time_range' 或 'duration_time_range'
        "minValue": float,       # 范围最小值（天数）
        "maxValue": float,       # 范围最大值（天数），可能为 None
    }
    
备注：
    在相对时间范围情况中，minValue 和 maxValue 的值为负数，表示相对于当前时间的时间范围。如：
    - 今年2月份：minValue = -50，maxValue = 0
    - 最近七天：minValue = -7，maxValue = 0
    - 过去一个月：minValue = -30，maxValue = 0
    - 过去三到六个月：minValue = -360，maxValue = -180
    
    在持续时间范围情况中，minValue 和 maxValue 的值为正数，表示持续时间范围。如：
    - 半年：minValue = 180，maxValue = 360
    - 一年到两年：minValue = 366，maxValue = 732
    - 90天左右：minValue = 85，maxValue = 95
    - 三到六个月：minValue = 90，maxValue = 180
    - 七天：minValue = 7，maxValue = 7
    - 5年以上：minValue = 1826，maxValue = None
"""

import re
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime

class TimeExtractor:
    def __init__(self):
        # 相对时间范围的正则模式
        self.relative_patterns = [
            r'最近[一两二三四五六七八九十]\d*天',
            r'过去[一两二三四五六七八九十]\d*个月',
            r'今年\d{1,2}月份?',
            r'上个月',
            r'上周',
            r'过去[一两二三四五六七八九十]\d*到[一两二三四五六七八九十]\d*个月'
        ]
        
        # 持续时间范围的正则模式
        self.duration_patterns = [
            r'[一两二三四五六七八九十]\d*年',
            r'[一两二三四五六七八九十]\d*个月',
            r'[一两二三四五六七八九十]\d*天',
            r'\d+天',
            r'半年',
            r'[一两二三四五六七八九十]\d*到[一两二三四五六七八九十]\d*年',
            r'[一两二三四五六七八九十]\d*到[一两二三四五六七八九十]\d*个月'
        ]
        
        # 中文数字到阿拉伯数字的映射
        self.cn_num = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '两': 2
        }

    def _convert_cn_to_num(self, text: str) -> int:
        """将中文数字转换为阿拉伯数字"""
        if text.isdigit():
            return int(text)
        
        result = 0
        for char in text:
            if char in self.cn_num:
                result = result * 10 + self.cn_num[char]
        return result if result > 0 else 1

    def _find_all_matches(self, text: str, patterns: List[str]) -> List[Dict[str, Any]]:
        """查找所有匹配的时间表达式"""
        matches = []
        for pattern in patterns:
            for match in re.finditer(pattern, text):
                matches.append({
                    'match': match.group(),
                    'position': (match.start(), match.end())
                })
        return matches

    def _resolve_overlaps(self, matches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解决重叠匹配，保留最长匹配"""
        if not matches:
            return []
            
        # 按开始位置排序
        matches.sort(key=lambda x: (x['position'][0], -x['position'][1]))
        
        result = [matches[0]]
        for curr in matches[1:]:
            prev = result[-1]
            # 检查是否有重叠
            if curr['position'][0] >= prev['position'][1]:
                result.append(curr)
            else:
                # 如果当前匹配更长，替换之前的匹配
                if (curr['position'][1] - curr['position'][0]) > (prev['position'][1] - prev['position'][0]):
                    result[-1] = curr
        
        return result

    def _calculate_relative_time_value(self, match: str) -> Tuple[float, float]:
        """计算相对时间范围的天数值"""
        now = datetime.now()
        
        if '最近' in match or '过去' in match:
            if '天' in match:
                match_result = re.search(r'[一两二三四五六七八九十]\d*', match)
                if match_result:
                    days = self._convert_cn_to_num(match_result.group())
                    return -days, 0
            elif '月' in match:
                if '到' in match:
                    nums = re.findall(r'[一两二三四五六七八九十]\d*', match)
                    if len(nums) >= 2:
                        min_months = self._convert_cn_to_num(nums[0])
                        max_months = self._convert_cn_to_num(nums[1])
                        return -max_months * 30, -min_months * 30
                else:
                    match_result = re.search(r'[一两二三四五六七八九十]\d*', match)
                    if match_result:
                        months = self._convert_cn_to_num(match_result.group())
                        return -months * 30, 0
        
        elif '今年' in match:
            match_result = re.search(r'\d{1,2}', match)
            if match_result:
                month = int(match_result.group())
                target_date = datetime(now.year, month, 1)
                days = (now - target_date).days
                return -days, 0
        
        elif '上个月' in match:
            if now.month == 1:
                last_month = datetime(now.year - 1, 12, 1)
            else:
                last_month = datetime(now.year, now.month - 1, 1)
            days = (now - last_month).days
            return -days, 0
        
        elif '上周' in match:
            return -7, 0
            
        return 0, 0

    def _calculate_duration_time_value(self, match: str) -> Tuple[float, Optional[float]]:
        """计算持续时间范围的天数值"""
        if '到' in match:
            nums = re.findall(r'[一两二三四五六七八九十]\d*', match)
            if len(nums) >= 2:
                min_num = self._convert_cn_to_num(nums[0])
                max_num = self._convert_cn_to_num(nums[1])
                
                if '年' in match:
                    return min_num * 366, max_num * 366
                elif '月' in match:
                    return min_num * 30, max_num * 30
                
        elif '以上' in match or '以后' in match:
            match_result = re.search(r'[一两二三四五六七八九十]\d*', match)
            if match_result:
                num = self._convert_cn_to_num(match_result.group())
                if '年' in match:
                    return num * 366, None
                elif '月' in match:
                    return num * 30, None
                
        elif '以内' in match or '之内' in match:
            match_result = re.search(r'[一两二三四五六七八九十]\d*', match)
            if match_result:
                num = self._convert_cn_to_num(match_result.group())
                if '年' in match:
                    return None, num * 366
                elif '月' in match:
                    return None, num * 30
                
        else:
            match_result = re.search(r'[一两二三四五六七八九十]\d*', match)
            if match_result:
                if '年' in match:
                    num = self._convert_cn_to_num(match_result.group())
                    return num * 366, num * 366
                elif '月' in match:
                    num = self._convert_cn_to_num(match_result.group())
                    return num * 30, num * 30
                elif '天' in match:
                    if match[:-1].isdigit():
                        days = int(match[:-1])
                    else:
                        days = self._convert_cn_to_num(match_result.group())
                    return days, days
            elif '半年' in match:
                return 180, 180
                
        return 0, 0

    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取时间信息
        
        Args:
            text: 输入文本
            
        Returns:
            提取的时间信息列表
        """
        # 查找所有匹配
        relative_matches = self._find_all_matches(text, self.relative_patterns)
        duration_matches = self._find_all_matches(text, self.duration_patterns)
        
        # 为每个匹配添加类型标记
        for match in relative_matches:
            match['slot_value'] = 'relative_time_range'
        for match in duration_matches:
            match['slot_value'] = 'duration_time_range'
            
        # 合并所有匹配并解决重叠
        all_matches = self._resolve_overlaps(relative_matches + duration_matches)
        
        # 计算时间值
        result = []
        for match in all_matches:
            if match['slot_value'] == 'relative_time_range':
                min_value, max_value = self._calculate_relative_time_value(match['match'])
            else:
                min_value, max_value = self._calculate_duration_time_value(match['match'])
                
            result.append({
                'match': match['match'],
                'position': match['position'],
                'slot': 'time',
                'slot_value': match['slot_value'],
                'minValue': min_value,
                'maxValue': max_value
            })
            
        return result
