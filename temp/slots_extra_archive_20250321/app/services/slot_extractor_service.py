"""
基于规则的理财槽位抽取服务

输入格式:
    text: str - 用户输入的文本
    
输出格式:
    Dict[str, Dict[str, Any]]: 提取的槽位信息
        格式：{
            slot_name: {
                "match": 匹配到的文本,
                "slot_value": 槽位值名称,
                "startPos": 开始位置,
                "endPos": 结束位置,
                # 如果槽位值为范围值，则包含以下字段：
                "minValue": 最小值,
                "maxValue": 最大值,
                # 如果槽位值为具体值，则包含以下字段：
                "value": 具体值,
            },
            ...
        }
                
"""

import logging
from typing import Dict, List, Any, Set

from app.utils.extractor import (
    TimeExtractor,
    AmountExtractor,
    RateExtractor,
    KeywordSlotExtractor
)

logger = logging.getLogger(__name__)

class FinancialSlotExtractorService:
    """
    基于规则的理财槽位抽取服务
    负责从用户输入中提取槽位信息
    """
    
    def __init__(self):
        """初始化槽位抽取服务"""
        self.time_extractor = TimeExtractor()
        self.amount_extractor = AmountExtractor()
        self.rate_extractor = RateExtractor()
        self.keyword_slot_extractor = KeywordSlotExtractor()

    def _convert_position_format(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将position格式转换为startPos和endPos格式"""
        converted_results = []
        for result in results:
            if "position" in result:
                result_copy = result.copy()
                start, end = result_copy.pop("position")
                result_copy["startPos"] = start
                result_copy["endPos"] = end
                converted_results.append(result_copy)
            else:
                converted_results.append(result)
        return converted_results

    def _resolve_overlaps(self, all_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        解决重叠问题，保留最长匹配
        
        Args:
            all_results: 所有抽取器的结果列表
            
        Returns:
            List[Dict[str, Any]]: 处理后的不重叠结果列表
        """
        # 按匹配长度降序排序
        all_results.sort(key=lambda x: len(x["match"]), reverse=True)
        
        covered_positions = set()  # 记录已覆盖的位置
        non_overlapping_results = []
        
        for result in all_results:
            start = result["startPos"]
            end = result["endPos"]
            
            # 检查是否与已选择的结果重叠
            overlap = False
            for pos in range(start, end):
                if pos in covered_positions:
                    overlap = True
                    break
            
            if not overlap:
                # 标记已覆盖的位置
                for pos in range(start, end):
                    covered_positions.add(pos)
                non_overlapping_results.append(result)
        
        return non_overlapping_results

    def extract_slots(self, text: str) -> Dict[str, Dict[str, Any]]:
        """
        从文本中提取槽位信息
        
        Args:
            text: 用户输入的文本
            
        Returns:
            Dict[str, Dict[str, Any]]: 提取的槽位信息
        """
        # 1. 使用各个抽取器提取结果
        time_results = self.time_extractor.extract_time(text)
        amount_results = self.amount_extractor.extract_amount(text)
        rate_results = self.rate_extractor.extract_rate(text)
        keyword_results = self.keyword_slot_extractor.extract(text)
        
        # 2. 合并所有结果并转换position格式
        all_results = []
        all_results.extend(self._convert_position_format(time_results))
        all_results.extend(self._convert_position_format(amount_results))
        all_results.extend(self._convert_position_format(rate_results))
        all_results.extend(self._convert_position_format(keyword_results))
        
        # 3. 处理重叠问题
        non_overlapping_results = self._resolve_overlaps(all_results)
        
        # 4. 将结果转换为最终格式
        final_slots = {}
        for result in non_overlapping_results:
            slot_name = result["slot"]
            result_copy = result.copy()
            del result_copy["slot"]  # 移除slot字段，因为它将作为key
            final_slots[slot_name] = result_copy
            
        logger.debug(f"提取结果: {final_slots}")
        return final_slots
 