"""
应用包初始化文件
"""

import logging
from config import app_config

# 配置日志
logging.basicConfig(
    level=getattr(logging, app_config.LOG_LEVEL),
    format=app_config.LOG_FORMAT,
    datefmt=app_config.LOG_DATE_FORMAT
)

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

def create_app():
    # 创建FastAPI应用
    app = FastAPI(
        title=app_config.TITLE,
        description=app_config.DESCRIPTION,
        version=app_config.VERSION,
        docs_url="/docs",
        redoc_url="/redoc",
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    from app.routes import slot_extractor_router
    app.include_router(slot_extractor_router)
    
    return app 