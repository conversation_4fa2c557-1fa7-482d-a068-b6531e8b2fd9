"""
槽位抽取服务的路由处理模块
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Any, Optional, Tuple

from app.services.slot_extractor_service import FinancialSlotExtractorService
from resource.slot_definitions import SLOTS

router = APIRouter()

# 创建槽位抽取服务实例
slot_extractor = FinancialSlotExtractorService()

class QueryRequest(BaseModel):
    """
    查询请求模型
    """
    query: str

class SlotInfo(BaseModel):
    """
    槽位信息模型
    """
    name: str
    value: Optional[Any] = None
    minValue: Optional[Any] = None
    maxValue: Optional[Any] = None

class SlotMatch(BaseModel):
    """
    槽位匹配结果模型
    """
    keyword: str
    offset: Tuple[int, int]
    slot: SlotInfo

class ExtractResponse(BaseModel):
    """
    抽取响应模型
    """
    slots: List[SlotMatch]
    raw_slots: Dict[str, List[Dict[str, Any]]]

@router.post("/extract", response_model=ExtractResponse)
async def extract_slots(request: QueryRequest) -> ExtractResponse:
    """
    从用户输入中提取槽位信息
    
    Args:
        request (QueryRequest): 包含用户查询的请求对象
        
    Returns:
        ExtractResponse: 包含提取结果的响应对象
    """
    try:
        # 提取槽位
        extraction_results = slot_extractor.extract_slots(request.query)
        
        # 转换为API响应格式
        slot_matches = []
        
        # 遍历所有提取到的槽位
        for slot_name, matches in extraction_results.items():
            for match in matches:
                # 提取匹配信息
                keyword = match.get("match", "")
                position = match.get("position", (0, 0))
                
                # 创建槽位信息
                slot_info = SlotInfo(name=slot_name)
                
                # 处理不同类型的槽位
                if "minValue" in match and "maxValue" in match:
                    # 范围型槽位（如时间、金额、收益率）
                    slot_info.minValue = match.get("minValue")
                    slot_info.maxValue = match.get("maxValue")
                else:
                    # 枚举型槽位
                    slot_info.value = match.get("value")
                
                # 创建匹配结果
                slot_match = SlotMatch(
                    keyword=keyword,
                    offset=position,
                    slot=slot_info
                )
                
                slot_matches.append(slot_match)
        
        # 返回结果
        return ExtractResponse(
            slots=slot_matches,
            raw_slots=extraction_results
        )
        
    except Exception as e:
        print(f"槽位提取失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"槽位提取失败: {str(e)}")