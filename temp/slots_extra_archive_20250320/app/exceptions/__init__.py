# -*- coding: utf-8 -*-

from fastapi import HTTPException
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_500_INTERNAL_SERVER_ERROR

class ServiceException(Exception):
    """服务基础异常类"""
    def __init__(self, message="服务异常", status_code=HTTP_500_INTERNAL_SERVER_ERROR):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)

class BadRequestException(ServiceException):
    """请求参数异常"""
    def __init__(self, message="请求参数错误"):
        super().__init__(message=message, status_code=HTTP_400_BAD_REQUEST)

class ModelLoadException(ServiceException):
    """模型加载异常"""
    def __init__(self, message="模型加载失败"):
        super().__init__(message=message, status_code=HTTP_500_INTERNAL_SERVER_ERROR) 