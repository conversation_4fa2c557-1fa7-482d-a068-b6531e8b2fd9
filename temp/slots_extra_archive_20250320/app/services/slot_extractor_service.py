"""
基于规则的理财槽位抽取服务
"""

import re
from typing import Dict, List, Tuple, Any, Set
import logging

from resource.slot_definitions import SLOTS
from resource.keywords_mapping import COMPILED_KEYWORD_PATTERNS, get_all_slot_keys
from app.utils.time_extractor import TimeExtractor
from app.utils.amount_extractor import AmountExtractor
from app.utils.rate_extractor import RateExtractor

logger = logging.getLogger(__name__)

class FinancialSlotExtractorService:
    """
    基于规则的理财槽位抽取服务
    负责从用户输入中提取槽位信息
    """
    
    def __init__(self):
        """
        初始化槽位抽取服务
        加载槽位定义和关键词映射
        """
        self.slots = SLOTS
        self.keyword_patterns = COMPILED_KEYWORD_PATTERNS
        
        # logger.info("槽位抽取服务初始化完成")
        # logger.info(f"加载了 {len(self.keyword_patterns)} 个槽位的关键词模式")
        # # 调试
        # for slot, values in self.keyword_patterns.items():
        #     total_patterns = sum(len(patterns) for patterns in values.values())
        #     logger.info(f"槽位 '{slot}' 有 {len(values)} 个条件值，共 {total_patterns} 个关键词模式")
    
    def extract_slots(self, text: str) -> Dict[str, Dict[str, Any]]:
        """
        从文本中提取槽位信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            Dict[str, Dict[str, Any]]: 提取的槽位信息
                格式：{
                    槽位名称1: {
                        "match": 匹配到的文本,
                        "slot_value": 槽位值名称,
                        "value": 条件值名称,
                        "startPos": 开始位置,
                        "endPos": 结束位置
                    },
                    槽位名称2: {
                        "match": 匹配到的范围文本,
                        "slot_value": 槽位值名称,
                        "minValue": 最小值,
                        "maxValue": 最大值,
                        "startPos": 开始位置,
                        "endPos": 结束位置
                    },
                }
        """
        # 提取所有槽位（中间处理仍然使用列表格式）
        temp_extracted_slots = {}
        
        # 1. 优先使用专用抽取器提取时间、金额、收益率
        time_results = TimeExtractor.extract_time(text)
        if time_results:
            # 修改时间抽取结果的字段名称
            for result in time_results:
                if "position" in result:
                    start, end = result["position"]
                    result["startPos"] = start
                    result["endPos"] = end
                    del result["position"]
                # slot_value字段已由抽取器提供，无需再添加
            temp_extracted_slots["time"] = time_results
        
        amount_results = AmountExtractor.extract_amount(text)
        if amount_results:
            # 修改金额抽取结果的字段名称
            for result in amount_results:
                if "position" in result:
                    start, end = result["position"]
                    result["startPos"] = start
                    result["endPos"] = end
                    del result["position"]
                # slot_value字段已由抽取器提供，无需再添加
            temp_extracted_slots["number"] = amount_results
        
        rate_results = RateExtractor.extract_rate(text)
        if rate_results:
            # 修改收益率抽取结果的字段名称
            for result in rate_results:
                if "position" in result:
                    start, end = result["position"]
                    result["startPos"] = start
                    result["endPos"] = end
                    del result["position"]
                # slot_value字段已由抽取器提供，无需再添加
            temp_extracted_slots["number"] = rate_results
        
        # 2. 对所有槽位执行一次通用的关键词匹配提取
        # 使用单次提取，基于最大匹配原则自动处理重叠
        all_slot_results = self._extract_slot(text)
        
        # 3. 根据slot字段将结果分组到各自的槽位中
        for result in all_slot_results:
            slot_name = result["slot"]
            # 移除slot字段，保持原有的返回格式
            result_copy = result.copy()
            del result_copy["slot"]
            
            # 修改位置字段名称
            if "position" in result_copy:
                start, end = result_copy["position"]
                result_copy["startPos"] = start
                result_copy["endPos"] = end
                del result_copy["position"]
            
            if slot_name not in temp_extracted_slots:
                temp_extracted_slots[slot_name] = []
            
            temp_extracted_slots[slot_name].append(result_copy)
        
        # 4. 转换为最终的返回格式（每个槽位只保留一个结果）
        final_slots = {}
        for slot_name, matches in temp_extracted_slots.items():
            if not matches:
                continue
                
            # 选择第一个匹配结果（最佳匹配）
            match = matches[0]
            
            # 删除length字段（如果存在）
            if "length" in match:
                del match["length"]
            
            # 添加到最终结果
            final_slots[slot_name] = match
        
        # 打印调试信息
        logger.debug(f"提取结果: {final_slots}")
        
        return final_slots

    def _extract_slot(self, text: str) -> List[Dict[str, Any]]:
        """
        提取文本中的槽位，采用最大匹配原则
        适用于中文环境，首先匹配所有可能的槽位值，然后对重叠部分选择最长的匹配
        
        Args:
            text (str): 待提取的文本
            
        Returns:
            List[Dict[str, Any]]: 槽位匹配信息列表，按匹配长度降序排序
        """
        all_matches = []  # 存储所有匹配结果
        
        # 1. 首先匹配所有可能的槽位值
        for slot, values in self.keyword_patterns.items():
            # 获取槽位名称
            slot_name = self.slots[slot]["name"]
            
            # 遍历该槽位下的所有条件值
            for value_key, patterns in values.items():
                # 获取条件值的name属性
                slot_value = value_key
                value_name = self.slots[slot]["values"][value_key]["name"]
                is_bs_value = self.slots[slot]["values"][value_key].get("is_bs_value", True)
                
                # 遍历该条件值下的所有模式
                for pattern_tuple in patterns:
                    pattern, keyword = pattern_tuple
                    
                    # 使用正则表达式匹配
                    for match in pattern.finditer(text):
                        match_text = match.group()
                        start, end = match.start(), match.end()
                        
                        if is_bs_value:
                            value = value_name
                        else:
                            value = ""

                        # 记录匹配信息
                        match_info = {
                            "match": match_text,
                            "value": value,  # 使用条件值的name而不是键
                            "slot_value": slot_value,  # 添加槽位值字段
                            "slot": slot,
                            "position": (start, end),
                            "length": len(match_text)
                        }
                        all_matches.append(match_info)
        
        # 2. 按匹配长度降序排序
        all_matches.sort(key=lambda x: x["length"], reverse=True)
        
        # 3. 使用贪心算法选择不重叠的最长匹配结果
        covered_positions = set()  # 记录已覆盖的位置
        results = []
        
        for match in all_matches:
            start, end = match["position"]
            
            # 检查是否与已选择的结果重叠
            overlap = False
            for pos in range(start, end):
                if pos in covered_positions:
                    overlap = True
                    break
            
            if overlap:
                continue
            
            # 标记已覆盖的位置
            for pos in range(start, end):
                covered_positions.add(pos)
            
            # 添加到结果中
            results.append({
                "match": match["match"],
                "value": match["value"],
                "slot_value": match["slot_value"],
                "slot": match["slot"],
                "position": match["position"]
            })
        
        return results
 