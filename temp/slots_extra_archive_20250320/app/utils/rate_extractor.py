"""
收益率抽取器
专注于提取金融文本中的收益率值
"""

import re
from typing import Dict, List, Any, Tuple, Optional, Pattern

class RateExtractor:
    """
    收益率抽取器
    专注于从文本中提取收益率值信息（通常为小于10的值或带有百分号的值）
    """
    
    # 收益率范围模式正则表达式
    RANGE_RATE_PATTERNS = [
        # 收益率范围匹配
        (re.compile(r"(\d+(?:\.\d+)?)\s*[~-]\s*(\d+(?:\.\d+)?)\s*%"), "百分比范围"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*[到至]\s*(\d+(?:\.\d+)?)\s*%"), "百分比到至范围"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*[~-]\s*(\d+(?:\.\d+)?)\s*个百分点"), "百分点范围"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*[~-]\s*(\d+(?:\.\d+)?)\s*折"), "折扣率范围"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*%\s*[~-]\s*(\d+(?:\.\d+)?)\s*%"), "百分比范围2"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*%\s*[到至]\s*(\d+(?:\.\d+)?)\s*%"), "百分比到至范围2"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*%以上"), "百分比以上"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*%左右"), "百分比左右"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*%以下"), "百分比以下"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*折以上"), "折扣率以上"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*折以下"), "折扣率以下"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*个点以上"), "点数以上"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*个点以下"), "点数以下")
    ]
    
    # 具体收益率模式正则表达式
    SPECIFIC_RATE_PATTERNS = [
        # 具体收益率匹配
        (re.compile(r"(\d+(?:\.\d+)?)\s*%"), "百分比"),
        (re.compile(r"百分之(\d+(?:\.\d+)?)"), "百分之"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*个百分点"), "百分点"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*折"), "折扣率"),
        (re.compile(r"年化\s*(\d+(?:\.\d+)?)"), "年化"),
        (re.compile(r"年收益率\s*(\d+(?:\.\d+)?)"), "年收益率"),
        (re.compile(r"利率\s*(\d+(?:\.\d+)?)"), "利率"),
        (re.compile(r"收益率\s*(\d+(?:\.\d+)?)"), "收益率"),
        (re.compile(r"收益\s*(\d+(?:\.\d+)?)"), "收益")
    ]
    
    # 收益率相关关键词，用于判断上下文
    RATE_CONTEXT_KEYWORDS = [
        "收益率", "利率", "回报率", "回报", "收益", "利息", "收入率",
        "年化", "月化", "日化", "季化", "七日年化", "预期收益"
    ]
    
    @classmethod
    def extract_rate(cls, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取收益率信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的收益率信息列表
        """
        # 提取收益率信息
        extracted_values = []
        covered_positions = set()  # 记录已覆盖的位置
        
        # 优先处理收益率范围模式
        for pattern, _ in cls.RANGE_RATE_PATTERNS:
            # 查找所有匹配
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.start(), match.end()
                
                # 检查是否与已覆盖的位置重叠
                overlap = False
                for pos in range(start, end):
                    if pos in covered_positions:
                        overlap = True
                        break
                
                if overlap:
                    continue
                
                # 尝试提取范围值
                try:
                    # 创建匹配信息
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "slot_value": "rate_range"
                    }
                    
                    # 处理百分比范围
                    percent_range = re.search(r"(\d+(?:\.\d+)?)\s*[~-到至]\s*(\d+(?:\.\d+)?)\s*%", match_text)
                    if percent_range:
                        min_value, max_value = cls._calculate_range_value(match_text, percent_range.group(1), percent_range.group(2))
                        match_info["minValue"] = round(min_value, 4)  # 保留4位小数
                        match_info["maxValue"] = round(max_value, 4)  # 保留4位小数
                    else:
                        # 处理"以上"、"以下"、"左右"等模式
                        percent_above = re.search(r"(\d+(?:\.\d+)?)\s*%以上", match_text)
                        if percent_above:
                            value = float(percent_above.group(1)) / 100
                            match_info["minValue"] = round(value, 4)  # 保留4位小数
                            match_info["maxValue"] = None
                        
                        percent_below = re.search(r"(\d+(?:\.\d+)?)\s*%以下", match_text)
                        if percent_below:
                            value = float(percent_below.group(1)) / 100
                            match_info["minValue"] = 0
                            match_info["maxValue"] = round(value, 4)  # 保留4位小数
                            
                        percent_around = re.search(r"(\d+(?:\.\d+)?)\s*%左右", match_text)
                        if percent_around:
                            value = float(percent_around.group(1)) / 100
                            match_info["minValue"] = round(value * 0.9, 4)  # 左右范围取上下10%，保留4位小数
                            match_info["maxValue"] = round(value * 1.1, 4)  # 保留4位小数
                            
                        # 处理折扣率
                        discount_above = re.search(r"(\d+(?:\.\d+)?)\s*折以上", match_text)
                        if discount_above:
                            value = float(discount_above.group(1)) / 10
                            match_info["minValue"] = round(value, 4)  # 保留4位小数
                            match_info["maxValue"] = None
                            
                        discount_below = re.search(r"(\d+(?:\.\d+)?)\s*折以下", match_text)
                        if discount_below:
                            value = float(discount_below.group(1)) / 10
                            match_info["minValue"] = 0
                            match_info["maxValue"] = round(value, 4)  # 保留4位小数
                            
                        # 处理百分点
                        point_above = re.search(r"(\d+(?:\.\d+)?)\s*个点以上", match_text)
                        if point_above:
                            value = float(point_above.group(1)) / 100
                            match_info["minValue"] = round(value, 4)  # 保留4位小数
                            match_info["maxValue"] = None
                            
                        point_below = re.search(r"(\d+(?:\.\d+)?)\s*个点以下", match_text)
                        if point_below:
                            value = float(point_below.group(1)) / 100
                            match_info["minValue"] = 0
                            match_info["maxValue"] = round(value, 4)  # 保留4位小数
                    
                    # 标记已覆盖的位置
                    for pos in range(start, end):
                        covered_positions.add(pos)
                        
                    extracted_values.append(match_info)
                except Exception:
                    # 解析失败，跳过该匹配
                    continue
        
        # 处理具体收益率模式
        for pattern, pattern_name in cls.SPECIFIC_RATE_PATTERNS:
            # 查找所有匹配
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.start(), match.end()
                
                # 检查是否已被覆盖
                overlap = False
                for pos in range(start, end):
                    if pos in covered_positions:
                        overlap = True
                        break
                
                if overlap:
                    continue
                    
                try:
                    # 提取数值并转换为浮点数
                    value = 0.0
                    
                    if pattern_name == "百分比":
                        # 直接从模式中提取百分比
                        num_match = re.search(r"(\d+(?:\.\d+)?)", match_text)
                        if num_match:
                            value = float(num_match.group(1)) / 100
                    elif pattern_name == "百分之":
                        # 从"百分之X"中提取数值
                        value = float(match.group(1)) / 100
                    elif pattern_name == "百分点":
                        # 从"X个百分点"中提取数值
                        num_match = re.search(r"(\d+(?:\.\d+)?)", match_text)
                        if num_match:
                            value = float(num_match.group(1)) / 100
                    elif pattern_name == "折扣率":
                        # 从"X折"中提取数值
                        num_match = re.search(r"(\d+(?:\.\d+)?)", match_text)
                        if num_match:
                            value = float(num_match.group(1)) / 10
                    else:
                        # 提取"年化X"等模式中的数值
                        num_match = re.search(r"(\d+(?:\.\d+)?)", match_text)
                        if num_match:
                            value_text = num_match.group(1)
                            value = float(value_text)
                            # 如果数值大于1且不在百分比模式中，可能是百分比值，需要除以100
                            if value > 1 and "%" not in match_text:
                                value = value / 100
                    
                    # 创建匹配信息
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "value": round(value, 4),  # 保留4位小数
                        "slot_value": "specific_rate"
                    }
                    
                    # 标记已覆盖的位置
                    for pos in range(start, end):
                        covered_positions.add(pos)
                        
                    extracted_values.append(match_info)
                except Exception:
                    # 解析失败，跳过该匹配
                    continue
                    
        # 处理满足收益率特征的小数值（小于10的数值）
        cls._extract_numeric_rates(text, covered_positions, extracted_values)
        
        return extracted_values
    
    @classmethod
    def _extract_numeric_rates(cls, text: str, covered_positions: set, extracted_values: List[Dict[str, Any]]) -> None:
        """
        提取文本中的数值型收益率（不带百分号等显式标记的小数值）
        
        Args:
            text (str): 标准化后的文本
            covered_positions (set): 已覆盖的位置集合
            extracted_values (List[Dict[str, Any]]): 提取结果列表，供添加提取的值
        """
        # 匹配满足收益率特征的小数值（小于10的数值）
        for match in re.finditer(r"(\d+(?:\.\d+)?)", text):
            match_text = match.group()
            start, end = match.start(), match.end()
            
            # 检查是否已被覆盖
            overlap = False
            for pos in range(start, end):
                if pos in covered_positions:
                    overlap = True
                    break
            
            if overlap:
                continue
                
            # 判断是否是收益率（收益率通常为小于10的小数值）
            try:
                value = float(match_text)
                # 如果数值太大，可能不是收益率
                if value >= 10:
                    continue
                    
                # 检查上下文是否包含收益率相关的关键词
                # 提取前后10个字符作为上下文
                context_start = max(0, start - 10)
                context_end = min(len(text), end + 10)
                context = text[context_start:context_end]
                
                # 判断上下文是否包含收益率相关词
                if not cls._is_rate_context(context):
                    continue
                
                # 创建匹配信息，原始数值可能是百分比格式，需要转换为小数
                if value > 0 and value < 1:
                    rate_value = value  # 已经是小数形式
                else:
                    rate_value = value / 100  # 转换为小数形式
                
                match_info = {
                    "match": match_text,
                    "position": (start, end),
                    "value": round(rate_value, 4),  # 保留4位小数
                    "slot_value": "specific_rate"
                }
                
                # 标记已覆盖的位置
                for pos in range(start, end):
                    covered_positions.add(pos)
                    
                extracted_values.append(match_info)
            except Exception:
                # 解析失败，跳过
                continue
    
    @classmethod
    def _calculate_range_value(cls, match_text: str, min_value: str, max_value: str = None) -> Tuple[float, Optional[float]]:
        """
        计算范围值
        
        Args:
            match_text (str): 匹配的文本
            min_value (str): 最小值文本
            max_value (str, optional): 最大值文本，默认为None
            
        Returns:
            Tuple[float, Optional[float]]: 转换后的最小值和最大值
        """
        # 将最小值文本转换为浮点数
        min_val = float(re.sub(r'[^\d.]', '', min_value))
        
        # 如果匹配文本中包含百分号，将数值除以100转换为小数
        if "%" in match_text and "%" not in min_value:
            min_val = min_val / 100
            
        # 如果匹配文本中包含"折"，将数值除以10转换为小数
        if "折" in match_text:
            min_val = min_val / 10
            
        # 如果没有最大值，则返回(min_val, None)
        if not max_value:
            return (min_val, None)
        
        # 计算最大值
        max_val = float(re.sub(r'[^\d.]', '', max_value))
        
        # 如果匹配文本中包含百分号，将数值除以100转换为小数
        if "%" in match_text and "%" not in max_value:
            max_val = max_val / 100
            
        # 如果匹配文本中包含"折"，将数值除以10转换为小数
        if "折" in match_text:
            max_val = max_val / 10
        
        return (min_val, max_val)
    
    @classmethod
    def _is_rate_context(cls, context: str) -> bool:
        """
        判断上下文是否与收益率相关
        
        Args:
            context (str): 上下文文本
            
        Returns:
            bool: 是否为收益率相关上下文
        """
        for keyword in cls.RATE_CONTEXT_KEYWORDS:
            if keyword in context:
                return True
                
        return False 