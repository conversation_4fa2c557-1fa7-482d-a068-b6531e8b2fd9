"""
时间抽取工具类
负责从文本中提取时间相关的信息
"""

import re
from typing import Dict, List, Any, Pattern, Tuple

class TimeExtractor:
    """时间抽取工具类"""
    
    # 一年的天数和一个月的天数
    DAYS_PER_YEAR = 366  # 一年按366天计算
    DAYS_PER_MONTH = 30  # 一个月按30天计算
    
    # 中文数字映射
    CN_NUM = {
        '一': 1, '二': 2, '两': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '零': 0, '0': 0, '1': 1, '2': 2, '3': 3, '4': 4, 
        '5': 5, '6': 6, '7': 7, '8': 8, '9': 9
    }
    
    # 时间范围模式正则表达式
    RANGE_TIME_PATTERNS = [
        # 数字范围表达式
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})\s*[天月年]\s*[~\-到至]\s*(\d+|[一二两三四五六七八九十]{1,3})\s*[天月年]"), "时间范围"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})\s*[到至]\s*(\d+|[一二两三四五六七八九十]{1,3})\s*[天月年]"), "时间范围"),
        
        # 特定时间范围表达
        (re.compile(r"最近一周"), "最近一周"),
        (re.compile(r"最近\s*(\d+|[一二两三四五六七八九十])\s*天"), "最近天数"),
        (re.compile(r"最近\s*(\d+|[一二两三四五六七八九十])\s*个月"), "最近月数"),
        (re.compile(r"最近(一|1)个月"), "最近一个月"),
        (re.compile(r"[一二两三1-3]到[两三四五2-5]个月"), "几个月范围"),
        (re.compile(r"半年(左右)?"), "半年左右"),
        (re.compile(r"[一1]年(左右)?"), "一年左右"),
        (re.compile(r"[一1]年以上"), "一年以上"),
        (re.compile(r"[两2]年(左右)?"), "两年左右"),
        (re.compile(r"[三3]到[五5]年"), "三到五年"),
        
        # 数字+单位+修饰词
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})年以上"), "多年以上"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})个月以上"), "多月以上"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})天以上"), "多天以上"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})年以内"), "多年以内"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})个月以内"), "多月以内"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})天以内"), "多天以内"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})年以下"), "多年以下"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})个月以下"), "多月以下"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})天以下"), "多天以下"),
        (re.compile(r"超过(\d+|[一二两三四五六七八九十]{1,3})年"), "超过多年"),
        (re.compile(r"超过(\d+|[一二两三四五六七八九十]{1,3})个月"), "超过多月"),
        (re.compile(r"超过(\d+|[一二两三四五六七八九十]{1,3})天"), "超过多天"),
        (re.compile(r"不到(\d+|[一二两三四五六七八九十]{1,3})年"), "不到多年"),
        (re.compile(r"不到(\d+|[一二两三四五六七八九十]{1,3})个月"), "不到多月"),
        (re.compile(r"不到(\d+|[一二两三四五六七八九十]{1,3})天"), "不到多天")
    ]
    
    # 具体时间模式正则表达式
    SPECIFIC_TIME_PATTERNS = [
        # 简单天数表达式
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})\s*天"), "天数"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})\s*日"), "日数"),
        
        # 月数表达式
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})\s*个月"), "月数"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})\s*月(?!薪)"), "月数简写"),  # 排除"月薪"
        
        # 年数表达式
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})\s*年"), "年数"),
        (re.compile(r"(\d+|[一二两三四五六七八九十]{1,3})\s*周年"), "周年数"),
        
        # 特定时间表达
        (re.compile(r"半年"), "半年"),
        (re.compile(r"[一1]年"), "一年"),
        (re.compile(r"[两2]年"), "两年"),
        (re.compile(r"[三3]年"), "三年"),
        (re.compile(r"[四4]年"), "四年"),
        (re.compile(r"[五5]年"), "五年"),
        (re.compile(r"[十10]年"), "十年"),
        
        # 模糊时间表达
        (re.compile(r"[几数]天"), "几天"),
        (re.compile(r"[几数]个月"), "几个月"),
        (re.compile(r"[几数]年"), "几年"),
        
        # 近似表达
        (re.compile(r"大约(\d+|[一二两三四五六七八九十]{1,3})天"), "大约天数"),
        (re.compile(r"大约(\d+|[一二两三四五六七八九十]{1,3})个月"), "大约月数"),
        (re.compile(r"大约(\d+|[一二两三四五六七八九十]{1,3})年"), "大约年数"),
        
        # 复合中文数字表达式
        (re.compile(r"十[一二三四五六七八九]天"), "十几天"),
        (re.compile(r"二十[一二三四五六七八九]?天"), "二十几天"),
        (re.compile(r"三十[一二三四五六七八九]?天"), "三十几天"),
        (re.compile(r"十[一二三四五六七八九]个月"), "十几个月"),
        (re.compile(r"二十[一二三四五六七八九]?个月"), "二十几个月")
    ]
    
    @staticmethod
    def extract_time(text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取时间信息
        
        Args:
            text (str): 待提取的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的时间信息列表
        """
        # 优先处理范围类型，再处理具体类型
        results = []
        covered_positions = set()  # 记录已覆盖的位置
        
        # 先处理时间范围
        for pattern, _ in TimeExtractor.RANGE_TIME_PATTERNS:
            # 查找所有匹配
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.start(), match.end()
                
                # 检查是否与已覆盖的位置重叠
                overlap = False
                for pos in range(start, end):
                    if pos in covered_positions:
                        overlap = True
                        break
                
                if overlap:
                    continue
                
                try:
                    # 计算时间范围的天数
                    min_days, max_days = TimeExtractor._calculate_range_days(match_text)
                    
                    # 创建匹配信息
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "minValue": min_days,
                        "maxValue": max_days,
                        "slot_value": "time_range"
                    }
                    
                    # 标记已覆盖的位置
                    for pos in range(start, end):
                        covered_positions.add(pos)
                        
                    results.append(match_info)
                except ValueError:
                    # 解析失败，跳过该匹配
                    continue
        
        # 再处理具体时间
        for pattern, _ in TimeExtractor.SPECIFIC_TIME_PATTERNS:
            # 查找所有匹配
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.start(), match.end()
                
                # 检查是否与已覆盖的位置重叠
                overlap = False
                for pos in range(start, end):
                    if pos in covered_positions:
                        overlap = True
                        break
                
                if overlap:
                    continue
                
                try:
                    # 计算具体时间的天数
                    days = TimeExtractor._calculate_days(match_text)
                    
                    # 创建匹配信息
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "value": days,
                        "slot_value": "specific_time"
                    }
                    
                    # 标记已覆盖的位置
                    for pos in range(start, end):
                        covered_positions.add(pos)
                        
                    results.append(match_info)
                except ValueError:
                    # 解析失败，跳过该匹配
                    continue
                    
        return results
    
    @staticmethod
    def _parse_chinese_num(text: str) -> int:
        """
        解析中文数字表达式，处理复合数字（如十一、二十三等）
        
        Args:
            text (str): 中文数字文本
            
        Returns:
            int: 解析后的整数
        """
        if not text:
            return 0
            
        # 如果已经是数字，直接返回
        if text.isdigit():
            return int(text)
            
        # 处理特殊情况
        if text == '十':
            return 10
            
        # 处理十X (十一到十九)
        if len(text) == 2 and text[0] == '十':
            return 10 + TimeExtractor.CN_NUM.get(text[1], 0)
            
        # 处理X十 (二十到九十)
        if len(text) == 2 and text[1] == '十':
            return TimeExtractor.CN_NUM.get(text[0], 0) * 10
            
        # 处理X十Y (二十一到九十九)
        if len(text) == 3 and text[1] == '十':
            return TimeExtractor.CN_NUM.get(text[0], 0) * 10 + TimeExtractor.CN_NUM.get(text[2], 0)
            
        # 处理单个中文数字
        if text in TimeExtractor.CN_NUM:
            return TimeExtractor.CN_NUM[text]
            
        # 对于不规则的情况，尝试逐字解析
        total = 0
        for char in text:
            if char in TimeExtractor.CN_NUM:
                total = total * 10 + TimeExtractor.CN_NUM[char]
                
        return total if total > 0 else 0
    
    @staticmethod
    def _extract_numbers(text: str) -> List[int]:
        """
        从文本中提取数字（阿拉伯数字或中文数字）
        
        Args:
            text (str): 包含数字的文本
            
        Returns:
            List[int]: 提取的数字列表
        """
        # 提取阿拉伯数字
        arabic_nums = re.findall(r'\d+', text)
        
        # 提取中文数字复合表达式
        cn_complex_patterns = [
            r'十[一二三四五六七八九]',
            r'[一二三四五六七八九]?十[一二三四五六七八九]?',
            r'[二三四五六七八九]十[一二三四五六七八九]?',
            r'[一二两三四五六七八九十]'
        ]
        
        cn_nums = []
        for pattern in cn_complex_patterns:
            cn_matches = re.findall(pattern, text)
            if cn_matches:
                for match in cn_matches:
                    # 确保匹配结果不被重复处理
                    if not any(match in existing for existing in cn_nums):
                        cn_nums.append(match)
        
        # 将所有数字转换为整数
        numbers = [int(num) for num in arabic_nums]
        
        for cn_num in cn_nums:
            parsed = TimeExtractor._parse_chinese_num(cn_num)
            if parsed > 0 and parsed not in numbers:
                numbers.append(parsed)
                
        return numbers
    
    @staticmethod
    def _calculate_days(text: str) -> int:
        """
        计算具体时间表达转换为天数
        
        Args:
            text (str): 时间表达文本
            
        Returns:
            int: 对应的天数
        """
        try:
            # 处理特定的时间表达
            if "半年" in text:
                return TimeExtractor.DAYS_PER_MONTH * 6
                
            if "一年" in text or "1年" in text:
                return TimeExtractor.DAYS_PER_YEAR
                
            if "两年" in text or "2年" in text:
                return TimeExtractor.DAYS_PER_YEAR * 2
                
            if "三年" in text or "3年" in text:
                return TimeExtractor.DAYS_PER_YEAR * 3
                
            if "四年" in text or "4年" in text:
                return TimeExtractor.DAYS_PER_YEAR * 4
                
            if "五年" in text or "5年" in text:
                return TimeExtractor.DAYS_PER_YEAR * 5
                
            if "十年" in text or "10年" in text:
                return TimeExtractor.DAYS_PER_YEAR * 10
                
            if "几天" in text or "数天" in text:
                return 3  # 几天默认按3天计算
                
            if "几个月" in text or "数个月" in text:
                return 3 * TimeExtractor.DAYS_PER_MONTH  # 几个月默认按3个月计算
                
            if "几年" in text or "数年" in text:
                return 3 * TimeExtractor.DAYS_PER_YEAR  # 几年默认按3年计算
            
            # 提取数字
            numbers = TimeExtractor._extract_numbers(text)
            
            if numbers:
                num = numbers[0]  # 取第一个数字
                
                # 根据单位计算天数
                if "天" in text or "日" in text:
                    return num
                elif "月" in text:
                    return num * TimeExtractor.DAYS_PER_MONTH
                elif "年" in text:
                    return num * TimeExtractor.DAYS_PER_YEAR
            
            # 处理特殊的"大约"表达
            if "大约" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    num = numbers[0]
                    if "天" in text:
                        return num
                    elif "月" in text:
                        return num * TimeExtractor.DAYS_PER_MONTH
                    elif "年" in text:
                        return num * TimeExtractor.DAYS_PER_YEAR
            
            raise ValueError(f"无法识别的时间表达: {text}")
        except Exception as e:
            raise ValueError(f"计算天数失败: {str(e)}")
    
    @staticmethod
    def _calculate_range_days(text: str) -> tuple:
        """
        计算时间范围表达转换为天数范围
        
        Args:
            text (str): 时间范围表达文本
            
        Returns:
            tuple: (最小天数, 最大天数)
        """
        try:
            # 处理特殊表达
            if "最近一周" in text:
                return 0, 7
                
            if "最近" in text and "天" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0]
                    
            if "最近" in text and "个月" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0] * TimeExtractor.DAYS_PER_MONTH
                    
            if "最近一个月" in text or "最近1个月" in text:
                return 0, TimeExtractor.DAYS_PER_MONTH
                
            if "一到两个月" in text or "1到2个月" in text:
                return TimeExtractor.DAYS_PER_MONTH, 2 * TimeExtractor.DAYS_PER_MONTH
            
            # 处理中文数字范围
            cn_months_range_match = re.search(r"([一二两三1-3])到([两三四五2-5])个月", text)
            if cn_months_range_match:
                min_text = cn_months_range_match.group(1)
                max_text = cn_months_range_match.group(2)
                
                min_months = TimeExtractor._parse_chinese_num(min_text)
                max_months = TimeExtractor._parse_chinese_num(max_text)
                
                return min_months * TimeExtractor.DAYS_PER_MONTH, max_months * TimeExtractor.DAYS_PER_MONTH
                
            if "半年左右" in text or "半年" in text:
                return 5 * TimeExtractor.DAYS_PER_MONTH, 7 * TimeExtractor.DAYS_PER_MONTH
                
            if "一年左右" in text or "1年左右" in text:
                return 10 * TimeExtractor.DAYS_PER_MONTH, 14 * TimeExtractor.DAYS_PER_MONTH
                
            if "两年左右" in text or "2年左右" in text:
                return 22 * TimeExtractor.DAYS_PER_MONTH, 26 * TimeExtractor.DAYS_PER_MONTH
                
            if "一年以上" in text or "1年以上" in text:
                return TimeExtractor.DAYS_PER_YEAR, None
                
            if "三到五年" in text or "3到5年" in text:
                return 3 * TimeExtractor.DAYS_PER_YEAR, 5 * TimeExtractor.DAYS_PER_YEAR
            
            # 处理常规范围表达 X到Y天/月/年
            numbers = TimeExtractor._extract_numbers(text)
            
            if len(numbers) >= 2:
                min_num, max_num = numbers[0], numbers[1]
                
                if "天" in text and "到" in text:
                    return min_num, max_num
                    
                if "月" in text and "到" in text:
                    return min_num * TimeExtractor.DAYS_PER_MONTH, max_num * TimeExtractor.DAYS_PER_MONTH
                    
                if "年" in text and "到" in text:
                    return min_num * TimeExtractor.DAYS_PER_YEAR, max_num * TimeExtractor.DAYS_PER_YEAR
            
            # 处理"以上"、"以内"、"以下"格式
            if "年以上" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return numbers[0] * TimeExtractor.DAYS_PER_YEAR, None
                    
            if "个月以上" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return numbers[0] * TimeExtractor.DAYS_PER_MONTH, None
                    
            if "天以上" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return numbers[0], None
            
            if "年以内" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0] * TimeExtractor.DAYS_PER_YEAR
                    
            if "个月以内" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0] * TimeExtractor.DAYS_PER_MONTH
                    
            if "天以内" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0]
                    
            if "年以下" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0] * TimeExtractor.DAYS_PER_YEAR
                    
            if "个月以下" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0] * TimeExtractor.DAYS_PER_MONTH
                    
            if "天以下" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0]
            
            # 处理"超过"、"不到"格式
            if "超过" in text and "年" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return numbers[0] * TimeExtractor.DAYS_PER_YEAR, None
                    
            if "超过" in text and "个月" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return numbers[0] * TimeExtractor.DAYS_PER_MONTH, None
                    
            if "超过" in text and "天" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return numbers[0], None
                    
            if "不到" in text and "年" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0] * TimeExtractor.DAYS_PER_YEAR - 1
                    
            if "不到" in text and "个月" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0] * TimeExtractor.DAYS_PER_MONTH - 1
                    
            if "不到" in text and "天" in text:
                numbers = TimeExtractor._extract_numbers(text)
                if numbers:
                    return 0, numbers[0] - 1
            
            raise ValueError(f"无法识别的时间范围表达: {text}")
        except Exception as e:
            raise ValueError(f"计算时间范围失败: {str(e)}") 