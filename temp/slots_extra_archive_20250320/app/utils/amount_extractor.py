"""
金额抽取器
专注于提取金融文本中的金额值
"""

import re
from typing import Dict, List, Any, Optional, Tuple, Pattern

class AmountExtractor:
    """
    金额抽取器
    专注于从文本中提取金额值信息（通常为元、万元等带有货币单位的值）
    """
    
    # 金额范围模式正则表达式
    RANGE_AMOUNT_PATTERNS = [
        # 金额范围匹配
        (re.compile(r"(\d+(?:\.\d+)?)\s*[万千]?[元块]\s*[~-]\s*(\d+(?:\.\d+)?)\s*[万千]?[元块]"), "金额范围"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*[万千]?[元块]\s*[到至]\s*(\d+(?:\.\d+)?)\s*[万千]?[元块]"), "金额范围到至"),
        (re.compile(r"(\d+)\s*万以上"), "万元以上"),
        (re.compile(r"(\d+)\s*元以上"), "元以上"),
        (re.compile(r"(\d+)\s*块以上"), "块以上"),
        (re.compile(r"(\d+)\s*万以下"), "万元以下"),
        (re.compile(r"(\d+)\s*元以下"), "元以下"),
        (re.compile(r"(\d+)\s*块以下"), "块以下"),
        (re.compile(r"几万块"), "几万块"),
        (re.compile(r"几千块"), "几千块"),
        (re.compile(r"上万元"), "上万元"),
        (re.compile(r"大额"), "大额"),
        (re.compile(r"小额"), "小额")
    ]
    
    # 具体金额模式正则表达式
    SPECIFIC_AMOUNT_PATTERNS = [
        # 具体金额匹配
        (re.compile(r"(\d+(?:\.\d+)?)\s*[万千]?元"), "元"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*[万千]?块"), "块"),
        (re.compile(r"(\d+(?:\.\d+)?)\s*[万千]?人民币"), "人民币"),
        (re.compile(r"一万"), "一万"),
        (re.compile(r"两万"), "两万"),
        (re.compile(r"五万"), "五万"),
        (re.compile(r"十万"), "十万"),
        (re.compile(r"百万"), "百万"),
        (re.compile(r"千万"), "千万"),
        (re.compile(r"一千"), "一千"),
        (re.compile(r"两千"), "两千"),
        (re.compile(r"五千"), "五千")
    ]
    
    # 中文金额表达对应的数值映射
    CHINESE_AMOUNT_MAP = {
        "一万": 10000,
        "两万": 20000,
        "五万": 50000,
        "十万": 100000,
        "百万": 1000000,
        "千万": 10000000,
        "一千": 1000,
        "两千": 2000,
        "五千": 5000
    }
    
    @classmethod
    def extract_amount(cls, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取金额信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的金额信息列表
        """
        # 提取金额信息
        extracted_values = []
        covered_positions = set()  # 记录已覆盖的位置
        
        # 优先处理金额范围模式
        for pattern, _ in cls.RANGE_AMOUNT_PATTERNS:
            # 查找所有匹配
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.start(), match.end()
                
                # 检查是否与已覆盖的位置重叠
                overlap = False
                for pos in range(start, end):
                    if pos in covered_positions:
                        overlap = True
                        break
                
                if overlap:
                    continue
                
                # 尝试提取范围值
                try:
                    # 创建匹配信息
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "slot_value": "amount_range"
                    }
                    
                    # 处理金额范围
                    amount_range = re.search(r"(\d+(?:\.\d+)?)\s*[万千]?[元块]\s*[~-到至]\s*(\d+(?:\.\d+)?)\s*[万千]?[元块]", match_text)
                    if amount_range:
                        min_value, max_value = cls._calculate_range_value(match_text, amount_range.group(1), amount_range.group(2))
                        match_info["minValue"] = min_value
                        match_info["maxValue"] = max_value
                    else:
                        # 处理"以上"、"以下"等模式
                        amount_above = re.search(r"(\d+(?:\.\d+)?)\s*万以上", match_text)
                        if amount_above:
                            value = float(amount_above.group(1)) * 10000  # 万元转换为元
                            match_info["minValue"] = value
                            match_info["maxValue"] = None
                        
                        yuan_above = re.search(r"(\d+(?:\.\d+)?)\s*元以上", match_text)
                        if yuan_above:
                            value = float(yuan_above.group(1))
                            match_info["minValue"] = value
                            match_info["maxValue"] = None
                            
                        kuai_above = re.search(r"(\d+(?:\.\d+)?)\s*块以上", match_text)
                        if kuai_above:
                            value = float(kuai_above.group(1))
                            match_info["minValue"] = value
                            match_info["maxValue"] = None
                            
                        amount_below = re.search(r"(\d+(?:\.\d+)?)\s*万以下", match_text)
                        if amount_below:
                            value = float(amount_below.group(1)) * 10000  # 万元转换为元
                            match_info["minValue"] = 0
                            match_info["maxValue"] = value
                            
                        yuan_below = re.search(r"(\d+(?:\.\d+)?)\s*元以下", match_text)
                        if yuan_below:
                            value = float(yuan_below.group(1))
                            match_info["minValue"] = 0
                            match_info["maxValue"] = value
                            
                        kuai_below = re.search(r"(\d+(?:\.\d+)?)\s*块以下", match_text)
                        if kuai_below:
                            value = float(kuai_below.group(1))
                            match_info["minValue"] = 0
                            match_info["maxValue"] = value
                            
                        # 处理特殊表达
                        if "几万块" in match_text:
                            match_info["minValue"] = 10000
                            match_info["maxValue"] = 100000
                            
                        if "几千块" in match_text:
                            match_info["minValue"] = 1000
                            match_info["maxValue"] = 10000
                            
                        if "上万元" in match_text:
                            match_info["minValue"] = 10000
                            match_info["maxValue"] = None
                            
                        if "大额" in match_text:
                            match_info["minValue"] = 50000
                            match_info["maxValue"] = None
                            
                        if "小额" in match_text:
                            match_info["minValue"] = 0
                            match_info["maxValue"] = 10000
                    
                    # 标记已覆盖的位置
                    for pos in range(start, end):
                        covered_positions.add(pos)
                        
                    extracted_values.append(match_info)
                except Exception:
                    # 解析失败，跳过该匹配
                    continue
        
        # 处理具体金额模式
        for pattern, _ in cls.SPECIFIC_AMOUNT_PATTERNS:
            # 查找所有匹配
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.start(), match.end()
                
                # 检查是否已被覆盖
                overlap = False
                for pos in range(start, end):
                    if pos in covered_positions:
                        overlap = True
                        break
                
                if overlap:
                    continue
                    
                try:
                    # 计算金额值（单位为元）
                    if match_text in cls.CHINESE_AMOUNT_MAP:
                        amount = cls.CHINESE_AMOUNT_MAP[match_text]
                    else:
                        amount = cls._calculate_amount(match_text)
                    
                    # 创建匹配信息
                    match_info = {
                        "match": match_text,
                        "position": (start, end),
                        "value": amount,  # 具体金额使用value字段
                        "slot_value": "specific_amount"
                    }
                    
                    # 标记已覆盖的位置
                    for pos in range(start, end):
                        covered_positions.add(pos)
                        
                    extracted_values.append(match_info)
                except Exception:
                    # 解析失败，跳过该匹配
                    continue
        
        # 过滤掉非金额相关的数值（通常金额值较大或包含货币单位）
        filtered_values = []
        for value in extracted_values:
            has_currency_unit = any(unit in value["match"] for unit in ["元", "块", "万", "千", "人民币"])
            has_large_value = False
            
            if "value" in value and value["value"] > 10:
                has_large_value = True
            elif "minValue" in value and value["minValue"] > 10:
                has_large_value = True
            elif "maxValue" in value and value["maxValue"] is not None and value["maxValue"] > 10:
                has_large_value = True
                
            if has_currency_unit or has_large_value:
                filtered_values.append(value)
        
        return filtered_values
    
    @classmethod
    def _calculate_range_value(cls, match_text: str, min_value: str, max_value: str) -> Tuple[float, float]:
        """
        计算金额范围值
        
        Args:
            match_text (str): 匹配的文本
            min_value (str): 最小值文本
            max_value (str): 最大值文本
            
        Returns:
            Tuple[float, float]: 转换后的最小值和最大值（单位为元）
        """
        # 处理最小值
        min_val = float(re.sub(r'[^\d.]', '', min_value))
        
        # 判断单位
        if "万" in min_value:
            min_val = min_val * 10000  # 万元转换为元
        elif "千" in min_value:
            min_val = min_val * 1000  # 千元转换为元
            
        # 处理最大值
        max_val = float(re.sub(r'[^\d.]', '', max_value))
        
        # 判断单位
        if "万" in max_value:
            max_val = max_val * 10000  # 万元转换为元
        elif "千" in max_value:
            max_val = max_val * 1000  # 千元转换为元
            
        return (min_val, max_val)
    
    @staticmethod
    def _calculate_amount(text: str) -> float:
        """
        将金额文本转换为数值（单位为元）
        
        Args:
            text (str): 金额文本
            
        Returns:
            float: 转换后的金额值（单位为元）
        """
        # 提取数字部分
        num_match = re.search(r"(\d+(?:\.\d+)?)", text)
        if not num_match:
            return 0.0
            
        amount = float(num_match.group(1))
        
        # 判断单位
        if "万" in text:
            amount = amount * 10000  # 万元转换为元
        elif "千" in text:
            amount = amount * 1000  # 千元转换为元
            
        return amount 