"""
槽位抽取服务测试脚本
"""

import json
import sys
from typing import Dict, Any

# 导入槽位抽取服务
sys.path.append('.')  # 确保可以导入app模块
from app.services.slot_extractor_service import FinancialSlotExtractorService

def run_test(text: str) -> Dict[str, Dict[str, Any]]:
    """
    运行槽位抽取测试
    
    Args:
        text (str): 要测试的文本
        
    Returns:
        Dict[str, Dict[str, Any]]: 抽取的槽位结果
    """
    # 创建槽位抽取服务实例
    extractor = FinancialSlotExtractorService()
    
    # 提取槽位
    results = extractor.extract_slots(text)
    
    return results

def print_results(text: str, results: Dict[str, Dict[str, Any]]) -> None:
    """
    打印测试结果
    
    Args:
        text (str): 测试文本
        results (Dict[str, Dict[str, Any]]): 抽取结果
    """
    print("="*80)
    print(f"测试文本: \"{text}\"")
    print("-"*80)
    
    if not results:
        print("未抽取到任何槽位信息")
        return
    
    # 直接打印JSON格式的结果
    print(json.dumps(results, ensure_ascii=False, indent=2))
    print("="*80)

def main():
    """主函数，运行测试案例"""
    
    # 测试案例列表
    test_cases = [
        # 风险等级测试
        "我想要一个低风险的理财产品",
        "请推荐R1级别的产品",
        "有没有中等风险的理财",
        "有R5级别的产品吗",
        
        # 收益需求测试
        "年化收益率高一点的产品",
        "有没有每日正收益的理财",
        "我想要每月都有收益的产品",
        "有没有收益稳定的理财产品",
        
        # 产品名称测试
        "我想了解一下周周宝",
        "定期宝怎么样",
        "价值+的产品性价比如何",
        "有全球+这种产品吗",
        
        # 投资策略测试
        "我想做一些活钱管理",
        "有没有稳健增值的理财产品",
        "进取投资类的产品有哪些",
        
        # 混合测试
        "我想要一个低风险、每日有正收益的产品",
        "有没有中等风险、收益稳定的周周宝",
        "推荐一个R1级别的定期宝",
        "我想要年化收益率高、活钱管理类的产品",
        
        # 金额、时间、收益率测试
        "有没有10万起投、期限3个月、收益率5%以上的产品",
        "想投5000块钱，半年期的，年化收益4%左右",
        "一年期、低风险、最少投1万的产品有哪些",
        
        # 特殊标签测试
        "有没有支持定投的产品",
        "赎回到账快的理财产品有哪些",
        "最新发售的产品推荐一下"
    ]
    
    # 运行所有测试案例
    for text in test_cases:
        results = run_test(text)
        print_results(text, results)
        print()  # 空行分隔

if __name__ == "__main__":
    main()
