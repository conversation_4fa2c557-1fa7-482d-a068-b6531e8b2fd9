### 槽位

1. **时间** -> `time`
   - 用于表示明确的时间点或时间段
   - 条件值
      - 起始天数 ～ 结束天数
         - 关键词样例：匹配具体时间范围，并将自然语言描述时间转为具体单位天
      - 具体时间
         - 关键词样例：匹配具体时间，并将自然语言描述时间转为具体单位天

2. **数值** -> `amount`
   - 用于表示产品的投资金额或产品的起购金额、或数值（一般为收益率）
   - 条件值
      - 最小金额 ～ 最大金额
         - 关键词样例：匹配具体金额范围，并将自然语言描述金额转为具体单位元
      - 具体金额
         - 关键词样例：匹配具体金额，并将自然语言描述金额转为具体单位元
      - 最小数值 ～ 最大数值
         - 关键词样例：匹配具体数值范围，并将自然语言描述数值转为具体单位
      - 具体数值
         - 关键词样例：匹配具体数值，并将自然语言描述数值转为具体单位

3. **风险等级** -> `risk_level`
   - 用于表示产品的风险等级
   - 条件值
      - R1
         - 关键词样例：低风险、保本、保本保息、保本浮动收益、低波动、低回撤
      - R2
         - 关键词样例：中低风险、中低波动、中低回撤
      - R3
         - 关键词样例：中风险、中波动、中回撤
      - R4
         - 关键词样例：中高风险、中高波动、中高回撤
      - R5
         - 关键词样例：高风险、高波动、高回撤

4. **收益需求** -> `return_demand`
    - 用于表示收益的需求描述
    - 条件值
      - 年华收益率
         - 关键词样例：收益率、利息、年化收益率、年化收益、年化收益排名靠前的、年化收益高的、年化收益排名靠前的
      - 每日正收益
         - 关键词样例：收益稳定、收益表现稳定、每天都有收益、收益没有亏损过、正收益、每天正收益、天天有利息
      - 每周正收益
         - 关键词样例：每周正收益、每周不亏损、每周都有钱赚、每周有利息
      - 每月正收益
         - 关键词样例：每月都有收益、每月都有正收益、每月收益不亏损、每月都有钱赚
      - 每季度正收益
         - 关键词样例：每季都有收益、每季都有正收益、每季收益不亏损、每月都有钱赚
      - 每年正收益
         - 关键词样例：每年都有收益、每年都有正收益、每年收益不亏损、每年都有钱赚
      - 每期正收益
         - 关键词样例：每期都有收益、每期都有正收益、每期收益不亏损、每期都有钱赚

5. **产品名称** -> `specific_product`
    - 用于表示具体的产品名称或类型
    - 条件值
      - 周周宝
         - 关键词样例：周周宝
      - 月月宝
         - 关键词样例：月月宝
      - 季季宝
         - 关键词样例：季季宝
      - 半年宝
         - 关键词样例：半年宝
      - 多月宝
         - 关键词样例：多月宝
      - 定期宝
         - 关键词样例：定期宝、定期理财
      - 价值+
         - 关键词样例：价值＋、价值投资型理财
      - 多元+
         - 关键词样例：多元+
      - 量化+
         - 关键词样例：量化+、适度参与量化投资的理财
      - 红利+
         - 关键词样例：红利＋、高股利策略、红利型策略、可以获取公司分红的理财
      - 黄金+
         - 关键词样例：黄金+、投资于黄金资产的理财
      - 全球+
         - 关键词样例：全球＋、配置全球资产、全球优质股债资产的理财
      - 结构+
         - 关键词样例：结构＋、结构资产

6. **产品形态** -> `product_type`
    - 用于表示产品的运作方式
    - 条件值
      - 封闭式
         - 关键词样例：自动到期
      - 定期开放
      - 最短持有期

7. **起购金额** -> `start_amount`
    - 用于表示产品的起购金额
    - 条件值
      - 1000元以下：小额
      - 1000元-5000元：中额
      - 5000元以上：大额

8. **募集方式** -> `raise_type`
    - 用于表示产品的募集方式
    - 条件值
      - 公募
         - 关键词样例：公募理财
      - 私募
         - 关键词样例：私募理财

9. **投资策略** -> `investment_strategy`
    - 用于表示产品的投资策略
    - 条件值
      - 活钱管理
         - 关键词样例：活钱理财、现金理财、活期存款、T+0到账、随时要用、当做零钱花但还有收益、边用边存、随时赎回、灵活取用、T+1到账、每天都能赎回、流动性很高、几天、灵活、时间短一点、既有资金灵活性且收益更高一些、次日到账、次日可赎回
      - 稳健低波
         - 关键词样例：稳健低波动理财、安心理财
      - 稳健增值
         - 关键词样例：稳当增值的理财
      - 稳中求进
         - 关键词样例：稳中求进的理财
      - 进取投资
         - 关键词样例：不怕风险收益越高越好

10. **投资方向** -> `investment_direction`
    - 用于表示产品的投资标的
    - 条件值
      - 固定收益
         - 关键词样例：债券资产、国债、收益稳定的资产、高流动性的资产
      - 权益类
         - 关键词样例：股票占比、投资股市部分、优先股、获取市场收益的资产
      - 商品及金融衍生品类资产
         - 关键词样例：大宗商品、期货、期权、金融衍生品
      - 货币类
         - 关键词样例：现金、银行存款、货币基金
      - 外币类
         - 关键词样例：美元理财、美元存款、美元基金、港元、澳元、欧元、英镑、加元、日元、新元

11. **费用需求** -> `fee_requirement`
    - 用于表示产品的费用相关要求
    - 例如：管理费、申购费、赎回费

12. **其他标签** -> `special_label`
    - 用于表示产品的特殊标签
    - 条件值
      - 高人气
      - 历史回撤小
      - 早一天享收益
      - 抗跌表现好
         - 关键词样例：很抗跌
      - 长盈理财
      - 锁定票息
      - 固收+公募REITS
      - 固收+结构化
      - 绝对收益
      - 固收+高股息
      - 期限错配
      - 稳定分红
      - 长钱优选
      - 精选资产
      - 目标止盈
      - 固收+优先股
      - 量化中性
      - 低波固收＋
      - 增值优选
      - 可转债
      - 私行尊享
      - 赎回到账快
      - 此系列超十万人持有
      - 此系列超万人复购
      - 支持定投
      - 近一周热搜
      - 最新发售
      - 近一周超万人浏览
      - 往期收益均达基准
      - 往期收益均超基准
      - 收益连续达基准5期
      - 收益连续超基准5期

@resource 需要再构建一套映射逻辑，维护一个映射能力脚本，将槽位与具体的业务条件码进行映射。
比如：special_label槽位可以直接映射到SPC_TAG，槽位条件值对应SPC_TAG的条件值。类似的还有：
investment_direction：INV_TYP
investment_strategy: STT_TAG
raise_type: CLC_MTH
start_amount: STR_AMT
product_type: PRD_FRM
specific_product的特殊一点，xx宝映射到BBL_TYP，xx+映射到YJ_TAG
qualitative_return中的条件值是XX正收益的映射到POS_TAG，而条件值是年华收益率的如果同时没有时间槽位，那么就映射到YLD_EXP，如果时间槽位是7天以内、1月内、3月内、一年内、近三年内、则分别映射到LAST_WEK_ANL_YLD、LAST_1_MON_ANL_YLD、LAST_3_MON_ANL_YLD、LAST_1_YEAR_ANL_YLD、LAST_3_YEAR_ANL_YLD、
risk_level：RSK_LV。
还有一些槽位到业务条件未梳理到，后续再继续整理。请你先调查整个项目是干嘛的，然后再合理的使用上述信息构建一个映射关系功能代码。

请再次优化脚本，确保功能逻辑符合最佳实践，可部分衔接正确。
我们项目整体功能是，router的api接收一个用户的query输入，使用app/services/slot_extractor_service.py进行问题的槽位抽取与槽位条件值匹配，基本原理是直接通过keywords_mapping的正则或者关键词匹配用户问题中的关键词，而关键词对应到槽位的条件值，从而也对应到槽位。然后我们再根据槽位到业务条件码的一些映射规则，用户问题中涉及到的槽位进行条件码映射，可能有直接的槽位与条件码映射关系，也可能有槽位的条件值组合映射到某个条件码。
最终我们通过router接口返回的结果也应该修改为条件码的形式去代替