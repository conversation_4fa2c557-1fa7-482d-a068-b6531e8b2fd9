"""
槽位抽取服务API测试脚本
"""

import json
import requests
from typing import Dict, Any, List

# API服务地址
API_URL = "http://localhost:8000/extract"  # 根据实际部署地址修改

def test_api(text: str) -> Dict[str, Any]:
    """
    测试槽位抽取API
    
    Args:
        text (str): 要测试的文本
        
    Returns:
        Dict[str, Any]: API响应结果
    """
    # 构造请求数据
    payload = {
        "query": text
    }
    
    # 发送POST请求
    try:
        response = requests.post(API_URL, json=payload)
        response.raise_for_status()  # 如果请求失败，抛出异常
        
        # 解析响应结果
        result = response.json()
        return result
    except requests.exceptions.RequestException as e:
        print(f"API请求错误: {e}")
        return {"error": str(e)}

def print_api_results(text: str, results: Dict[str, Any]) -> None:
    """
    打印API测试结果
    
    Args:
        text (str): 测试文本
        results (Dict[str, Any]): API响应结果
    """
    print("="*80)
    print(f"API测试文本: \"{text}\"")
    print("-"*80)
    
    if "error" in results:
        print(f"API错误: {results['error']}")
        return
    
    if not results:
        print("API未返回任何槽位信息")
        return
    
    # 美化输出
    print(json.dumps(results, ensure_ascii=False, indent=2))
    
    print("="*80)

def main():
    """主函数，运行API测试案例"""
    
    # 测试案例列表
    test_cases = [
        # 风险等级测试
        "我想要一个低风险的理财产品",
        "请推荐R1级别的产品",
        
        # 收益需求测试
        "年化收益率高一点的产品",
        "有没有每日正收益的理财",
        
        # 产品名称测试
        "我想了解一下周周宝",
        "价值+的产品性价比如何",
        
        # 混合测试
        "我想要一个低风险、每日有正收益的产品",
        "有没有10万起投、期限3个月、收益率5%以上的产品"
    ]
    
    # 运行所有测试案例
    for text in test_cases:
        results = test_api(text)
        print_api_results(text, results)
        print()  # 空行分隔

if __name__ == "__main__":
    main() 