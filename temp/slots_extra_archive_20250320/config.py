# -*- coding: utf-8 -*-

# Gunicorn 配置
bind = "0.0.0.0:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
timeout = 120
keepalive = 5

# 应用配置
class Config:
    # 基础配置
    DEBUG = False
    TITLE = "金融槽位提取服务"
    VERSION = "1.0.0"
    DESCRIPTION = "基于规则和机器学习的金融领域槽位提取微服务"
    
    # 模型配置
    CUSTOM_DICT_PATH = "financial_terms.txt"
    MODEL_PATH = "financial_slot_models.pkl"
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

class DevelopmentConfig(Config):
    DEBUG = True
    LOG_LEVEL = "DEBUG"

class ProductionConfig(Config):
    pass

# 根据环境变量选择配置
import os
env = os.environ.get("ENVIRONMENT", "development")
if env == "production":
    app_config = ProductionConfig
else:
    app_config = DevelopmentConfig 

# 服务器配置
SERVER_CONFIG = {
    "host": os.getenv("HOST", "0.0.0.0"),
    "port": int(os.getenv("PORT", 8001)),  # 修改为8001
    "debug": bool(os.getenv("DEBUG", False)),
    "reload": bool(os.getenv("RELOAD", True))
} 