"""
服务启动管理器
用于启动和管理FastAPI服务
"""

import uvicorn
import logging
from typing import Optional, Dict, Any
import os
from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="理财槽位抽取服务",
    description="基于规则的理财槽位抽取服务，用于从用户输入中提取理财相关的槽位信息",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
from app.routes import slot_extractor_router
app.include_router(
    slot_extractor_router,
    prefix="/api/v1/slots",
    tags=["slots"]
)

@app.get("/")
async def root() -> Dict[str, Any]:
    """
    根路径处理函数
    """
    return {
        "message": "欢迎使用理财槽位抽取服务",
        "version": "1.0.0"
    }

def start_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    reload: bool = True,
    workers: Optional[int] = None
) -> None:
    """
    启动FastAPI服务
    
    Args:
        host (str): 服务主机地址
        port (int): 服务端口
        reload (bool): 是否启用热重载
        workers (Optional[int]): 工作进程数
    """
    try:
        # 获取环境变量中的配置
        host = os.getenv("HOST", host)
        port = int(os.getenv("PORT", port))
        reload = os.getenv("RELOAD", "true").lower() == "true"
        workers = int(os.getenv("WORKERS", workers or 1))
        
        logger.info(f"正在启动服务... 主机: {host}, 端口: {port}")
        
        # 启动服务
        uvicorn.run(
            "manager:app",  # 修改为本文件中的app对象
            host=host,
            port=port,
            reload=reload,
            workers=workers
        )
        
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        raise

if __name__ == "__main__":
    # 直接运行此文件时启动服务
    start_server() 