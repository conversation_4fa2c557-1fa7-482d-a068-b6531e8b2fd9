"""
时间抽取器

专注于提取金融文本中的时间信息，抽取两类时间范围：
1. 相对时间范围：如"最近七天"、"过去三到六个月"、"今年以来"等
2. 持续时间范围：如"一年半"、"90天到180天"、"三年左右"等

特别说明：
1. 一年按366天计算，一个月按30天计算
2. 相对时间范围的默认锚点是当前时间，除非有明确的时间锚点，当前时间锚点的天数默认为0，过去时间锚点的天数为负数，未来时间锚点的天数为正数。
3. 持续时间是指具体的时间跨度，如果有最小最大时间跨度描述，则最小最大值为对应的描述转天数，如果只有单个时间跨度描述，则最小最大值为该描述转天数。

模式设计：

- 相对时间修饰词：相对某个时间锚点的描述，默认相对当前时间
    - 相对过去：
        prefix: [过去|最近|前|上|近|以来|至今|截至|截止|这|本|今|当前|現在|目前|此]
        suffix: [前|以前|之前|为止|以来]
        infix: [到|至|~|-|、|和|与|或|及]
        compound: [prefix] + 时间单位 + [infix] + [suffix]，如"过去三天以前"、"今年以来"
    - 相对未来：
        prefix: [接下来|未来|将来|即将|今后|往后]
        suffix: [后|以后|之后]
        infix: [到|至|~|-|、|和|与|或|及]
        compound: [prefix] + 时间单位 + [infix] + [suffix]，如"接下来三天之后"
    - 特殊时间表达：
        - 年维度: [去年|前年|今年|本年|年初|年中|年末|年底|上半年|下半年]
        - 季维度: [上季度|上个季度|上一季度|本季度|这个季度|这一季度|季初|季中|季末|季度末]
        - 月维度: [上月|上个月|上一个月|本月|这个月|这一个月|月初|月中|月底|月末]
        - 周维度: [上周|上个周|上星期|上个星期|本周|这周|这个星期]
        - 财年表达: [本财年|本財年|今年财年|今年財年]
        - 特殊时间点: [迄今为止|截止目前|至今|前几天|前幾天]

- 时间范围修饰词：对时间跨度范围的修饰
    - 上限：[不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|以下|以内|之内|内|未满]
    - 下限：[以上|之上|开外|起|最少|至少|不少于|不少於|多于|多於|起码]
    - 近似：[约|大约|大概|差不多|将近|接近|几乎|幾乎|左右|上下|多|出头|来]
    - 复合：<上限><近似><下限>

- 时间跨度描述词：描述时间跨度的单位或数值
    - 基本单位：[天|日|来天|多天|几天|月|个月|個月|几月|来月|多个月|几个月|年|周年|来年|多年|几年|周|星期|礼拜|週|禮拜|来周|多周|几周|季度|季|个季度|個季度]
    - 数量描述：[一二三四五六七八九十百千万萬亿億兩壹贰叁肆伍陆柒捌玖拾佰仟]|[0-9]+]
    - 复合时间表达：["三年零6个月"|"一千二百五六天"]等

- 时间锚点描述词：描述具体或模糊的时间锚点
    - 年份标记：[今年|去年|前年|明年|后年|[0-9]{4}年]
    - 月份标记：[[一二三四五六七八九十]{1,2}月份|[1-9]月|1[0-2]月|本月|上个月|上個月]
    - 季度标记：[第[一二三四]季度|[一二三四]季度|本季度|上个季度|上個季度|本财年|本財年|今年财年|今年財年]
    - 周标记：[本周|上周|上週|上个星期|上個星期|星期[一二三四五六日天]|周[一二三四五六日天]|禮拜[一二三四五六日天]]
    - 日标记：[今天|昨天|前天|明天|后天|大前天|大后天|[1-9]号|[1-2][0-9]号|3[0-1]号|[1-9]日|[1-2][0-9]日|3[0-1]日]

处理原则：

1、相对时间 + 时间锚点 > 相对时间 + 时间跨度 > 时间锚点 > 时间跨度 + 时间范围 > 时间范围
2、组合时间表达式 > 单一时间表达式
3、长文本 > 短文本
4、原则1 > 原则2 > 原则3

保证提供一个TimeExtractor类，以及一个extract_time方法，该方法接收一个字符串，返回一个列表。

输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'relative_time_range' 或 'duration_time_range'
        "minValue": float,       # 范围最小值（天数），可能为 None，但不与maxValue同时为None
        "maxValue": float,       # 范围最大值（天数），可能为 None，但不与minValue同时为None
    }
"""


import re
from typing import List, Dict, Any, Tuple, Optional, Pattern, Union, Set
from datetime import datetime
import logging
from functools import lru_cache

logger = logging.getLogger(__name__)

class TimeConstants:
    """时间相关常量定义"""
    DAYS_IN_DAY = 1
    DAYS_IN_WEEK = 7
    DAYS_IN_MONTH = 30
    DAYS_IN_QUARTER = 90
    DAYS_IN_YEAR = 365
    
    # 模糊修饰词的范围调整比例
    FUZZY_MODIFIER_RANGE = 0.05
    
    # 模糊表达的默认范围
    DEFAULT_FEW_DAYS = (2, 5)
    DEFAULT_FEW_WEEKS = (2 * DAYS_IN_WEEK, 4 * DAYS_IN_WEEK)
    DEFAULT_FEW_MONTHS = (2 * DAYS_IN_MONTH, 4 * DAYS_IN_MONTH)


class PatternPriority:
    """模式优先级定义"""
    EXACT_MATCH = 1       # 精确匹配，如"去年"、"上个月"
    SPECIFIC_RANGE = 2    # 特定范围，如"过去三到六个月"
    MODIFIED_RANGE = 3    # 带修饰的范围，如"大约三个月"、"三个月左右"
    RELATIVE_RANGE = 4    # 相对范围，如"最近一周"、"过去三天"
    BOUNDED_RANGE = 5     # 有界范围，如"三个月以上"、"一年以内"
    BASIC_UNIT = 6        # 基本单位表达，如"三天"、"一个月"
    DEFAULT = 99          # 默认优先级，最低


class TimePattern:
    """时间模式定义，包含正则模式和优先级"""
    def __init__(self, pattern: str, priority: int, pattern_type: str = ""):
        self.pattern = pattern
        self.compiled = re.compile(pattern)
        self.priority = priority
        self.type = pattern_type
    
    def match(self, text: str) -> List[re.Match]:
        """查找所有匹配"""
        return list(self.compiled.finditer(text))


class TimeExtractor:
    def __init__(self):
        # 初始化时间常量
        self.constants = TimeConstants()
        
        # 基础数字和单位模式
        self._init_base_patterns()
        
        # 初始化各类时间模式
        self._init_relative_patterns()  # 相对时间
        self._init_duration_patterns()  # 持续时间
        self._init_special_cases()      # 特殊情况

        # 中文数字到阿拉伯数字的映射
        self._init_cn_num_mapping()

    def _init_base_patterns(self):
        """初始化基础模式"""
        # 数字模式
        self.digit_pattern = r'\d+'
        self.cn_num_pattern = r'[一两二三四五六七八九十百千万亿壹贰叁肆伍陆柒捌玖拾佰仟萬億]+'
        self.num_pattern = fr'({self.digit_pattern}|{self.cn_num_pattern})'
        
        # 时间单位，使用非捕获组和词边界提高精度
        self.day_unit = r'(?:天|日|号|夜|来天|多天|几天)'
        self.month_unit = r'(?:月|个月|個月|几月|来月|多个月|几个月)'
        self.year_unit = r'(?:年|周年|来年|多年|几年)'
        self.week_unit = r'(?:周|星期|礼拜|週|禮拜|来周|多周|几周)'
        self.quarter_unit = r'(?:季度|季|个季度|個季度|季节|季節)'
        
        # 统一时间单位模式
        self.time_unit = fr'(?:{self.day_unit}|{self.month_unit}|{self.year_unit}|{self.week_unit}|{self.quarter_unit})'
        
        # 修饰词重新组织
        # 1. 模糊修饰词，可以出现在时间前或后
        self.fuzzy_pre_modifiers = r'(?:约|大约|大概|差不多|将近|接近|几乎|幾乎)'  # 前置
        self.fuzzy_post_modifiers = r'(?:左右|上下|多|出头)'  # 后置
        
        # 2. 上限修饰词，分前置和后置
        self.pre_upper_limit = r'(?:不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多)'  # 前置
        self.post_upper_limit = r'(?:以下|以内|之内|内|以前|之前|前)'  # 后置
        
        # 3. 下限修饰词，分前置和后置
        self.pre_lower_limit = r'(?:最少|至少|不少于|不少於|多于|多於|起码)'  # 前置
        self.post_lower_limit = r'(?:以上|之上|开外|以后|以後|之后|之後|起|开始)'  # 后置
        
        # 4. 相对时间前缀（合并相对当前和相对过去）
        self.past_time_prefix = r'(?:过去|最近|前|上|近|之前|以前|这|本|今|当前|現在|目前|此)'
        
        # 5. 特殊相对时间表达
        self.special_relative = r'(?:以来|迄今|至今|截至|截止|为止)'
        
        # 6. 特殊时间表达的模板化定义
        # 时间指示词
        self.year_indicators = r'(?:去年|前年|今年|本年)'
        self.quarter_indicators = r'(?:上季度|上个季度|上一季度|本季度|这个季度|这一季度)'
        self.month_indicators = r'(?:上月|上个月|上一个月|本月|这个月|这一个月)'
        self.week_indicators = r'(?:上周|上个周|上星期|上个星期|本周|这周|这个星期)'
        
        # 时间锚点
        self.year_anchors = r'(?:年初|年中|年末|年底|上半年|下半年)'
        self.quarter_anchors = r'(?:季初|季中|季末|季度末)'
        self.month_anchors = r'(?:月初|月中|月底|月末)'
        
        # 财年表达
        self.fiscal_year = r'(?:本财年|本財年|今年财年|今年財年)'
        
        # 特殊时间点
        self.special_time_points = r'(?:迄今为止|截止目前|至今|前几天|前幾天)'

    def _init_relative_patterns(self):
        """初始化相对时间模式"""
        # 带优先级的相对时间模式
        self.relative_patterns = []
        
        # 1. 特定时间词 - 最高优先级
        specific_times = [
            r'上个月|上個月', r'上周|上週|上个星期|上個星期', r'昨天|昨日', r'前天|前日', 
            r'上半年', r'去年', r'前年', r'刚刚|剛剛|方才', r'不久前', r'刚才|剛才',
            r'近期|近日|近几天|近幾天', r'上个季度|上個季度', r'不日|近日',
            r'年初|年末|年底', r'月初|月末|月底', r'季初|季末|季度末',
            r'上半年|下半年', r'今年上半年|今年下半年', r'去年上半年|去年下半年',
            r'前几天|前幾天', r'上个星期[一二三四五六日天]|上週[一二三四五六日天]',
            r'本季度|这个季度|本季|這個季度', r'本财年|本財年|今年财年|今年財年'
        ]
        
        for pattern in specific_times:
            self.relative_patterns.append(
                TimePattern(pattern, PatternPriority.EXACT_MATCH, "relative_time_range")
            )
        
        # 2. 精确相对范围 - 次高优先级
        specific_ranges = [
            fr'{self.past_time_prefix}{self.num_pattern}到{self.num_pattern}{self.day_unit}',
            fr'{self.past_time_prefix}{self.num_pattern}到{self.num_pattern}{self.month_unit}',
            fr'{self.past_time_prefix}{self.num_pattern}到{self.num_pattern}{self.week_unit}',
            fr'{self.past_time_prefix}{self.num_pattern}到{self.num_pattern}{self.year_unit}',
            r'今年\d{1,2}月份?'
        ]
        
        for pattern in specific_ranges:
            self.relative_patterns.append(
                TimePattern(pattern, PatternPriority.SPECIFIC_RANGE, "relative_time_range")
            )
        
        # 3. 常见相对时间表达 - 普通优先级
        common_relative = [
            fr'{self.past_time_prefix}{self.num_pattern}?{self.day_unit}',
            fr'{self.past_time_prefix}{self.num_pattern}?{self.month_unit}',
            fr'{self.past_time_prefix}{self.num_pattern}?{self.week_unit}',
            fr'{self.past_time_prefix}{self.num_pattern}?{self.year_unit}',
            fr'{self.current_time_prefix}{self.day_unit}',
            fr'{self.current_time_prefix}{self.month_unit}',
            fr'{self.current_time_prefix}{self.week_unit}',
            fr'{self.current_time_prefix}{self.year_unit}'
        ]
        
        for pattern in common_relative:
            self.relative_patterns.append(
                TimePattern(pattern, PatternPriority.RELATIVE_RANGE, "relative_time_range")
            )

        # 添加新的相对时间模式
        special_relative_patterns = [
            fr'{self.current_time_prefix}?年{self.special_relative}',
            fr'{self.current_time_prefix}?月{self.special_relative}',
            fr'{self.current_time_prefix}?季度{self.special_relative}',
            fr'{self.special_relative}',  # 单独的"以来"、"至今"等
        ]
        
        for pattern in special_relative_patterns:
            self.relative_patterns.append(
                TimePattern(pattern, PatternPriority.RELATIVE_RANGE, "relative_time_range")
            )

    def _init_duration_patterns(self):
        """初始化持续时间模式"""
        self.duration_patterns = []
        
        # 1. 特殊持续时间表达 - 高优先级
        special_durations = [
            r'半年', r'半个月|半個月', r'一个半月|一個半月', r'两个半月|兩個半月',
            r'一年半|兩年半|两年半', r'不到一年|不到半年|不到一个月|不到半个月',
            r'几个月|幾個月', r'几天|幾天', r'几周|幾周|几个星期|幾個星期'
        ]
        
        for pattern in special_durations:
            self.duration_patterns.append(
                TimePattern(pattern, PatternPriority.EXACT_MATCH, "duration_time_range")
            )
        
        # 2. 精确范围表达 - 次高优先级
        range_patterns = [
            fr'{self.num_pattern}到{self.num_pattern}{self.day_unit}',
            fr'{self.num_pattern}到{self.num_pattern}{self.month_unit}',
            fr'{self.num_pattern}到{self.num_pattern}{self.week_unit}',
            fr'{self.num_pattern}到{self.num_pattern}{self.year_unit}',
            fr'{self.num_pattern}到{self.num_pattern}{self.quarter_unit}',
            fr'{self.num_pattern}[-~至]{self.num_pattern}{self.day_unit}',
            fr'{self.num_pattern}[-~至]{self.num_pattern}{self.month_unit}',
            fr'{self.num_pattern}[-~至]{self.num_pattern}{self.week_unit}',
            fr'{self.num_pattern}[-~至]{self.num_pattern}{self.year_unit}',
            fr'{self.num_pattern}[-~至]{self.num_pattern}{self.quarter_unit}'
        ]
        
        for pattern in range_patterns:
            self.duration_patterns.append(
                TimePattern(pattern, PatternPriority.SPECIFIC_RANGE, "duration_time_range")
            )
        
        # 3. 基本时间单位模式
        basic_unit_patterns = [
            fr'{self.num_pattern}{self.day_unit}',
            fr'{self.num_pattern}{self.month_unit}',
            fr'{self.num_pattern}{self.week_unit}',
            fr'{self.num_pattern}{self.year_unit}',
            fr'{self.num_pattern}{self.quarter_unit}'
        ]
        
        for pattern in basic_unit_patterns:
            self.duration_patterns.append(
                TimePattern(pattern, PatternPriority.BASIC_UNIT, "duration_time_range")
            )
        
        # 4. 前置模糊修饰的持续时间
        for pattern in basic_unit_patterns:
            self.duration_patterns.append(
                TimePattern(
                    fr'{self.fuzzy_pre_modifiers}{pattern}',
                    PatternPriority.MODIFIED_RANGE,
                    "duration_time_range"
                )
            )
        
        # 5. 后置模糊修饰的持续时间
        for pattern in basic_unit_patterns:
            self.duration_patterns.append(
                TimePattern(
                    fr'{pattern}{self.fuzzy_post_modifiers}',
                    PatternPriority.MODIFIED_RANGE,
                    "duration_time_range"
                )
            )
        
        # 6. 前置上限修饰的持续时间
        for pattern in basic_unit_patterns:
            self.duration_patterns.append(
                TimePattern(
                    fr'{self.pre_upper_limit}{pattern}',
                    PatternPriority.BOUNDED_RANGE,
                    "duration_time_range"
                )
            )
        
        # 7. 后置上限修饰的持续时间
        for pattern in basic_unit_patterns:
            self.duration_patterns.append(
                TimePattern(
                    fr'{pattern}{self.post_upper_limit}',
                    PatternPriority.BOUNDED_RANGE,
                    "duration_time_range"
                )
            )
        
        # 8. 前置下限修饰的持续时间
        for pattern in basic_unit_patterns:
            self.duration_patterns.append(
                TimePattern(
                    fr'{self.pre_lower_limit}{pattern}',
                    PatternPriority.BOUNDED_RANGE,
                    "duration_time_range"
                )
            )
        
        # 9. 后置下限修饰的持续时间
        for pattern in basic_unit_patterns:
            self.duration_patterns.append(
                TimePattern(
                    fr'{pattern}{self.post_lower_limit}',
                    PatternPriority.BOUNDED_RANGE,
                    "duration_time_range"
                )
            )

    def _init_special_cases(self):
        """初始化特殊情况处理"""
        # 日期模式 - 用于排除错误匹配
        self.date_patterns = [
            r'\d+[号日][-~至到]\d+[号日]',
            r'\d+月\d+[号日]',
            r'[这本上下]?[个個]?[周週星期礼拜禮拜][一二三四五六日天]',
            r'\d+月(?:初|中旬|下旬|末)'
        ]
        self.compiled_date_patterns = [re.compile(p) for p in self.date_patterns]
        
        # 特殊情况映射 - 直接给出值
        self.special_cases = {
            '半年': (self.constants.DAYS_IN_MONTH * 6, self.constants.DAYS_IN_MONTH * 6),
            '半个月': (self.constants.DAYS_IN_MONTH / 2, self.constants.DAYS_IN_MONTH / 2),
            '半個月': (self.constants.DAYS_IN_MONTH / 2, self.constants.DAYS_IN_MONTH / 2),
            '一个半月': (self.constants.DAYS_IN_MONTH * 1.5, self.constants.DAYS_IN_MONTH * 1.5),
            '一個半月': (self.constants.DAYS_IN_MONTH * 1.5, self.constants.DAYS_IN_MONTH * 1.5),
            '两个半月': (self.constants.DAYS_IN_MONTH * 2.5, self.constants.DAYS_IN_MONTH * 2.5),
            '兩個半月': (self.constants.DAYS_IN_MONTH * 2.5, self.constants.DAYS_IN_MONTH * 2.5),
            '一年半': (self.constants.DAYS_IN_YEAR + self.constants.DAYS_IN_MONTH * 6, 
                     self.constants.DAYS_IN_YEAR + self.constants.DAYS_IN_MONTH * 6),
            '两年半': (self.constants.DAYS_IN_YEAR * 2 + self.constants.DAYS_IN_MONTH * 6, 
                     self.constants.DAYS_IN_YEAR * 2 + self.constants.DAYS_IN_MONTH * 6),
            '兩年半': (self.constants.DAYS_IN_YEAR * 2 + self.constants.DAYS_IN_MONTH * 6, 
                     self.constants.DAYS_IN_YEAR * 2 + self.constants.DAYS_IN_MONTH * 6),
            # 相对时间特殊情况
            '上个月': (-self.constants.DAYS_IN_MONTH, 0),
            '上個月': (-self.constants.DAYS_IN_MONTH, 0),
            '上周': (-self.constants.DAYS_IN_WEEK, 0),
            '上週': (-self.constants.DAYS_IN_WEEK, 0),
            '上个星期': (-self.constants.DAYS_IN_WEEK, 0),
            '上個星期': (-self.constants.DAYS_IN_WEEK, 0),
            '昨天': (-1, 0),
            '昨日': (-1, 0),
            '前天': (-2, 0),
            '前日': (-2, 0),
            '刚刚': (-0.1, 0),
            '剛剛': (-0.1, 0),
            '方才': (-0.1, 0),
            '刚才': (-0.5, 0),
            '剛才': (-0.5, 0),
            '近期': (-self.constants.DAYS_IN_WEEK * 2, 0),
            '近日': (-self.constants.DAYS_IN_WEEK, 0),
            '近几天': (-5, 0),
            '近幾天': (-5, 0),
            '前几天': (-5, 0),
            '前幾天': (-5, 0),
        }
        
        # 扩充特殊情况映射
        self.special_cases.update({
            # 添加更多特殊情况
            "今年以来": (-datetime.now().timetuple().tm_yday, 0),  # 从年初到现在
            "至今": (-999999, 0),  # 表示一个很长的时间段
            "迄今": (-999999, 0),
            "迄今为止": (-999999, 0),
            "截至目前": (-999999, 0),
            "几天": (2, 5),
            "几周": (14, 28),
            "几个月": (60, 120),
        })

    def _init_cn_num_mapping(self):
        """初始化中文数字映射"""
        self.cn_num = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '百': 100, '千': 1000, '万': 10000, '亿': 100000000,
            '两': 2, '兩': 2,
            # 繁体数字
            '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5,
            '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10,
            '佰': 100, '仟': 1000, '萬': 10000, '億': 100000000
        }

    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取时间信息的主方法"""
        # 查找所有匹配
        matches = self._find_all_matches(text)
        
        # 过滤掉日期表达式，避免误识别
        matches = [match for match in matches 
                  if not self._is_date_not_duration(match['match'])]
        
        # 解决重叠和冲突
        matches = self._resolve_overlaps(matches)
        
        # 计算时间值
        result = []
        for match in matches:
            if match['slot_value'] == 'relative_time_range':
                min_value, max_value = self._calculate_relative_time_value(match['match'])
            else:
                min_value, max_value = self._calculate_duration_time_value(match['match'])
                
            min_value, max_value = self._normalize_values(min_value, max_value)
            
            result.append({
                'match': match['match'],
                'position': match['position'],
                'slot': 'time',
                'slot_value': match['slot_value'],
                'minValue': min_value,
                'maxValue': max_value
            })
            
        return result

    def _find_all_matches(self, text: str) -> List[Dict[str, Any]]:
        """查找所有匹配的时间表达式"""
        matches = []
        
        # 组合所有模式
        all_patterns = self.relative_patterns + self.duration_patterns
        
        # 查找所有匹配
        for pattern_obj in all_patterns:
            for match in pattern_obj.match(text):
                matched_text = match.group()
                if matched_text:  # 确保不匹配空字符串
                    matches.append({
                        'match': matched_text,
                        'position': (match.start(), match.end()),
                        'priority': pattern_obj.priority,
                        'slot_value': pattern_obj.type
                    })
        
        return matches

    def _resolve_overlaps(self, matches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解决重叠匹配，采用更保守的策略"""
        if not matches:
            return []
        
        # 按优先级和长度排序
        matches.sort(key=lambda x: (x.get('priority', PatternPriority.DEFAULT), 
                                    -(x['position'][1] - x['position'][0])))
        
        result = []
        excluded_ranges = []
        
        for curr_match in matches:
            curr_start, curr_end = curr_match['position']
            is_contained = False
            
            # 检查当前匹配是否完全被已选择的匹配包含
            for start, end in excluded_ranges:
                if curr_start >= start and curr_end <= end:
                    is_contained = True
                    break
            
            # 如果不被包含，则添加到结果中
            if not is_contained:
                result.append(curr_match)
                excluded_ranges.append((curr_start, curr_end))
        
        # 恢复按位置排序
        result.sort(key=lambda x: x['position'][0])
        return result

    @lru_cache(maxsize=128)
    def _convert_cn_to_num(self, text: str) -> int:
        """将中文数字转换为阿拉伯数字，处理复杂情况"""
        if text.isdigit():
            return int(text)
            
        if not text:
            return 1
        
        # 处理"十"开头的特殊情况
        if text.startswith('十'):
            text = '一' + text
        
        # 处理"百"、"千"开头的特殊情况
        if text.startswith('百'):
            text = '一' + text
        if text.startswith('千'):
            text = '一' + text
        
        # 标准化数字表示
        text = text.replace('零', '')
        
        # 处理"二十"、"三十"等简写
        for i, digit in enumerate(['', '一', '二', '三', '四', '五', '六', '七', '八', '九']):
            if i > 0:
                text = text.replace(f'{digit}十', f'{digit}十零')
        
        # 替换繁体数字为简体
        text = text.replace('壹', '一').replace('贰', '二').replace('叁', '三')
        text = text.replace('肆', '四').replace('伍', '五').replace('陆', '六')
        text = text.replace('柒', '七').replace('捌', '八').replace('玖', '九')
        text = text.replace('拾', '十').replace('佰', '百').replace('仟', '千')
        text = text.replace('萬', '万').replace('億', '亿')
        
        # 处理复杂中文数字
        result = 0
        temp_num = 0
        temp_unit = 1
        
        for char in text:
            if char in self.cn_num:
                value = self.cn_num[char]
                if value >= 10:  # 是单位
                    if value > temp_unit:  # 更大的单位
                        result = (result + temp_num) * value
                        temp_unit = value
                    else:  # 小于等于当前单位
                        result += temp_num * value
                    temp_num = 0
                else:  # 是数字
                    temp_num = temp_num * 10 + value if temp_num >= 10 else value
        
        result += temp_num
        return result if result > 0 else 1

    def _extract_numbers_from_text(self, text: str) -> List[float]:
        """从文本中提取数字（支持中文数字、阿拉伯数字和分数）"""
        numbers = []
        
        try:
            # 提取阿拉伯数字
            arabic_nums = re.findall(self.digit_pattern, text)
            numbers.extend([float(num) for num in arabic_nums])
            
            # 查找分数表达式 (如 1/2, 1/4)
            fraction_pattern = r'(\d+)/(\d+)'
            fractions = re.findall(fraction_pattern, text)
            fraction_values = [float(num) / float(denom) for num, denom in fractions]
            
            # 查找中文分数表达 (如 "四分之一", "三分之二")
            cn_fraction_pattern = r'([一二三四五六七八九十百千]+)分之([一二三四五六七八九十百千]+)'
            cn_fractions = re.findall(cn_fraction_pattern, text)
            cn_fraction_values = [self._convert_cn_to_num(numerator) / self._convert_cn_to_num(denominator) 
                                  for denominator, numerator in cn_fractions]
            
            # 再查找中文数字
            cn_nums = re.findall(self.cn_num_pattern, text)
            cn_values = [self._convert_cn_to_num(num) for num in cn_nums]
            
            numbers.extend(fraction_values)
            numbers.extend(cn_fraction_values)
            numbers.extend(cn_values)
            
        except Exception as e:
            logger.error(f"提取数字时出错: {e}, 文本: {text}")
            # 处理失败但不中断程序
        
        return numbers

    def _is_date_not_duration(self, match: str) -> bool:
        """判断匹配是表示具体日期而非持续时间
        
        返回:
            True 表示是日期，False 表示可能是持续时间
        """
        # 使用编译好的模式进行匹配
        for pattern in self.compiled_date_patterns:
            if pattern.search(match):
                return True
                
        # "号"字在中文中明确表示日期
        if "号" in match and "天" not in match:
            return True
            
        # 如果包含"日"但不包含"天"，且没有明显的持续时间标记
        if "日" in match and "天" not in match:
            duration_indicators = ['期限', '持续', '为期', '時間', '时间']
            for indicator in duration_indicators:
                if indicator in match:
                    return False
            return True
            
        return False

    def _calculate_relative_time_value(self, match: str) -> Tuple[float, float]:
        """计算相对时间范围的天数值"""
        # 首先检查是否是特殊情况
        if match in self.special_cases:
            return self.special_cases[match]
            
        now = datetime.now()
        
        # 处理"今年X月份"
        month_match = re.search(r'今年(\d{1,2})月', match)
        if month_match:
            month = int(month_match.group(1))
            if 1 <= month <= 12:
                target_date = datetime(now.year, month, 1)
                days = (now - target_date).days
                return -days, 0
        
                # 处理上半年、下半年
        if '上半年' in match:
            if now.month > 6:
                start_date = datetime(now.year, 1, 1)
                end_date = datetime(now.year, 7, 1)
            else:
                start_date = datetime(now.year - 1, 7, 1)
                end_date = datetime(now.year, 1, 1)
            days_start = (now - start_date).days
            days_end = (now - end_date).days
            return -days_start, -days_end
            
        if '下半年' in match:
            if now.month <= 6:
                start_date = datetime(now.year, 7, 1)
                end_date = datetime(now.year + 1, 1, 1)
            else:
                start_date = datetime(now.year, 7, 1)
                end_date = datetime(now.year + 1, 1, 1)
            days_start = (now - start_date).days
            days_end = (now - end_date).days
            return -days_start, -days_end
        
        # 处理季度
        if '季度' in match:
            if '上个季度' in match or '上個季度' in match:
                return self._past_quarter_days(now)
            elif '本季度' in match or '这个季度' in match or '這個季度' in match:
                return self._current_quarter_days(now)
        
        # 处理财年
        if '财年' in match or '財年' in match:
            # 假设财年从4月1日开始
            current_month = now.month
            if current_month >= 4:
                fiscal_year_start = datetime(now.year, 4, 1)
            else:
                fiscal_year_start = datetime(now.year - 1, 4, 1)
            fiscal_year_end = datetime(fiscal_year_start.year + 1, 4, 1)
            
            days_start = (now - fiscal_year_start).days
            days_end = (now - fiscal_year_end).days
            return -days_start, -days_end
        
        # 处理"最近"、"过去"等相对时间
        if re.search(fr'{self.past_time_prefix}', match):
            # 处理范围表达：过去三到六个月
            if self._is_range_expression(match):
                nums = self._extract_numbers_from_text(match)
                if len(nums) >= 2:
                    min_num, max_num = sorted(nums[:2])
                    unit_days = self._get_unit_days_from_match(match)
                    if unit_days:
                        return -max_num * unit_days, -min_num * unit_days
            else:
                # 处理单一时间：最近七天、过去一个月
                nums = self._extract_numbers_from_text(match)
                num = nums[0] if nums else 1
                unit_days = self._get_unit_days_from_match(match)
                if unit_days:
                    return -num * unit_days, 0
        
        # 处理"这"、"本"等当前时间
        if re.search(fr'{self.current_time_prefix}', match):
            if '天' in match or '日' in match:
                return 0, 0  # 今天
            elif '周' in match or '星期' in match or '礼拜' in match or '週' in match or '禮拜' in match:
                # 计算本周开始到现在的天数
                weekday = now.weekday()  # 0是周一，6是周日
                return -weekday, 0
            elif '月' in match:
                # 本月第一天到现在的天数
                month_start = datetime(now.year, now.month, 1)
                days = (now - month_start).days
                return -days, 0
            elif '年' in match:
                # 今年第一天到现在的天数
                year_start = datetime(now.year, 1, 1)
                days = (now - year_start).days
                return -days, 0
        
        # 处理"去年"
        if '去年' in match:
            year_start = datetime(now.year - 1, 1, 1)
            year_end = datetime(now.year, 1, 1)
            days_start = (now - year_start).days
            days_end = (now - year_end).days
            return -days_start, -days_end
            
        # 处理"前年"
        if '前年' in match:
            year_start = datetime(now.year - 2, 1, 1)
            year_end = datetime(now.year - 1, 1, 1)
            days_start = (now - year_start).days
            days_end = (now - year_end).days
            return -days_start, -days_end
        
        # 默认返回值
        return 0, 0

    def _current_quarter_days(self, now: datetime) -> Tuple[float, float]:
        """计算本季度的天数范围"""
        current_quarter = (now.month - 1) // 3 + 1
        start_month = (current_quarter - 1) * 3 + 1
        
        # 本季度开始
        quarter_start = datetime(now.year, start_month, 1)
        
        # 本季度结束
        if current_quarter == 4:
            quarter_end = datetime(now.year + 1, 1, 1)
        else:
            quarter_end = datetime(now.year, start_month + 3, 1)
        
        days_start = (now - quarter_start).days
        days_end = (now - quarter_end).days
        
        return -days_start, -days_end

    def _past_quarter_days(self, now: datetime) -> Tuple[float, float]:
        """计算上个季度的天数范围"""
        current_quarter = (now.month - 1) // 3 + 1
        
        if current_quarter == 1:
            # 如果当前是第一季度，上一季度是去年第四季度
            start_date = datetime(now.year - 1, 10, 1)
            end_date = datetime(now.year, 1, 1)
        else:
            # 否则是今年的上一个季度
            start_month = (current_quarter - 2) * 3 + 1
            end_month = (current_quarter - 1) * 3 + 1
            start_date = datetime(now.year, start_month, 1)
            end_date = datetime(now.year, end_month, 1)
            
        days_start = (now - start_date).days
        days_end = (now - end_date).days
        
        return -days_start, -days_end

    def _calculate_duration_time_value(self, match: str) -> Tuple[float, Optional[float]]:
        """计算持续时间范围的天数值"""
        # 首先检查是否是特殊情况
        if match in self.special_cases:
            return self.special_cases[match]
        
        # 检查各种修饰词
        has_fuzzy_pre = bool(re.search(self.fuzzy_pre_modifiers, match))
        has_fuzzy_post = bool(re.search(self.fuzzy_post_modifiers, match))
        has_pre_upper = bool(re.search(self.pre_upper_limit, match))
        has_post_upper = bool(re.search(self.post_upper_limit, match))
        has_pre_lower = bool(re.search(self.pre_lower_limit, match))
        has_post_lower = bool(re.search(self.post_lower_limit, match))
        
        # 处理模糊表达
        if re.search(r'几个?月|幾個?月', match):
            return (self.constants.DAYS_IN_MONTH * 2, self.constants.DAYS_IN_MONTH * 4)
        if re.search(r'几天|幾天', match):
            return self.constants.DEFAULT_FEW_DAYS
        if re.search(r'几周|幾周|几个?星期|幾個?星期', match):
            return (self.constants.DAYS_IN_WEEK * 2, self.constants.DAYS_IN_WEEK * 4)
        
        # 处理范围表达式
        if self._is_range_expression(match):
            return self._handle_range_expression(match, has_fuzzy_pre or has_fuzzy_post)
        
        # 处理上限修饰（前置或后置）
        elif has_pre_upper or has_post_upper:
            return self._handle_upper_limit_expression(match)
        
        # 处理下限修饰（前置或后置）
        elif has_pre_lower or has_post_lower:
            return self._handle_lower_limit_expression(match)
        
        # 处理精确时间表达式
        else:
            return self._handle_exact_expression(match, has_fuzzy_pre or has_fuzzy_post)
    
    def _handle_range_expression(self, match: str, has_fuzzy: bool) -> Tuple[float, float]:
        """处理范围表达式，支持更多格式"""
        nums = self._extract_numbers_from_text(match)
        if len(nums) < 2:
            return (0, 0)
        
        # 处理范围内的单位可能不同的情况
        units = []
        if re.search(r'天|日', match):
            units.append(self.constants.DAYS_IN_DAY)
        if re.search(r'月', match):
            units.append(self.constants.DAYS_IN_MONTH)
        if re.search(r'周|星期|礼拜|週|禮拜', match):
            units.append(self.constants.DAYS_IN_WEEK)
        if re.search(r'季', match):
            units.append(self.constants.DAYS_IN_QUARTER)
        if re.search(r'年', match):
            units.append(self.constants.DAYS_IN_YEAR)
        
        # 默认使用相同单位
        if len(units) == 1:
            min_num, max_num = sorted(nums[:2])
            min_value = min_num * units[0]
            max_value = max_num * units[0]
        # 处理不同单位的情况 
        elif len(units) > 1:
            # 假设第一个数字使用第一个单位，第二个数字使用第二个单位
            values = [nums[i] * units[min(i, len(units)-1)] for i in range(len(nums))]
            min_value, max_value = sorted(values[:2])
        else:
            return (0, 0)
        
        # 处理模糊修饰
        if has_fuzzy:
            fuzzy_range = self.constants.FUZZY_MODIFIER_RANGE
            min_value *= (1 - fuzzy_range)
            max_value *= (1 + fuzzy_range)
        
        return (min_value, max_value)
    
    def _handle_upper_limit_expression(self, match: str) -> Tuple[float, float]:
        """处理上限修饰（前置或后置）"""
        nums = self._extract_numbers_from_text(match)
        if not nums:
            return (0, 0)
        
        num = nums[0]
        unit_days = self._get_unit_days_from_match(match)
        
        if not unit_days:
            return (0, 0)
        
        # 上限表达式返回从0到指定值的范围
        return (0, num * unit_days)
    
    def _handle_lower_limit_expression(self, match: str) -> Tuple[float, Optional[float]]:
        """处理下限修饰（前置或后置）"""
        nums = self._extract_numbers_from_text(match)
        if not nums:
            return (0, None)
        
        num = nums[0]
        unit_days = self._get_unit_days_from_match(match)
        
        if not unit_days:
            return (0, None)
        
        # 下限表达式返回从指定值到无穷大的范围
        return (num * unit_days, None)
    
    def _handle_exact_expression(self, match: str, has_fuzzy: bool) -> Tuple[float, float]:
        """处理精确时间表达，如'3个月'"""
        # 处理"不到"表达式
        if '不到' in match:
            return self._handle_upper_limit_expression(match)
        
        nums = self._extract_numbers_from_text(match)
        if not nums:
            return (0, 0)
        
        num = nums[0]
        unit_days = self._get_unit_days_from_match(match)
        
        if not unit_days:
            return (0, 0)
        
        value = num * unit_days
        
        # 处理模糊修饰
        if has_fuzzy:
            fuzzy_range = self.constants.FUZZY_MODIFIER_RANGE
            return (value * (1 - fuzzy_range), value * (1 + fuzzy_range))
        
        return (value, value)
    
    def _normalize_values(self, min_value: Optional[float], max_value: Optional[float]) -> Tuple[Optional[float], Optional[float]]:
        """归一化值，处理舍入和类型一致性"""
        if min_value is not None:
            min_value = round(min_value, 1)
        if max_value is not None:
            max_value = round(max_value, 1)
        return min_value, max_value

    def _is_range_expression(self, text: str) -> bool:
        """检查是否是范围表达式，增加更多可能的连接词"""
        for connector in ['到', '至', '-', '~', '、', '和', '与', '或', '及']:
            if connector in text:
                return True
        return False

    def _get_unit_days_from_match(self, match: str) -> Optional[float]:
        """从匹配文本中获取时间单位的天数值"""
        if re.search(r'天|日', match):
            return self.constants.DAYS_IN_DAY
        elif re.search(r'月', match):
            return self.constants.DAYS_IN_MONTH
        elif re.search(r'周|星期|礼拜|週|禮拜', match):
            return self.constants.DAYS_IN_WEEK
        elif re.search(r'季', match):
            return self.constants.DAYS_IN_QUARTER
        elif re.search(r'年', match):
            return self.constants.DAYS_IN_YEAR
        return None

    def format_time_range(self, min_days: Optional[float], max_days: Optional[float], 
                         is_relative: bool = False) -> str:
        """
        将天数格式化为易读的时间表达
        
        Args:
            min_days: 最小天数
            max_days: 最大天数
            is_relative: 是否是相对时间
            
        Returns:
            格式化后的时间表达，如"3-6个月"、"1年以上"等
        """
        if min_days is None and max_days is None:
            return "未知时间"
        
        result = ""
        prefix = "过去" if is_relative and min_days and min_days < 0 else ""
        
        # 处理下限
        if min_days is not None and (max_days is None or min_days != max_days):
            abs_min = abs(min_days) if min_days else 0
            
            if abs_min >= self.constants.DAYS_IN_YEAR:
                years = abs_min / self.constants.DAYS_IN_YEAR
                result += f"{prefix}{years:.1f}年"
            elif abs_min >= self.constants.DAYS_IN_MONTH:
                months = abs_min / self.constants.DAYS_IN_MONTH
                result += f"{prefix}{months:.1f}个月"
            elif abs_min >= self.constants.DAYS_IN_WEEK:
                weeks = abs_min / self.constants.DAYS_IN_WEEK
                result += f"{prefix}{weeks:.1f}周"
            else:
                result += f"{prefix}{abs_min:.1f}天"
        
        # 处理上限
        if max_days is not None:
            if min_days is None:
                # 只有上限
                abs_max = abs(max_days)
                if abs_max >= self.constants.DAYS_IN_YEAR:
                    years = abs_max / self.constants.DAYS_IN_YEAR
                    result += f"{years:.1f}年以内"
                elif abs_max >= self.constants.DAYS_IN_MONTH:
                    months = abs_max / self.constants.DAYS_IN_MONTH
                    result += f"{months:.1f}个月以内"
                elif abs_max >= self.constants.DAYS_IN_WEEK:
                    weeks = abs_max / self.constants.DAYS_IN_WEEK
                    result += f"{weeks:.1f}周以内"
                else:
                    result += f"{abs_max:.1f}天以内"
            elif min_days != max_days:
                # 上下限都有
                abs_max = abs(max_days)
                
                if abs_max >= self.constants.DAYS_IN_YEAR:
                    years = abs_max / self.constants.DAYS_IN_YEAR
                    result += f"到{years:.1f}年"
                elif abs_max >= self.constants.DAYS_IN_MONTH:
                    months = abs_max / self.constants.DAYS_IN_MONTH
                    result += f"到{months:.1f}个月"
                elif abs_max >= self.constants.DAYS_IN_WEEK:
                    weeks = abs_max / self.constants.DAYS_IN_WEEK
                    result += f"到{weeks:.1f}周"
                else:
                    result += f"到{abs_max:.1f}天"
        
        # 处理精确值
        if min_days is not None and max_days is not None and min_days == max_days:
            abs_val = abs(min_days)
            if abs_val >= self.constants.DAYS_IN_YEAR:
                years = abs_val / self.constants.DAYS_IN_YEAR
                result = f"{prefix}{years:.1f}年"
            elif abs_val >= self.constants.DAYS_IN_MONTH:
                months = abs_val / self.constants.DAYS_IN_MONTH
                result = f"{prefix}{months:.1f}个月"
            elif abs_val >= self.constants.DAYS_IN_WEEK:
                weeks = abs_val / self.constants.DAYS_IN_WEEK
                result = f"{prefix}{weeks:.1f}周"
            else:
                result = f"{prefix}{abs_val:.1f}天"
        
        # 处理"以上"
        if min_days is not None and max_days is None:
            result += "以上"
        
        return result


if __name__ == "__main__":
    """测试时间抽取器的功能"""
    extractor = TimeExtractor()
    
    # 测试文本示例
    test_texts = [
        # 相对时间测试
        "上个月的销售数据",
        "上周业绩如何？",
        "昨天发布的公告",
        "前天的交易量",
        "去年的财务报表",
        "本季度的市场表现",
        "今年以来的涨幅",
        
        # 持续时间测试
        "投资期限三个月",
        "贷款期限为一年",
        "基金锁定期七天",
        "合同有效期半年",
        "两年零三个月的项目",
        
        # 范围表达式测试
        "投资期限三到六个月",
        "过去三到六个月",
        "还款期限为一至两年",
        "持有时间五~十天",
        
        # 有界范围测试
        "三个月以上的存款",
        "一年以内的项目",
        "不超过半年的贷款",
        "至少两周的持有期",
        
        # 模糊表达式测试
        "大约三个月的周期",
        "投资时间差不多一年",
        "两周左右的培训",
        
        # 特殊情况测试
        "一年半期限",
        "投资期半个月",
        "一个半月的项目",
        
        # 中文数字测试
        "贷款期限为三个月",
        "持有时间一年",
        "十天的冷静期",
        "二十一天的体验期",
        "期限是一百天",
        
        # 复杂文本测试
        """该基金的投资期限为三到五年，最低持有期为六个月。
        过去一个月的收益率为5%，过去一年的收益率为15%。
        建议投资者至少持有一年以上，风险承受能力较低的投资者建议持有两年到三年。
        产品发行日期为2023年4月1日，每周三开放申购。"""
        
        # 添加更多边缘情况测试
        "今年以来销售额增长20%",
        "截至目前已完成80%的任务",
        "最近的一个季度业绩",
        "过去半年多的努力",
        "未来一两周内完成",
        "接下来几天内",
        "三五天后交付",
        "十天八天也做不完",
        "投资回报周期约为二十来天",
        "两年零三个月的合同期",
        "至少要三个月的时间",
        "不会超过半年的试用期",
        "大概三个月左右的工期",
        "差不多一年的时间",
    ]
    
    # 循环处理每个测试文本
    for i, text in enumerate(test_texts):
        print(f"\n=== 测试用例 {i+1} ===")
        print(f"文本: {text}")
        
        # 抽取时间信息
        results = extractor.extract_time(text)
        
        # 输出抽取结果
        if results:
            print(f"发现 {len(results)} 个时间表达式:")
            for j, result in enumerate(results):
                match = result['match']
                slot_value = result['slot_value']
                min_value = result['minValue']
                max_value = result['maxValue']
                
                # 格式化时间范围
                is_relative = (slot_value == 'relative_time_range')
                formatted = extractor.format_time_range(min_value, max_value, is_relative)
                
                print(f"  {j+1}. 匹配: '{match}'")
                print(f"     类型: {slot_value}")
                print(f"     位置: {result['position']}")
                print(f"     最小值: {min_value} 天")
                print(f"     最大值: {max_value} 天")
                print(f"     格式化: {formatted}")
        else:
            print("未发现时间表达式")