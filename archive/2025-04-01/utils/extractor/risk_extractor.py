"""
风险等级抽取器

专注于提取金融文本中的风险等级信息

输入格式:
    text: str - 包含风险等级信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的风险等级信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'risk_range' 或 'specific_risk'
        "minValue": int,         # 范围最小值（如R1对应1，R5对应5）
        "maxValue": int,         # 范围最大值（如R1对应1，R5对应5），可能为 None
    }
"""

import re   
from typing import Dict, List, Any, Tuple, Pattern, Optional
from app.utils.extractor.shared.shared_utils import create_result, logger
from app.utils.extractor.num.num_utils import (
    RATE_RANGE_CONNECTOR,  
    RATE_APPROXIMATE_PREFIX, RATE_APPROXIMATE_SUFFIX,
    RATE_UPPER_LIMIT_PREFIX, RATE_UPPER_LIMIT_SUFFIX,
    RATE_LOWER_LIMIT_PREFIX, RATE_LOWER_LIMIT_SUFFIX,
)

# 风险等级表达式 (R1-R5或者对应的文本描述)
RISK_LEVEL_EXPR = r'''
(?: 
  (?:R[1-5])|(?:r[1-5])|
  (?:低风险)|(?:中低风险)|(?:中风险)|(?:中高风险)|(?:高风险)
)
'''

# 风险等级超级模式
RISK_SUPER_PATTERN_STR = fr'''
(?: 
  # 近似前缀修饰词（可选）
  (?P<approximate_prefix>{RATE_APPROXIMATE_PREFIX})?
  
  # 上限前缀修饰词（可选）
  (?P<upper_limit_prefix>{RATE_UPPER_LIMIT_PREFIX})?
  
  # 下限前缀修饰词（可选）
  (?P<lower_limit_prefix>{RATE_LOWER_LIMIT_PREFIX})?
  
  (?:
    # 格式1：风险等级+连接词+风险等级
    (?P<risk_level1>{RISK_LEVEL_EXPR})
    (?P<connector>{RATE_RANGE_CONNECTOR})
    (?P<risk_level2>{RISK_LEVEL_EXPR})
    |
    # 格式2：单个风险等级
    (?P<risk_level>{RISK_LEVEL_EXPR})
  )
  
  # 上限后缀修饰词（可选）
  (?P<upper_limit_suffix>{RATE_UPPER_LIMIT_SUFFIX})?
  
  # 下限后缀修饰词（可选）
  (?P<lower_limit_suffix>{RATE_LOWER_LIMIT_SUFFIX})?
  
  # 近似后缀修饰词（可选）
  (?P<approximate_suffix>{RATE_APPROXIMATE_SUFFIX})?
)
'''

# 风险等级映射
RISK_LEVEL_MAPPING = {
    "R1": 1, "低风险": 1, "r1": 1,
    "R2": 2, "中低风险": 2, "r2": 2,
    "R3": 3, "中风险": 3, "r3": 3,
    "R4": 4, "中高风险": 4, "r4": 4,
    "R5": 5, "高风险": 5, "r5": 5,
}

# 定义槽位类型
SPECIFIC_RISK = "specific_risk"
RISK_RANGE = "risk_range"

# 编译正则表达式
RISK_SUPER_PATTERN = re.compile(RISK_SUPER_PATTERN_STR, re.VERBOSE)

class RiskModifierHandler:
    """处理风险等级修饰词"""
    
    @staticmethod
    def has_modifier(text: str) -> bool:
        """判断文本是否包含修饰词"""
        # 检查各类修饰词
        has_approximate = bool(re.search(f"{RATE_APPROXIMATE_PREFIX}|{RATE_APPROXIMATE_SUFFIX}", text))
        has_upper_limit = bool(re.search(f"{RATE_UPPER_LIMIT_PREFIX}|{RATE_UPPER_LIMIT_SUFFIX}", text))
        has_lower_limit = bool(re.search(f"{RATE_LOWER_LIMIT_PREFIX}|{RATE_LOWER_LIMIT_SUFFIX}", text))
        
        return has_approximate or has_upper_limit or has_lower_limit
    
    @staticmethod
    def apply_modifiers(min_value: int, max_value: int, text: str) -> Tuple[Optional[int], Optional[int]]:
        """应用修饰词对风险等级范围的影响"""
        # original_min = min_value
        # original_max = max_value
        
        # 处理上限修饰词
        if re.search(f"{RATE_UPPER_LIMIT_PREFIX}|{RATE_UPPER_LIMIT_SUFFIX}", text):
            min_value = None  # 设置下界为None，表示无下限
            # max_value保持不变
        
        # 处理下限修饰词
        if re.search(f"{RATE_LOWER_LIMIT_PREFIX}|{RATE_LOWER_LIMIT_SUFFIX}", text):
            max_value = None  # 设置上界为None，表示无上限
            # min_value保持不变
        
        # # 处理近似修饰词 - 对于风险等级，近似可能意味着前后一个等级的范围
        # if re.search(f"{RATE_APPROXIMATE_PREFIX}|{RATE_APPROXIMATE_SUFFIX}", text):
        #     if original_min == original_max:  # 只有当单一值时才扩展范围
        #         min_value = max(1, original_min - 1)
        #         max_value = min(5, original_max + 1)
        
        return min_value, max_value

class RiskLevelHandler:
    """处理风险等级文本到数值的转换"""
    
    @staticmethod
    def text_to_level(text: str) -> int:
        """将风险等级文本转换为数值"""
        return RISK_LEVEL_MAPPING.get(text, 0)

class RiskExtractor:
    """
    风险等级抽取器
    专注于从文本中提取风险等级信息
    """
    
    def extract_risk(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取风险等级信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的风险等级信息列表
        """
        try:
            results = []
            covered_positions = set()  # 记录已覆盖的位置
            
            # 处理统一风险等级超级模式
            for match in RISK_SUPER_PATTERN.finditer(text):
                result = self.process_unified_risk(match)
                if result and not self._is_position_covered(match.start(), match.end(), covered_positions):
                    self._mark_covered_positions(match.start(), match.end(), covered_positions)
                    results.append(result)
            
            # 过滤和标准化提取的值
            return self._filter_values(results)
            
        except Exception as e:
            logger.warning(f"[RISK_EXTRACTOR] Warning: {e.__class__.__name__}: {e}, 原始文本: '{text}'")
            return []
    
    def process_unified_risk(self, match) -> Optional[Dict[str, Any]]:
        """处理统一风险等级模式的匹配结果"""
        match_text = match.group(0)
        
        # 提取命名捕获组
        components = {name: value for name, value in match.groupdict().items() if value is not None}
        min_value = None
        max_value = None
        
        try:
            # 处理连接词形式的风险等级范围（如"R1到R3"）
            if 'risk_level1' in components and 'risk_level2' in components:
                value1 = RiskLevelHandler.text_to_level(components['risk_level1'])
                value2 = RiskLevelHandler.text_to_level(components['risk_level2'])
                min_value = min(value1, value2)
                max_value = max(value1, value2)
                slot_value = RISK_RANGE
            
            # 处理单个风险等级（如"R3"）
            elif 'risk_level' in components:
                value = RiskLevelHandler.text_to_level(components['risk_level'])
                # 如果没有修饰词，则认为是单一风险等级
                if not RiskModifierHandler.has_modifier(match_text):
                    return create_result(
                        match=match_text,
                        position=(match.start(), match.end()),
                        slot="risk",
                        slot_value=SPECIFIC_RISK,
                        value=value
                    )
                else:
                    min_value = value
                    max_value = value
                    slot_value = RISK_RANGE
                
            # 应用修饰词的影响（如"至少"、"不超过"等）
            if min_value is not None or max_value is not None:
                # 如果有修饰词，应用修饰词调整范围
                min_value, max_value = RiskModifierHandler.apply_modifiers(
                    min_value if min_value is not None else max_value,
                    max_value if max_value is not None else min_value,
                    match_text
                )
                
                # 创建结果
                return create_result(
                    match=match_text,
                    position=(match.start(), match.end()),
                    slot="risk",
                    slot_value=slot_value,
                    min_value=min_value,
                    max_value=max_value
                )
                
        except ValueError as e:
            logger.warning(f"[RISK_EXTRACTOR] Warning: {e}，匹配文本: '{match_text}'")
        except Exception as e:
            logger.warning(f"[RISK_EXTRACTOR] Warning: {e.__class__.__name__}: {e}")
            
        return None
    
    def _filter_values(self, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤无效的风险等级值"""
        filtered_values = []
        
        for value in values:
            # 获取最小值和最大值
            min_value = value.get("minValue")
            max_value = value.get("maxValue")
            
            # 跳过无效数据情况
            if min_value is None and max_value is None:
                continue
                
            # 检查有效值是否在合理范围内(1-5)
            if min_value is not None and (min_value < 1 or min_value > 5):
                continue
                
            if max_value is not None and (max_value < 1 or max_value > 5):
                continue
                
            # 如果最小值和最大值都存在，确保最大值大于等于最小值
            if min_value is not None and max_value is not None and max_value < min_value:
                value["minValue"] = max_value
                value["maxValue"] = min_value
                
            # 通过所有检查，添加到结果列表
            filtered_values.append(value)
                
        return filtered_values
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos)