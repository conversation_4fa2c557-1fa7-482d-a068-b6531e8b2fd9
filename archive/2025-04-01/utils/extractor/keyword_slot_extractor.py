"""
基于关键词的槽位抽取器
负责从文本中提取基于关键词匹配的槽位信息

输入格式:
    text: str - 待提取的文本
    
输出格式:
    List[Dict[str, Any]] - 提取的槽位信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "value": str,            # 条件值名称
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值
        "position": Tuple[int, int]  # 在原文中的位置 (start, end)
    }
"""

from typing import Dict, List, Any
from resource.slot_definitions import SLOTS
from resource.keywords_mapping import COMPILED_KEYWORD_PATTERNS
from app.utils.extractor.shared.shared_utils import create_result, logger

class KeywordSlotExtractor:
    """
    基于关键词的槽位抽取器
    负责从文本中提取基于关键词匹配的槽位信息
    """
    
    def __init__(self):
        """
        初始化关键词槽位抽取器
        加载槽位定义和关键词映射
        """
        self.slots = SLOTS
        self.keyword_patterns = COMPILED_KEYWORD_PATTERNS
    
    def extract_keyword_slot(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取关键词槽位信息
        
        Args:
            text (str): 待提取的文本
            
        Returns:
            List[Dict[str, Any]]: 槽位匹配信息列表
        """
        try:
            # 1. 首先匹配所有可能的槽位值
            all_matches = self._find_all_matches(text)
            
            # 3. 将内部格式转换为标准结果格式
            final_results = []
            for match in all_matches:
                final_results.append(create_result(
                    match=match["match"],
                    position=match["position"],
                    slot=match["slot"],
                    slot_value=match["slot_value"],
                    value=match.get("value", "")
                ))
            
            return final_results
            
        except Exception as e:
            logger.warn(f"[KEYWORD_SLOT_EXTRACTOR] Warning: {e}")
            return []
    
    def _find_all_matches(self, text: str) -> List[Dict[str, Any]]:
        """
        查找文本中所有可能的关键词匹配
        
        Args:
            text: 待匹配的文本
            
        Returns:
            List[Dict[str, Any]]: 所有可能的匹配结果
        """
        all_matches = []  # 存储所有匹配结果
        
        # R值到数值的映射字典
        r_value_mapping = {
            "R1": "1",
            "R2": "2",
            "R3": "3",
            "R4": "4",
            "R5": "5"
        }
        
        # 遍历所有槽位
        for slot, values in self.keyword_patterns.items():
            # 获取槽位名称
            slot_name = self.slots[slot]["name"]
            
            # 遍历该槽位下的所有条件值
            for value_key, patterns in values.items():
                # 获取条件值的name属性
                slot_value = value_key
                value_name = self.slots[slot]["values"][value_key]["name"]
                is_bs_value = self.slots[slot]["values"][value_key].get("is_bs_value", True)
                
                # 遍历该条件值下的所有模式
                for pattern_tuple in patterns:
                    pattern, keyword = pattern_tuple
                    
                    # 使用正则表达式匹配
                    for match in pattern.finditer(text):
                        match_text = match.group()
                        start, end = match.span()
                        
                        if is_bs_value:
                            value = value_name
                        else:
                            value = ""
                        
                        # 检查并转换R值
                        converted_value = value
                        if value in r_value_mapping:
                            converted_value = r_value_mapping[value]

                        # 记录匹配信息
                        match_info = {
                            "match": match_text,
                            "value": converted_value,  
                            "slot_value": "specific_risk",  
                            "slot": slot,
                            "position": (start, end),
                            "length": len(match_text)
                        }
                        all_matches.append(match_info)
        
        return all_matches