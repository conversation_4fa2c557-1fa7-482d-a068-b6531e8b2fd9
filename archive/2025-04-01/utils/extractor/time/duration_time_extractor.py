"""
金融领域持续时间抽取器

专注于提取金融文本中的时间信息，抽取持续时间范围：

如"一年半"、"90天到180天"、"三年左右"等
    
特别说明：
    1. 一年按366天计算，一个月按30天计算
    2. 持续时间是指具体的时间跨度，如果有最小最大时间跨度描述，则最小最大值为对应的描述转天数，
       如果只有单个时间跨度描述，则最小最大值为该描述转天数。

输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'duration_time_range'
        "minValue": float,       # 范围最小值（天数），可能为 None，但不与maxValue同时为None
        "maxValue": float,       # 范围最大值（天数），可能为 None，但不与minValue同时为None
    }
"""
from typing import Dict, List, Any, Optional, Tuple
import re
from app.utils.extractor.time.time_utils import (
    DURATION_TIME_RANGE, TIME_UNIT_PATTERN,
    DURATION_UPPER_LIMIT_PREFIX, DURATION_UPPER_LIMIT_SUFFIX,
    DURATION_LOWER_LIMIT_PREFIX, DURATION_LOWER_LIMIT_SUFFIX,
    DURATION_APPROXIMATE_PREFIX, DURATION_APPROXIMATE_SUFFIX,
    DURATION_OTHER_SUFFIX, TIME_RANGE_CONNECTOR, DURATION_INFIX_SEPARATOR,
    TIME_UNIT_EXPR, DURATION_FIRST_PREFIX, DURATION_SECOND_PREFIX,
    SHORT_TERM_PATTERN, MIDDLE_TERM_PATTERN, LONG_TERM_PATTERN, T_PLUS_0_PATTERN, T_PLUS_1_PATTERN,
    TimeUnitHandler, TimeModifierHandler
)
from app.utils.extractor.num.num_utils import chinese_to_num, COMPLEX_NUMBER_PATTERN
from app.utils.extractor.shared.shared_utils import create_result

# 时间范围模式
DURATION_SUPER_PATTERN_STR = fr'''
(?: 
  # 近似前缀修饰词（可选）
  (?P<approximate_prefix>{DURATION_APPROXIMATE_PREFIX})?
  
  # 上限前缀修饰词（可选）
  (?P<upper_limit_prefix>{DURATION_UPPER_LIMIT_PREFIX})?
  
  # 下限前缀修饰词（可选）
  (?P<lower_limit_prefix>{DURATION_LOWER_LIMIT_PREFIX})?
  
  (?:
    # 格式1：时间单元+连接词+时间单元
    (?P<time_unit1>{TIME_UNIT_EXPR})
    (?P<connector>{TIME_RANGE_CONNECTOR})
    (?P<time_unit2>{TIME_UNIT_EXPR})
    |
    # 格式2：单个时间单元
    (?P<time_unit>{TIME_UNIT_EXPR})
    |
    # 格式3：数字+连接词+数字+时间单元
    (?P<number1>{COMPLEX_NUMBER_PATTERN})
    (?P<connector2>{TIME_RANGE_CONNECTOR})
    (?P<number2>{COMPLEX_NUMBER_PATTERN})
    (?P<shared_unit>{TIME_UNIT_PATTERN})
  )
  
  # 上限后缀修饰词（可选）
  (?P<upper_limit_suffix>{DURATION_UPPER_LIMIT_SUFFIX})?
  
  # 下限后缀修饰词（可选）
  (?P<lower_limit_suffix>{DURATION_LOWER_LIMIT_SUFFIX})?
  
  # 近似后缀修饰词（可选）
  (?P<approximate_suffix>{DURATION_APPROXIMATE_SUFFIX})?
  
  # 其他后缀修饰（可选）
  (?P<other_suffix>{DURATION_OTHER_SUFFIX})?
)
'''

# 修改中缀持续时间模式
DURATION_RANGE_WITH_INFIX_PATTERN_STR = fr'{DURATION_FIRST_PREFIX}的?{TIME_UNIT_EXPR}{DURATION_INFIX_SEPARATOR}{DURATION_SECOND_PREFIX}的?{TIME_UNIT_EXPR}'

# 编译正则表达式
DURATION_SUPER_PATTERN = re.compile(DURATION_SUPER_PATTERN_STR, re.VERBOSE)
DURATION_RANGE_WITH_INFIX_PATTERN = re.compile(DURATION_RANGE_WITH_INFIX_PATTERN_STR)
TERM_PATTERN = re.compile(fr'(?:{SHORT_TERM_PATTERN})|(?:{MIDDLE_TERM_PATTERN})|(?:{LONG_TERM_PATTERN})')

class DurationTimeExtractor:
    """持续时间抽取器，用于提取文本中的持续时间表达（如"3个月"、"1-2年"等）"""
    
    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取持续时间表达式"""
        results = []
        
        # 1. 处理统一持续时间模式
        for match in DURATION_SUPER_PATTERN.finditer(text):
            result = self.process_unified_duration(match)
            if result:
                results.append(result)
        
        # 2. 处理中缀模式
        for match in DURATION_RANGE_WITH_INFIX_PATTERN.finditer(text):
            result = self.process_infix_duration(match)
            if result:
                results.append(result)
        
        # 3. 处理短期/长期模式
        for match in TERM_PATTERN.finditer(text):
            result = self.process_term(match)
            if result:
                results.append(result)
                
        # 4. 处理 T+0 模式
        for match in re.finditer(T_PLUS_0_PATTERN, text):
            result = self.process_t_plus_zero(match)
            if result:
                results.append(result)
                
        # 5. 处理 T+1 模式
        for match in re.finditer(T_PLUS_1_PATTERN, text):
            result = self.process_t_plus_one(match)
            if result:
                results.append(result)
        
        return results 


    def _convert_to_days(self, number: float, unit: str) -> float:
        """将数字和单位转换为天数"""
        return TimeUnitHandler.to_days(number, unit)

    def _apply_modifiers(self, min_value: float, max_value: float, match_text: str) -> Tuple[float, float]:
        """应用修饰词对时间范围的影响"""
        # 直接调用TimeModifierHandler的apply_modifiers方法
        return TimeModifierHandler.apply_modifiers(min_value, max_value, match_text)

    def _process_time_unit(self, text: str) -> float:
        """处理通用时间单元表达式，返回对应的天数"""
        # 检查是否是复合时间表达式
        if re.search(fr'{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN}.*{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN}', text):
            return self._process_compound_expression(text)
        
        # 检查是否是单位加半表达式
        unit_half_match = re.search(fr'({COMPLEX_NUMBER_PATTERN})({TIME_UNIT_PATTERN})半', text)
        if unit_half_match:
            return self._process_unit_half_expression(text)
        
        # 检查是否是半单位表达式
        half_unit_match = re.search(fr'半(?:个|個)?({TIME_UNIT_PATTERN})', text)
        if half_unit_match:
            return self._process_half_unit_expression(text)
        
        # 默认处理为简单时间单位
        number, unit = TimeUnitHandler.parse_number_and_unit(text)
        return self._convert_to_days(number, unit)

    def _process_compound_expression(self, text: str) -> float:
        """处理复合时间表达式（如"1年3个月"或"1年零3个月"）"""
        total_days = 0
        number_matches = list(re.finditer(COMPLEX_NUMBER_PATTERN, text))
        unit_matches = list(re.finditer(TIME_UNIT_PATTERN, text))
        
        if len(number_matches) == len(unit_matches):
            for i in range(len(number_matches)):
                num = chinese_to_num(number_matches[i].group(1))
                unit = TimeUnitHandler.standardize(unit_matches[i].group(1))
                total_days += self._convert_to_days(num, unit)
                
        return total_days

    def _process_unit_half_expression(self, text: str) -> float:
        """处理单位加半表达式（如"一年半"、"两月半"）"""
        # 提取数字和单位
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        
        if not number_match or not unit_match:
            return 0
        
        number = chinese_to_num(number_match.group(1))
        unit = TimeUnitHandler.standardize(unit_match.group(1))
        
        # 计算基础天数
        base_days = self._convert_to_days(number, unit)
        
        # 计算"半"的天数（单位的一半）
        half_days = self._convert_to_days(0.5, unit)
        
        # 返回总天数
        return base_days + half_days
        
    def _process_half_unit_expression(self, text: str) -> float:
        """处理半单位表达式（如"半年"、"半个月"）"""
        # 提取单位
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        
        if not unit_match:
            return 0
        
        unit = TimeUnitHandler.standardize(unit_match.group(1))
        
        # 计算半个单位的天数
        return self._convert_to_days(0.5, unit)

    def process_unified_duration(self, match) -> Optional[Dict[str, Any]]:
        """处理统一持续时间模式的匹配结果"""
        match_text = match.group(0)
        
        components = {name: value for name, value in match.groupdict().items() if value is not None}
        min_value = None
        max_value = None
        
        # 处理连接词形式的时间范围
        if 'time_unit1' in components and 'time_unit2' in components:
            days1 = self._process_time_unit(components['time_unit1'])
            days2 = self._process_time_unit(components['time_unit2'])
            min_value = min(days1, days2)
            max_value = max(days1, days2)
        
        # 处理单个时间单元
        elif 'time_unit' in components:
            days = self._process_time_unit(components['time_unit'])
            min_value = max_value = days
            
        # 处理数字+连接词+数字+时间单元模式
        elif 'number1' in components and 'number2' in components and 'shared_unit' in components:
            number1 = chinese_to_num(components['number1'])
            number2 = chinese_to_num(components['number2'])
            unit = TimeUnitHandler.standardize(components['shared_unit'])
            
            days1 = self._convert_to_days(number1, unit)
            days2 = self._convert_to_days(number2, unit)
            
            min_value = min(days1, days2)
            max_value = max(days1, days2)
    
        if min_value is None or max_value is None:
            return None
        
        # 应用修饰词的影响
        min_value, max_value = self._apply_modifiers(min_value, max_value, match_text)
        
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE,
            min_value=min_value,
            max_value=max_value,
        )

    def process_infix_duration(self, match) -> Optional[Dict[str, Any]]:
        """处理带中缀的持续时间范围匹配（如"短则3天，长则7天"）"""
        match_text = match.group(0)
        
        # 解析文本中的两个数字和单位
        number_matches = list(re.finditer(COMPLEX_NUMBER_PATTERN, match_text))
        if len(number_matches) < 2:
            return None
            
        number1 = chinese_to_num(number_matches[0].group(1))
        number2 = chinese_to_num(number_matches[1].group(1))
        
        # 查找单位
        unit_matches = list(re.finditer(TIME_UNIT_PATTERN, match_text))
        if len(unit_matches) < 2:
            return None
            
        unit1 = TimeUnitHandler.standardize(unit_matches[0].group(1))
        unit2 = TimeUnitHandler.standardize(unit_matches[1].group(1))
        
        # 验证匹配有效性
        if (TimeUnitHandler.is_year_identifier(f"{number1}{unit1}") or 
            TimeUnitHandler.is_year_identifier(f"{number2}{unit2}")):
            return None
        
        # 转换为天数
        days1 = self._convert_to_days(number1, unit1)
        days2 = self._convert_to_days(number2, unit2)
        
        # 确保最小值在前，最大值在后
        min_value = min(days1, days2)
        max_value = max(days1, days2)
        
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE,
            min_value=min_value,
            max_value=max_value,
        )

    def process_term(self, match) -> Optional[Dict[str, Any]]:
        """处理短期/中期/长期表达式"""
        match_text = match.group(0)
        
        # 检查是否为短期或长期
        is_short_term = bool(re.search(SHORT_TERM_PATTERN, match_text))
        is_middle_term = bool(re.search(MIDDLE_TERM_PATTERN, match_text))
        is_long_term = bool(re.search(LONG_TERM_PATTERN, match_text))
        
        if is_short_term:
            # 短期: 30到30天
            min_value = 0.0
            max_value = 30.0
        elif is_middle_term:
            # 中期: 30到180天
            min_value = 30.0
            max_value = 180.0
        elif is_long_term:
            # 长期: 180天以上
            min_value = 180.0
            max_value = None  # None 表示无上限
        else:
            return None
        
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE,
            min_value=min_value,
            max_value=max_value,
        )

    def process_t_plus_zero(self, match) -> Dict[str, Any]:
        """处理 T+0 模式"""
        return create_result(
            match=match.group(0),
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE,
            min_value=0.0,
            max_value=0.0,
        )

    def process_t_plus_one(self, match) -> Dict[str, Any]:
        """处理 T+1 模式"""
        return create_result(
            match=match.group(0),
            position=(match.start(), match.end()),
            slot="time",
            slot_value=DURATION_TIME_RANGE,
            min_value=1.0,
            max_value=1.0,
        )
