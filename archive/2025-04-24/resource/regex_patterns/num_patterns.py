import re

NUM_PREFIX = "num"

# 基础中文数字映射
BASE_DIGIT_MAP = {
    '零': 0, '〇': 0, 
    '一': 1,
    '二': 2, 
    '三': 3, 
    '四': 4, 
    '五': 5, 
    '六': 6,
    '七': 7,
    '八': 8,
    '九': 9, 
}

# 中文数字单位映射
UNIT_MAP = {
    '十': 10,
    '百': 100,
    '千': 1000, 
    '万': 10000, 
    '亿': 100000000, 
}

# 繁体数字和特殊形式映射到标准中文数字
TRADITIONAL_MAP = {
    # 数字
    '壹': "一",
    '贰': "二", '兩': "二", '两': "二", '双': "二", '貳': "二",
    '叁': "三", '叄': "三",
    '肆': "四",
    '伍': "五",
    '陆': "六", '陸': "六",
    '柒': "七",
    '捌': "八",
    '玖': "九",
    # 单位
    '拾': "十",
    '佰': "百",
    '仟': "千", 'K': "千", 'k': "千",
    '萬': "万", 'W': "万", 'w': "万",
    '億': "亿",
}

# 小数点字符集合
DECIMAL_POINTS = {'点', '點', '.'}

# 正则表达式模式
# 阿拉伯数字模式
ARABIC_PATTERN = r'(\d+(?:\.\d+)?)'

# 中文数字字符集（包含所有可能的形式）
CN_CHARS = ''.join(set(list(BASE_DIGIT_MAP.keys()) + list(UNIT_MAP.keys()) + list(TRADITIONAL_MAP.keys()) + list(DECIMAL_POINTS)))
CN_CHAR_PATTERN = f'[{CN_CHARS}]'

# 中文数字模式
CN_NUMBER_PATTERN = f'({CN_CHAR_PATTERN}+)'

# 混合数字模式
MIXED_PATTERN = f'((?:{ARABIC_PATTERN}\\s*|{CN_CHAR_PATTERN})+(?:{CN_CHAR_PATTERN}|\\s*{CN_CHAR_PATTERN}|\\s*{ARABIC_PATTERN})*)'

# 小数点模式
DECIMAL_PATTERN = f'[{"".join(DECIMAL_POINTS)}]'

# 带小数点的混合数字模式
DECIMAL_MIXED_PATTERN = f'({MIXED_PATTERN}{DECIMAL_PATTERN}{MIXED_PATTERN})'

# 百分比模式：百分之一、四分之五、25分之1
PERCENT_PATTERN = f'((?:百分之{MIXED_PATTERN})|(?:{MIXED_PATTERN}分之{MIXED_PATTERN}))'

# 复合数字模式（用于完整匹配）
COMPLEX_NUMBER_PATTERN = f'({DECIMAL_MIXED_PATTERN}|{MIXED_PATTERN}|(?:\\d+\\s*[Ww]\\d+)|(?:\\d+\\s*[WwKk]))|(?:{PERCENT_PATTERN})'

# 合并所有数字映射（用于查询）
ALL_DIGIT_MAP = {**BASE_DIGIT_MAP, **{k: v for k, v in UNIT_MAP.items()}}

COMPILED_NUM_PATTERNS = {
    f"{NUM_PREFIX}-complex_number": COMPLEX_NUMBER_PATTERN,
}

if __name__ == "__main__":
    import sys
    import os

    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from resource.update_helper import update_patterns_to_es
    update_patterns_to_es(COMPILED_NUM_PATTERNS, "tool_regexs") 