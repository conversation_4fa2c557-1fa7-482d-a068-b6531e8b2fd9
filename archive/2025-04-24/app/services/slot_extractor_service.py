"""
基于规则的理财槽位抽取服务

输入格式:
    text: str - 用户输入的文本
    
输出格式:
    Dict[str, Dict[str, Any]]: 提取的槽位信息
        格式：{
            slot_name: {
                "match": 匹配到的文本,
                "slot_value": 槽位值名称,
                "startPos": 开始位置,
                "endPos": 结束位置,
                # 如果槽位值为范围值，则包含以下字段：
                "minValue": 最小值,
                "maxValue": 最大值,
                # 如果槽位值为具体值，则包含以下字段：
                "value": 具体值,
            },
            ...
        }
                
"""

import logging
from typing import Dict, List, Any

from app.utils.extractor import (
    TimeExtractor,
    AmountExtractor,
    RateExtractor,
    RiskExtractor,
    KeywordSlotExtractor
)
from app.utils.extractor.shared.shared_utils import (
    resolve_overlapping_results,
    process_filtered_time_slots
)

logger = logging.getLogger(__name__)

class FinancialSlotExtractorService:
    """
    基于规则的理财槽位抽取服务
    负责从用户输入中提取槽位信息
    """
    
    def __init__(self):
        """初始化槽位抽取服务"""
        self.time_extractor = TimeExtractor()
        self.amount_extractor = AmountExtractor()
        self.rate_extractor = RateExtractor()
        self.risk_extractor = RiskExtractor()
        self.keyword_slot_extractor = KeywordSlotExtractor()

    def _convert_position_format(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将position格式转换为startPos和endPos格式"""
        converted_results = []
        for result in results:
            if "position" in result:
                result_copy = result.copy()
                start, end = result_copy.pop("position")
                result_copy["startPos"] = start
                result_copy["endPos"] = end
                converted_results.append(result_copy)
            else:
                converted_results.append(result)
        return converted_results

    def extract_slots(self, text: str) -> Dict[str, Dict[str, Any]]:
        """
        从文本中提取槽位信息
        
        Args:
            text: 用户输入的文本
            
        Returns:
            Dict[str, Dict[str, Any]]: 提取的槽位信息
        """
        # 1. 使用各个抽取器提取结果
        time_results = self.time_extractor.extract_time(text)
        amount_results = self.amount_extractor.extract_amount(text)
        rate_results = self.rate_extractor.extract_rate(text)
        risk_results = self.risk_extractor.extract_risk(text)
        keyword_results = self.keyword_slot_extractor.extract_keyword_slot(text)
        
        # 2. 合并所有结果并转换position格式
        all_results = []
        all_results.extend(self._convert_position_format(amount_results))
        all_results.extend(self._convert_position_format(rate_results))
        all_results.extend(self._convert_position_format(risk_results))
        all_results.extend(self._convert_position_format(keyword_results))
        all_results.extend(self._convert_position_format(time_results))

        # 3. 重叠问题 - 获取非重叠结果和被过滤的结果
        non_overlapping_results, filtered_results = resolve_overlapping_results(all_results)
        
        # 4. 特殊处理被过滤的时间槽位
        recovered_time_slots = process_filtered_time_slots(non_overlapping_results, filtered_results)
        
        # 5. 将恢复的时间槽位添加到非重叠结果中
        if recovered_time_slots:
            logger.info(f"恢复了 {len(recovered_time_slots)} 个特殊条件下的时间槽位")
            non_overlapping_results.extend(recovered_time_slots)
        
        # 6. 将结果转换为最终格式
        final_slots = {}
        for result in non_overlapping_results:
            # 确保结果有需要的字段
            if "slot" not in result:
                continue
                
            slot_name = result["slot"]
            result_copy = result.copy()
            
            # 处理position格式
            if "position" in result_copy and "startPos" not in result_copy:
                start, end = result_copy.pop("position")
                result_copy["startPos"] = start
                result_copy["endPos"] = end
                
            # 移除slot字段，因为它将作为key
            if "slot" in result_copy:
                del result_copy["slot"]
                
            if slot_name not in final_slots:
                final_slots[slot_name] = [result_copy]
            else:
                final_slots[slot_name].append(result_copy)
            
        logger.debug(f"提取结果: {final_slots}")
        return final_slots
 