"""
槽位抽取服务的路由处理模块
"""

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from typing import Dict, List, Any, Optional, Tuple

from app.services.slot_extractor_service import FinancialSlotExtractorService
from resource.slot_definitions import SLOTS
from app.services.es_service import es_service
from app.services.cache_service import cache_service

router = APIRouter()

# 定义请求数据模型
class dataParam(BaseModel):
    question: str

# 定义槽位信息模型
class SlotInfo(BaseModel):
    name: str
    value: Optional[str] = None
    minValue: Optional[Any] = None
    maxValue: Optional[Any] = None

# 定义槽位匹配结果模型
class SlotMatch(BaseModel):
    keyword: str
    offset: Tuple[int, int]
    slot: SlotInfo

# 定义槽位更新项模型
class SlotUpdateItem(BaseModel):
    slot_name: str
    slot_value: str
    pattern_str: str
    data_type: Optional[str] = "regexs"  # 默认为regexs类型

# 定义槽位更新请求模型
class SlotUpdateRequest(BaseModel):
    update_data: List[SlotUpdateItem]

# 创建槽位抽取服务实例
slot_extractor = FinancialSlotExtractorService()

@router.post("/slot_extract", description="从用户输入中提取槽位信息")
async def slot_predict(data: dataParam, request: Request):
    """
    从用户输入中提取槽位信息
    
    Args:
        request (QueryRequest): 包含用户查询的请求对象
        
    Returns:
        槽位结果
    """
    try:
        question = data.question
        # 提取槽位
        extraction_results = slot_extractor.extract_slots(question)

        # 转换为API响应格式
        slot_matches = []
        
        # 遍历所有提取到的槽位
        for slot_name, matches in extraction_results.items():
            for match in matches:
                # 提取匹配信息
                keyword = match.get("match", "")
                position = (match.get("startPos", 0), match.get("endPos", 0))
                
                # 创建槽位信息
                slot_info = SlotInfo(name=slot_name)
                
                # 处理不同类型的槽位
                if "minValue" in match and "maxValue" in match:
                    # 范围型槽位（如时间、金额、收益率）
                    slot_info.minValue = match.get("minValue")
                    slot_info.maxValue = match.get("maxValue")
                else:
                    # 枚举型槽位
                    slot_info.value = match.get("value")
                
                # 创建匹配结果
                slot_match = SlotMatch(
                    keyword=keyword,
                    offset=position,
                    slot=slot_info
                )
                
                slot_matches.append(slot_match)
        
        # 返回结果
        return slot_matches
        
    except Exception as e:
        print(f"槽位提取失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"槽位提取失败: {str(e)}")

@router.post("/update_slot", description="更新存储在ES中的槽位正则表达式")
async def update_slot(update_request: SlotUpdateRequest):
    """
    更新存储在ES中的槽位正则表达式，并触发缓存更新
    
    Args:
        update_request (SlotUpdateRequest): 包含更新数据的请求对象
        
    Returns:
        更新结果
    """
    try:
        # 检查ES连接
        if not es_service.is_connected():
            if not es_service.reconnect():
                raise HTTPException(status_code=503, detail="ES服务不可用")
        
        update_results = []
        any_success = False # 标记是否有任何更新成功
        for item in update_request.update_data:
            # 更新ES中的数据
            success = es_service.upsert_slot_data(
                item.slot_name,
                item.slot_value,
                item.data_type,
                item.pattern_str
            )
            
            update_results.append({
                "slot_name": item.slot_name,
                "slot_value": item.slot_value,
                "data_type": item.data_type,
                "success": success
            })
            if success:
                any_success = True # 如果有任何一个更新成功，则标记

        # 如果至少有一个更新成功，则强制更新缓存
        if any_success:
            force_update_success = cache_service.force_update()
            if not force_update_success:
                # 可以选择记录日志或在响应中添加缓存更新失败的信息
                 print("警告：ES数据已更新，但缓存强制更新失败") # 使用 print 替代 logger，因为 logger 未在此处定义
        
        return {
            "message": f"成功更新{sum(1 for r in update_results if r['success'])}个槽位，失败{sum(1 for r in update_results if not r['success'])}个。{'缓存已触发更新。' if any_success else '未触发缓存更新。'}", # 更新消息
            "results": update_results
        }
        
    except Exception as e:
        print(f"更新槽位失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"更新槽位失败: {str(e)}")