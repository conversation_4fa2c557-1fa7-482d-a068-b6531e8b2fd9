"""
风险等级抽取器

专注于提取金融文本中的风险等级信息

输入格式:
    text: str - 包含风险等级信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的风险等级信息列表
    每个字典包含以下字段：  
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'risk_range' 或 'specific_risk'
        "minValue": int,         # 范围最小值（如R1对应1，R5对应5）
        "maxValue": int,         # 范围最大值（如R1对应1，R5对应5），可能为 None
    }
"""

from typing import Dict, List, Any, Optional
from app.utils.extractor.num.num_utils import (
    RiskLevelHandler,
    RiskModifierHandler
)
from app.utils.extractor.shared.shared_utils import create_result, logger
from app.services.cache_service import cache_service

class RiskExtractor:
    """
    风险等级抽取器
    专注于从文本中提取风险等级信息
    """
    # 定义槽位类型
    SPECIFIC_RISK = "specific_risk"
    RISK_RANGE = "risk_range"
    
    def extract_risk(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取风险等级信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的风险等级信息列表
        """
        try:
            results = []
            covered_positions = set()  # 记录已覆盖的位置
            RISK_SUPER_PATTERN = cache_service.get_tool_regexs(slot_name="risk", tool_name="super_pattern")
            
            # 处理统一风险等级超级模式
            for match in RISK_SUPER_PATTERN.finditer(text):
                result = self.process_unified_risk(match)
                if result and not self._is_position_covered(match.start(), match.end(), covered_positions):
                    self._mark_covered_positions(match.start(), match.end(), covered_positions)
                    results.append(result)

            # 过滤和标准化提取的值
            return self._filter_values(results)
            
        except Exception as e:
            logger.warning(f"[RISK_EXTRACTOR] Warning: {e.__class__.__name__}: {e}, 原始文本: '{text}'")
            return []
    
    def process_unified_risk(self, match) -> Optional[Dict[str, Any]]:
        """处理统一风险等级模式的匹配结果"""
        match_text = match.group(0)
        
        # 提取命名捕获组
        components = {name: value for name, value in match.groupdict().items() if value is not None}

        min_value = None
        max_value = None
        
        # 从组件中提取修饰词信息
        has_upper_limit = bool(components.get('upper_limit_prefix') or components.get('upper_limit_suffix'))
        has_lower_limit = bool(components.get('lower_limit_prefix') or components.get('lower_limit_suffix'))
        has_approximate = bool(components.get('approximate_prefix') or components.get('approximate_suffix'))
        
        try:
            # 处理连接词形式的风险等级范围（如"R1到R3"）
            if 'risk_level1' in components and 'risk_level2' in components:
                level1_text = components['risk_level1']
                level2_text = components['risk_level2']
                
                value1 = RiskLevelHandler.text_to_level(level1_text)
                value2 = RiskLevelHandler.text_to_level(level2_text)
                
                if value1 is None or value2 is None:
                    logger.warning(f"[RISK_EXTRACTOR] Warning: 无法解析风险等级：'{level1_text}' 或 '{level2_text}'")
                    return None
                    
                min_value = min(value1, value2)
                max_value = max(value1, value2)
                slot_value = self.RISK_RANGE
            
            # 处理单个风险等级（如"R3"）
            elif 'risk_level' in components:
                level_text = components['risk_level']
                value = RiskLevelHandler.text_to_level(level_text)

                if value is None:
                    logger.warning(f"[RISK_EXTRACTOR] Warning: 无法解析风险等级：'{level_text}'")
                    return None
                
                # 如果没有任何修饰词，则认为是单一风险等级
                if not has_upper_limit and not has_lower_limit and not has_approximate:
                    return create_result(
                        match=match_text,
                        position=(match.start(), match.end()),
                        slot="risk_level",
                        slot_value=self.SPECIFIC_RISK,
                        value=value
                    )
                else:
                    min_value = value
                    max_value = value
                    slot_value = self.RISK_RANGE
                    
            # 应用修饰词的影响（如"至少"、"不超过"等）
            if min_value is not None or max_value is not None:
                # 直接传递修饰词标志，而不是在RiskModifierHandler中再次检查
                min_value, max_value = RiskModifierHandler.apply_modifiers_with_flags(
                    min_value if min_value is not None else max_value,
                    max_value if max_value is not None else min_value,
                    has_upper_limit=has_upper_limit,
                    has_lower_limit=has_lower_limit,
                    has_approximate=has_approximate
                )
                
                # 创建结果
                return create_result(
                    match=match_text,
                    position=(match.start(), match.end()),
                    slot="risk_level",
                    slot_value=slot_value,
                    min_value=min_value,
                    max_value=max_value
                )
                
        except ValueError as e:
            logger.warning(f"[RISK_EXTRACTOR] Warning: {e}，匹配文本: '{match_text}'")
        except Exception as e:
            logger.warning(f"[RISK_EXTRACTOR] Warning: {e.__class__.__name__}: {e}")
            
        return None
    
    def _filter_values(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤无效的风险等级值"""
        filtered_values = []
        
        for result in results:
            # 获取最小值和最大值
            min_value = result.get("minValue")
            max_value = result.get("maxValue")
            value = result.get("value")
            
            # 跳过无效数据情况
            if min_value is None and max_value is None and value is None:
                continue
                
            # 检查有效值是否在合理范围内(1-5)
            if min_value is not None and (min_value < 1 or min_value > 5):
                continue
                
            if max_value is not None and (max_value < 1 or max_value > 5):
                continue
            
            if value is not None and (value < 1 or value > 5):
                continue
            
            # 如果最小值和最大值都存在，确保最大值大于等于最小值
            if min_value is not None and max_value is not None and max_value < min_value:
                result["minValue"] = max_value
                result["maxValue"] = min_value
                
            # 通过所有检查，添加到结果列表
            filtered_values.append(result)
                
        return filtered_values
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos)