"""
通用提取工具
包含各类提取器共用的工具函数和常量
"""
from app.utils.extractor.num.number_convert import text_to_num
from resource.regex_patterns.num_patterns import COMPLEX_NUMBER_PATTERN

from resource.regex_patterns.amount_patterns import (
    AmountUnits,
    AMOUNT_UNIT_PATTERN, 
    AMOUNT_NEGATION_PREFIX, 
    AMOUNT_APPROXIMATE_PREFIX, 
    AMOUNT_APPROXIMATE_SUFFIX, 
    AMOUNT_UPPER_LIMIT_PREFIX, 
    AMOUNT_UPPER_LIMIT_SUFFIX, 
    AMOUNT_LOWER_LIMIT_PREFIX, 
    AMOUNT_LOWER_LIMIT_SUFFIX
)
from resource.regex_patterns.rate_patterns import (
    RateUnits,
    RATE_UNIT_PATTERN, 
    RATE_NEGATION_PREFIX, 
    RATE_CONTEXT_KEYWORDS,
    RATE_APPROXIMATE_PREFIX, 
    RATE_APPROXIMATE_SUFFIX, 
    RATE_UPPER_LIMIT_PREFIX, 
    RATE_UPPER_LIMIT_SUFFIX, 
    RATE_LOWER_LIMIT_PREFIX, 
    RATE_LOWER_LIMIT_SUFFIX
)
from resource.regex_patterns.risk_patterns import (
    RISK_APPROXIMATE_PREFIX,
    RISK_APPROXIMATE_SUFFIX,
    RISK_UPPER_LIMIT_PREFIX,
    RISK_UPPER_LIMIT_SUFFIX,
    RISK_LOWER_LIMIT_PREFIX,
    RISK_LOWER_LIMIT_SUFFIX,
    R1_RISK_LEVEL_PATTERN,
    R2_RISK_LEVEL_PATTERN,
    R3_RISK_LEVEL_PATTERN,
    R4_RISK_LEVEL_PATTERN,
    R5_RISK_LEVEL_PATTERN,
)
from typing import Tuple, List, Dict, Optional
import re

class AmountUnitHandler:
    """金额单位处理器"""
    
    @classmethod
    def standardize(cls, unit: str) -> str:
        """标准化金额单位"""
        return AmountUnits.get_standard_unit(unit)
    
    @classmethod
    def to_yuan(cls, value: float, unit: str) -> float:
        """将金额单位转换为元"""
        standard_unit = cls.standardize(unit)
        return value * AmountUnits.YUAN_CONVERSION.get(standard_unit, 1)
    
    @classmethod
    def parse_number_and_unit(cls, text: str) -> Tuple[float, str]:
        """从文本中解析数字和单位"""
        # 处理可能带有空格的文本
        # 保留原始文本用于单位提取，但为数字提取准备无空格版本
        text_no_space = text.replace(" ", "")
        
        # 提取数字（使用无空格版本以确保如"10 万"被正确解析）
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text_no_space)
        number_text = number_match.group(0) if number_match else "1"
        number = text_to_num(number_text)
        
        # 提取单位（使用原始文本，因为单位提取模式可以处理空格）
        unit_match = re.search(AMOUNT_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else "元"
                
        return number, unit
    
    @classmethod
    def normalize_amount(cls, text: str, context: str = None) -> float:
        """
        将金额文本标准化为元
        
        Args:
            text: 金额文本
            context: 上下文文本，有助于识别单位
            
        Returns:
            float: 标准化后的金额（元）
        """
        number, unit = cls.parse_number_and_unit(text if context is None else text + " " + context)
 
        return cls.to_yuan(number, unit)
        
class AmountModifierHandler:
    """金额修饰词处理器"""
    
    @classmethod
    def _check_pattern(cls, text: str, pattern: str) -> bool:
        """检查文本中是否存在指定的非空模式"""
        return bool(pattern and pattern.strip() and re.search(fr'({pattern})', text))

    @classmethod
    def _find_matches(cls, text: str, patterns: List[str]) -> List[Tuple[str, int, int]]:
        """查找给定模式列表的所有匹配项"""
        matches = []
        for pattern in patterns:
            if pattern and pattern.strip():
                for match in re.finditer(fr'({pattern})', text):
                    matches.append((match.group(0), match.start(), match.end()))
        return matches

    @classmethod
    def _filter_contained_matches(cls, primary_matches: List[Tuple[str, int, int]], secondary_matches: List[Tuple[str, int, int]]) -> List[Tuple[str, int, int]]:
        """过滤掉 primary_matches 中被 secondary_matches 包含的匹配项"""
        filtered = []
        for primary in primary_matches:
            is_contained = False
            for secondary in secondary_matches:
                # 如果 primary 的起止位置都在 secondary 的范围内，则认为被包含
                if secondary[1] <= primary[1] and primary[2] <= secondary[2]:
                    is_contained = True
                    break
            if not is_contained:
                filtered.append(primary)
        return filtered

    @classmethod
    def get_modifiers(cls, text: str) -> Tuple[bool, bool, bool, bool]:
        """
        精确检测文本中的上限、下限修饰词、否定前缀和近似修饰词
        
        Args:
            text: 要检查的文本
            
        Returns:
            Tuple[bool, bool, bool, bool]: (是否有上限修饰词, 是否有下限修饰词, 是否有否定前缀, 是否有近似修饰词)
        """
        # 检查否定前缀
        has_negation = cls._check_pattern(text, AMOUNT_NEGATION_PREFIX)

        # 检查近似修饰词
        approx_pattern = f"{AMOUNT_APPROXIMATE_PREFIX}|{AMOUNT_APPROXIMATE_SUFFIX}"
        has_approximate = cls._check_pattern(text, approx_pattern)

        # 查找上限和下限修饰词
        upper_patterns = [AMOUNT_UPPER_LIMIT_PREFIX, AMOUNT_UPPER_LIMIT_SUFFIX]
        lower_patterns = [AMOUNT_LOWER_LIMIT_PREFIX, AMOUNT_LOWER_LIMIT_SUFFIX]

        all_upper_matches = cls._find_matches(text, upper_patterns)
        all_lower_matches = cls._find_matches(text, lower_patterns)

        # 过滤掉相互包含的修饰词
        # 过滤掉被下限词包含的上限词
        filtered_upper = cls._filter_contained_matches(all_upper_matches, all_lower_matches)
        # 过滤掉被上限词包含的下限词
        filtered_lower = cls._filter_contained_matches(all_lower_matches, all_upper_matches)

        has_upper = len(filtered_upper) > 0
        has_lower = len(filtered_lower) > 0

        return has_upper, has_lower, has_negation, has_approximate

    @staticmethod
    def has_upper_limit_modifier(text: str) -> bool:
        """检查是否有上限修饰词"""
        has_upper, _, _, _ = AmountModifierHandler.get_modifiers(text)
        return has_upper
    
    @staticmethod
    def has_lower_limit_modifier(text: str) -> bool:
        """检查是否有下限修饰词"""
        _, has_lower, _, _ = AmountModifierHandler.get_modifiers(text)
        return has_lower
        
    @staticmethod
    def has_negation_prefix(text: str) -> bool:
        """检查是否有否定前缀"""
        _, _, has_negation, _ = AmountModifierHandler.get_modifiers(text)
        return has_negation
    
    @staticmethod
    def has_approximate_modifier(text: str) -> bool:
        """检查是否有近似修饰词"""
        _, _, _, has_approximate = AmountModifierHandler.get_modifiers(text)
        return has_approximate
    
    @staticmethod
    def has_modifier(text: str) -> bool:
        """检查是否有任何修饰词（上限、下限、否定、近似）"""
        has_upper, has_lower, has_negation, has_approximate = AmountModifierHandler.get_modifiers(text)
        return has_upper or has_lower or has_negation or has_approximate
    
    @classmethod
    def apply_modifiers(cls, min_value: float, max_value: float, text: str) -> Tuple[float, float]:
        """
        应用修饰词调整金额范围
        
        Args:
            min_value: 初始最小值（元）
            max_value: 初始最大值（元）
            text: 原始文本
            
        Returns:
            Tuple[float, float]: 调整后的(最小值, 最大值)，最大值可能为None表示无上限
        """
        # 检查是否有各类修饰词
        has_upper, has_lower, has_negation, has_approximate = cls.get_modifiers(text)
        
        # 处理否定前缀：如果有否定前缀，则取反上下限修饰词的影响
        if has_negation:
            # 如果有否定前缀，上限修饰词变为下限修饰词，下限修饰词变为上限修饰词
            has_upper, has_lower = has_lower, has_upper
            
        # 情况1: 单一值 (min_value == max_value) 且有近似修饰词 (并且没有明确的上下限)
        # 注意：如果同时有近似和上下限，优先处理上下限
        if min_value == max_value and has_approximate and not has_lower and not has_upper:
            base_amount = min_value
            # 近似值的范围为±20% (（暂不处理）)
            # min_value = base_amount * 0.8
            # max_value = base_amount * 1.2
            return min_value, max_value
            
        # 情况2: 单一值 (min_value == max_value)
        if min_value == max_value:
            base_amount = min_value
            
            # 下限修饰词：如"至少100元"，"不能少于100元"，表示最小值是100元，最大值无限
            if has_lower:
                min_value = base_amount
                max_value = None
            
            # 上限修饰词：如"不超过100元"，"不能多于100元"，表示最大值是100元，最小值为0
            elif has_upper:
                min_value = 0
                max_value = base_amount
                
        # 情况3: 范围值 (min_value != max_value)
        else:
            # 处理下限修饰词：例如"至少100-200元", "不能少于100-200元"，表示最小值是200元，最大值无限
            if has_lower:
                min_value = max_value  # 取范围中的最大值作为下限
                max_value = None       # 无上限
            
            # 处理上限修饰词：例如"不超过100-200元", "不能多于100-200元"，表示最大值是200元，最小值为0
            elif has_upper:
                min_value = 0          # 最小值设为0
                # max_value保持不变
        
        return min_value, max_value

class RateUnitHandler:
    """收益率单位处理器"""
    
    @classmethod
    def standardize_unit(cls, unit: str) -> str:
        """标准化收益率单位"""
        return RateUnits.get_standard_unit(unit)
    
    @classmethod
    def to_decimal(cls, value: float, unit: str) -> float:
        """将收益率转换为小数形式（如5%转为0.05）"""
        standard_unit = cls.standardize_unit(unit)
        return value * RateUnits.DECIMAL_CONVERSION.get(standard_unit, 0.01)
    
    @classmethod
    def parse_number_unit_period(cls, text: str) -> Tuple[float, str, str]:
        """从文本中解析数字、单位和收益率上下文词"""
        # 处理可能带有空格的文本
        # 保留原始文本用于单位和收益率上下文词提取，但为数字提取准备无空格版本
        text_no_space = text.replace(" ", "")
        
        # 提取数字（使用无空格版本）
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text_no_space)
        number_text = number_match.group(0) if number_match else "0"
        number = text_to_num(number_text)
        
        # 提取单位（使用原始文本）
        unit_match = re.search(RATE_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else ""
        
        # 提取收益率上下文词
        context_match = re.search(RATE_CONTEXT_KEYWORDS, text, re.VERBOSE)
        context = context_match.group(0) if context_match else ""
                
        return number, unit, context
    
    @classmethod
    def normalize_rate(cls, text: str) -> float:
        """
        将收益率文本标准化为小数形式
        
        Args:
            text: 收益率文本
            
        Returns:
            float: 标准化后的收益率（小数形式，如5%返回0.05）
            
        Raises:
            ValueError: 如果文本中既没有单位也没有收益率上下文词，则认为不是有效的收益率表达
        """
        number, unit, context = cls.parse_number_unit_period(text)
        
        # 验证：必须至少含有单位或收益率上下文词之一
        if not unit and not context:
            raise ValueError(f"无效的收益率表达: '{text}' - 缺少单位或收益率上下文词")
            
        # 如果没有单位但有收益率上下文词，默认为百分比单位
        if not unit:
            unit = "%"
            
        return cls.to_decimal(number, unit)

class RateModifierHandler:
    """收益率修饰词处理器"""
    
    @classmethod
    def _check_pattern(cls, text: str, pattern: str) -> bool:
        """检查文本中是否存在指定的非空模式"""
        return bool(pattern and pattern.strip() and re.search(fr'({pattern})', text))

    @classmethod
    def _find_matches(cls, text: str, patterns: List[str]) -> List[Tuple[str, int, int]]:
        """查找给定模式列表的所有匹配项"""
        matches = []
        for pattern in patterns:
            if pattern and pattern.strip():
                for match in re.finditer(fr'({pattern})', text):
                    matches.append((match.group(0), match.start(), match.end()))
        return matches

    @classmethod
    def _filter_contained_matches(cls, primary_matches: List[Tuple[str, int, int]], secondary_matches: List[Tuple[str, int, int]]) -> List[Tuple[str, int, int]]:
        """过滤掉 primary_matches 中被 secondary_matches 包含的匹配项"""
        filtered = []
        for primary in primary_matches:
            is_contained = False
            for secondary in secondary_matches:
                if secondary[1] <= primary[1] and primary[2] <= secondary[2]:
                    is_contained = True
                    break
            if not is_contained:
                filtered.append(primary)
        return filtered

    @classmethod
    def get_modifiers(cls, text: str) -> Tuple[bool, bool, bool, bool]:
        """
        精确检测文本中的上限、下限修饰词、否定前缀和近似修饰词
        
        Args:
            text: 要检查的文本
            
        Returns:
            Tuple[bool, bool, bool, bool]: (是否有上限修饰词, 是否有下限修饰词, 是否有否定前缀, 是否有近似修饰词)
        """
        # 检查否定前缀 (使用RATE常量)
        has_negation = cls._check_pattern(text, RATE_NEGATION_PREFIX)

        # 检查近似修饰词 (使用RATE常量)
        approx_pattern = f"{RATE_APPROXIMATE_PREFIX}|{RATE_APPROXIMATE_SUFFIX}"
        has_approximate = cls._check_pattern(text, approx_pattern)

        # 查找上限和下限修饰词 (使用RATE常量)
        upper_patterns = [RATE_UPPER_LIMIT_PREFIX, RATE_UPPER_LIMIT_SUFFIX]
        lower_patterns = [RATE_LOWER_LIMIT_PREFIX, RATE_LOWER_LIMIT_SUFFIX]

        all_upper_matches = cls._find_matches(text, upper_patterns)
        all_lower_matches = cls._find_matches(text, lower_patterns)

        # 过滤掉相互包含的修饰词 (逻辑同上)
        filtered_upper = cls._filter_contained_matches(all_upper_matches, all_lower_matches)
        filtered_lower = cls._filter_contained_matches(all_lower_matches, all_upper_matches)

        has_upper = len(filtered_upper) > 0
        has_lower = len(filtered_lower) > 0

        return has_upper, has_lower, has_negation, has_approximate

    @staticmethod
    def has_upper_limit_modifier(text: str) -> bool:
        """检查是否有上限修饰词"""
        has_upper, _, _, _ = RateModifierHandler.get_modifiers(text)
        return has_upper
    
    @staticmethod
    def has_lower_limit_modifier(text: str) -> bool:
        """检查是否有下限修饰词"""
        _, has_lower, _, _ = RateModifierHandler.get_modifiers(text)
        return has_lower
        
    @staticmethod
    def has_negation_prefix(text: str) -> bool:
        """检查是否有否定前缀"""
        _, _, has_negation, _ = RateModifierHandler.get_modifiers(text)
        return has_negation
    
    @staticmethod
    def has_approximate_modifier(text: str) -> bool:
        """检查是否有近似修饰词"""
        _, _, _, has_approximate = RateModifierHandler.get_modifiers(text)
        return has_approximate
    
    @staticmethod
    def has_specific_suffix_modifier(text: str) -> bool:
        """检查是否有特定后缀修饰词（几、多）"""
        return bool(re.search(r'(几|多)$', text))
    
    @staticmethod
    def has_modifier(text: str) -> bool:
        """检查是否有修饰词"""
        has_upper, has_lower, has_negation, has_approximate = RateModifierHandler.get_modifiers(text)
        return has_upper or has_lower or has_negation or has_approximate
    
    @classmethod
    def apply_modifiers(cls, min_value: float, max_value: float, text: str) -> Tuple[float, float]:
        """
        应用修饰词调整收益率范围
        
        Args:
            min_value: 初始最小值（小数形式）
            max_value: 初始最大值（小数形式）
            text: 原始文本
            
        Returns:
            Tuple[float, float]: 调整后的(最小值, 最大值)，最大值可能为None表示无上限
        """
        # 检查是否有各类修饰词
        has_upper, has_lower, has_negation, has_approximate = cls.get_modifiers(text)
        has_specific_suffix = cls.has_specific_suffix_modifier(text)
        
        # 处理否定前缀：如果有否定前缀，则取反上下限修饰词的影响
        if has_negation:
            # 如果有否定前缀，上限修饰词变为下限修饰词，下限修饰词变为上限修饰词
            has_upper, has_lower = has_lower, has_upper
        
        # 特殊处理：如果文本以"几"或"多"结尾，只修改最大值 (且无其他上下限)
        if has_specific_suffix and min_value == max_value and not has_lower and not has_upper:
            base_rate = min_value
            # 只增加最大值，最小值保持不变（暂不处理）
            # max_value = base_rate + abs(base_rate * 0.3) # 使用abs确保增加正值
            return min_value, max_value
        
        # 情况1: 单一值 (min_value == max_value) 且有近似修饰词 (并且没有明确的上下限)
        if min_value == max_value and has_approximate and not has_lower and not has_upper:
            base_rate = min_value
            # 近似值的范围为±20%（暂不处理）
            # delta = abs(base_rate * 0.2) # 使用abs确保增减量为正
            # min_value = max(0, base_rate - delta) # 确保不低于0
            # max_value = base_rate + delta
            return min_value, max_value
            
        # 情况2: 单一值 (min_value == max_value)
        if min_value == max_value:
            base_rate = min_value
            
            # 下限修饰词：如"至少5%", "不能少于5%"，表示最小值是5%，最大值无限
            if has_lower:
                min_value = base_rate
                max_value = None
            
            # 上限修饰词：如"不超过5%", "不能多于5%"，表示最大值是5%，最小值为0
            elif has_upper:
                min_value = 0
                max_value = base_rate
                
        # 情况3: 范围值 (min_value != max_value)
        else:
            # 处理下限修饰词：例如"至少5%-8%", "不能少于5%-8%"，表示最小值是8%，最大值无限
            if has_lower:
                min_value = max_value  # 取范围中的最大值作为下限
                max_value = None       # 无上限
            
            # 处理上限修饰词：例如"不超过5%-8%", "不能多于5%-8%"，表示最大值是8%，最小值为0
            elif has_upper:
                min_value = 0          # 最小值设为0
                # max_value保持不变
        
        return min_value, max_value

class RiskModifierHandler:
    """处理风险等级修饰词"""
    
    @staticmethod
    def has_modifier(text: str) -> bool:
        """判断文本是否包含修饰词"""
        # 检查各类修饰词
        has_approximate = bool(re.search(f"{RISK_APPROXIMATE_PREFIX}|{RISK_APPROXIMATE_SUFFIX}", text))
        has_upper_limit = bool(re.search(f"{RISK_UPPER_LIMIT_PREFIX}|{RISK_UPPER_LIMIT_SUFFIX}", text))
        has_lower_limit = bool(re.search(f"{RISK_LOWER_LIMIT_PREFIX}|{RISK_LOWER_LIMIT_SUFFIX}", text))
        
        return has_approximate or has_upper_limit or has_lower_limit
    
    @staticmethod
    def apply_modifiers(min_value: int, max_value: int, text: str) -> Tuple[Optional[int], Optional[int]]:
        """应用修饰词对风险等级范围的影响"""
        # 处理上限修饰词
        has_upper_limit = bool(re.search(f"{RISK_UPPER_LIMIT_PREFIX}|{RISK_UPPER_LIMIT_SUFFIX}", text))
        has_lower_limit = bool(re.search(f"{RISK_LOWER_LIMIT_PREFIX}|{RISK_LOWER_LIMIT_SUFFIX}", text))
        
        return RiskModifierHandler.apply_modifiers_with_flags(
            min_value, max_value, 
            has_upper_limit=has_upper_limit, 
            has_lower_limit=has_lower_limit,
            has_approximate=False  # 暂不处理近似修饰词
        )
    
    @staticmethod
    def apply_modifiers_with_flags(
        min_value: int, 
        max_value: int, 
        has_upper_limit: bool = False, 
        has_lower_limit: bool = False,
        has_approximate: bool = False
    ) -> Tuple[Optional[int], Optional[int]]:
        """根据修饰词标志应用风险等级范围调整"""
        
        # 处理上限修饰词
        if has_upper_limit:
            min_value = None  # 设置下界为None，表示无下限
            # max_value保持不变
        
        # 处理下限修饰词
        if has_lower_limit:
            max_value = None  # 设置上界为None，表示无上限
            # min_value保持不变
        
        # 处理近似修饰词 - 对于风险等级，在需要时可以实现
        # if has_approximate and min_value == max_value:
        #     min_value = max(1, min_value - 1)
        #     max_value = min(5, max_value + 1)
        
        return min_value, max_value

class RiskLevelHandler:
    """处理风险等级文本到数值的转换"""

    # 将模式和等级存储在列表中
    RISK_PATTERNS = [
        (R1_RISK_LEVEL_PATTERN, 1),
        (R2_RISK_LEVEL_PATTERN, 2),
        (R3_RISK_LEVEL_PATTERN, 3),
        (R4_RISK_LEVEL_PATTERN, 4),
        (R5_RISK_LEVEL_PATTERN, 5),
    ]

    @staticmethod
    def text_to_level(text: str) -> Optional[int]:
        """
        将风险等级文本转换为数值，使用正则表达式完全匹配。
        返回对应的风险等级 (1-5)，如果不匹配任何已知模式，则返回 None。
        实现长度优先匹配策略，优先选择匹配长度最长的模式。
        """
        text = text.strip()  # 清理首尾空格
        
        # 存储所有成功匹配的结果：(匹配长度, 风险等级)
        matches = []
        
        # 尝试所有模式匹配
        for pattern, level in RiskLevelHandler.RISK_PATTERNS:
            if re.fullmatch(pattern, text, re.VERBOSE):
                matches.append((len(text), level))

        # 如果有匹配结果，按匹配长度降序排序，返回长度最长的匹配对应的风险等级
        if matches:
            matches.sort(reverse=True)  # 按匹配长度降序排序
            return matches[0][1]  # 返回最长匹配的风险等级
            
        return None 
