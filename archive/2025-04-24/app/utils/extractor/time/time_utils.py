"""
时间抽取相关常量和通用工具类
"""
from typing import <PERSON><PERSON>, List
import re
from app.utils.extractor.num.number_convert import text_to_num
from resource.regex_patterns.num_patterns import COMPLEX_NUMBER_PATTERN

from resource.regex_patterns.time_duration_patterns import (
    TimeUnits,
    TIME_UNIT_PATTERN,
    DURATION_NEGATION_PREFIX,
    DURATION_UPPER_LIMIT_PREFIX,
    DURATION_UPPER_LIMIT_SUFFIX,
    DURATION_LOWER_LIMIT_PREFIX,
    DURATION_LOWER_LIMIT_SUFFIX
)

class TimeUnitHandler:
    """时间单位处理器"""
    
    @classmethod
    def standardize(cls, unit: str) -> str:
        """标准化时间单位"""
        return TimeUnits.get_standard_unit(unit)
    
    @classmethod
    def to_days(cls, value: float, unit: str) -> float:
        """将时间单位转换为天数"""
        standard_unit = cls.standardize(unit)
        return value * TimeUnits.DAYS_CONVERSION.get(standard_unit, 1)
    
    @classmethod
    def parse_number_and_unit(cls, text: str) -> Tu<PERSON>[float, str]:
        """从文本中解析数字和单位"""
        # 提取数字
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        number_text = number_match.group(0) if number_match else "1"
        number = text_to_num(number_text)
        
        # 提取单位
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else "天"
        
        # 处理"半"字
        if "半" in text:
            if text.startswith("半"):
                number = 0.5
            elif "半" in text and number > 0:
                number += 0.5
                
        return number, unit
    
    @classmethod
    def is_year_identifier(cls, text: str) -> bool:
        """
        判断文本是否为年份标识符（如2024年，2000年，2024等）
        
        Args:
            text: 要检查的文本
            
        Returns:
            bool: 如果文本匹配年份标识符规则，则返回True
        """
        # 检查是否为纯数字年份（如2024）
        if text.isdigit() and len(text) == 4 and int(text) > 1000:
            return True
            
        # 提取数字和单位
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        
        # 如果只有数字没有单位，但是符合年份格式
        if number_match and number_match.group(0) == text:
            number = text_to_num(number_match.group(1))
            if 1000 <= number <= 2100:  # 合理的年份范围
                return True
        
        # 检查带单位的年份
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        
        if number_match and unit_match:
            number = text_to_num(number_match.group(1))
            unit = unit_match.group(1)
            
            # 判断是否为年份标识
            if unit in ["年", "年份"] and number >= 1000:
                return True
                
        return False
        
class TimeModifierHandler:
    """时间修饰词处理器"""
    
    @classmethod
    def get_modifiers(cls, text: str) -> Tuple[bool, bool, bool]:
        """
        精确检测文本中的上限、下限修饰词和否定前缀
        
        Args:
            text: 要检查的文本
            
        Returns:
            Tuple[bool, bool, bool]: (是否有上限修饰词, 是否有下限修饰词, 是否有否定前缀)
        """
        # 检查是否有否定前缀
        has_negation = bool(re.search(fr'({DURATION_NEGATION_PREFIX})', text))
        
        # 查找所有可能的上限修饰词
        upper_matches = []
        for pattern in [DURATION_UPPER_LIMIT_PREFIX, DURATION_UPPER_LIMIT_SUFFIX]:
            for match in re.finditer(fr'({pattern})', text):
                upper_matches.append((match.group(0), match.start(), match.end()))
                
        # 查找所有可能的下限修饰词
        lower_matches = []
        for pattern in [DURATION_LOWER_LIMIT_PREFIX, DURATION_LOWER_LIMIT_SUFFIX]:
            for match in re.finditer(fr'({pattern})', text):
                lower_matches.append((match.group(0), match.start(), match.end()))
        
        # 过滤掉被包含在否定词中的修饰词
        # 例如："不少于"中的"少于"不应该被识别为上限修饰词
        filtered_upper = []
        for upper in upper_matches:
            is_contained = False
            for lower in lower_matches:
                # 只检查上限词是否被包含在下限词中（不再判断前缀）
                if lower[1] <= upper[1] and upper[2] <= lower[2]:
                    is_contained = True
                    break
            if not is_contained:
                filtered_upper.append(upper)
                
        filtered_lower = []
        for lower in lower_matches:
            is_contained = False
            for upper in upper_matches:
                # 只检查下限词是否被包含在上限词中（不再判断前缀）
                if upper[1] <= lower[1] and lower[2] <= upper[2]:
                    is_contained = True
                    break
            if not is_contained:
                filtered_lower.append(lower)
        
        has_upper = len(filtered_upper) > 0
        has_lower = len(filtered_lower) > 0
        
        return has_upper, has_lower, has_negation
    
    @staticmethod
    def has_upper_limit_modifier(text: str) -> bool:
        """检查是否有上限修饰词"""
        has_upper, _, _ = TimeModifierHandler.get_modifiers(text)
        return has_upper
    
    @staticmethod
    def has_lower_limit_modifier(text: str) -> bool:
        """检查是否有下限修饰词"""
        _, has_lower, _ = TimeModifierHandler.get_modifiers(text)
        return has_lower
        
    @staticmethod
    def has_negation_prefix(text: str) -> bool:
        """检查是否有否定前缀"""
        _, _, has_negation = TimeModifierHandler.get_modifiers(text)
        return has_negation
    
    @classmethod
    def apply_modifiers(cls, min_value: float, max_value: float, text: str) -> Tuple[float, float]:
        """
        应用修饰词调整时间范围
        
        Args:
            min_value: 初始最小值（天数）
            max_value: 初始最大值（天数）
            text: 原始文本
            
        Returns:
            Tuple[float, float]: 调整后的(最小值, 最大值)，最大值可能为None表示无上限
        """
        # 检查是否有各类修饰词
        has_upper, has_lower, has_negation = cls.get_modifiers(text)
        
        # 处理否定前缀：如果有否定前缀，则取反上下限修饰词的影响
        if has_negation:
            # 如果有否定前缀，上限修饰词变为下限修饰词，下限修饰词变为上限修饰词
            has_upper, has_lower = has_lower, has_upper
  
        # 情况1: 单一值 (min_value == max_value)
        if min_value == max_value:
            base_days = min_value
            
            # 下限修饰词：如"至少3个月"，表示最小值是3个月，最大值无限
            if has_lower:
                min_value = base_days
                max_value = None
            
            # 上限修饰词：如"不超过3个月"，表示最大值是3个月，最小值为0
            elif has_upper:
                min_value = 0
                max_value = base_days
                
        # 情况2: 范围值 (min_value != max_value)
        else:
            # 处理下限修饰词：例如"至少3-6个月"，表示最小值是6个月，最大值无限
            if has_lower:
                min_value = max_value  # 取范围中的最大值作为下限
                max_value = None       # 无上限
            
            # 处理上限修饰词：例如"不超过3-6个月"，表示最大值是6个月，最小值为0
            elif has_upper:
                min_value = 0          # 最小值设为0
                # max_value保持不变
        
        return min_value, max_value
