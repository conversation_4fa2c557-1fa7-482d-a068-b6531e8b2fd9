"""
通用提取工具
包含各类提取器共用的工具函数和常量
"""

from typing import Tuple, List
import re

# 中文数字映射
CHINESE_DIGITS = {
    '零': 0, '〇': 0, '0': 0,
    '一': 1, '壹': 1, '1': 1,
    '二': 2, '贰': 2, '兩': 2, '两': 2, '2': 2,
    '三': 3, '叁': 3, '3': 3,
    '四': 4, '肆': 4, '4': 4,
    '五': 5, '伍': 5, '5': 5,
    '六': 6, '陆': 6, '陸': 6, '6': 6,
    '七': 7, '柒': 7, '7': 7,
    '八': 8, '捌': 8, '8': 8,
    '九': 9, '玖': 9, '9': 9,
    '十': 10, '拾': 10,
    '百': 100, '佰': 100,
    '千': 1000, '仟': 1000,
    '万': 10000, '萬': 10000,
    '亿': 100000000, '億': 100000000
}

# 通用阿拉伯数字模式
ARABIC_NUMBER_PATTERN = r'(\d+(?:\.\d+)?)'

# 中文数字基本单位
CHINESE_NUMBER_CHARS = r'[一二三四五六七八九十百千万亿零兩两壹贰叁肆伍陆柒捌玖拾佰仟萬億]'

# 中文数字模式（纯中文数字表达式）
CHINESE_NUMBER_PATTERN = rf'({CHINESE_NUMBER_CHARS}+)'

# 混合数字模式（允许中文数字和阿拉伯数字混合，例如"1千5百"，"2万3千"等）
MIXED_NUMBER_PATTERN = rf'((?:{ARABIC_NUMBER_PATTERN}|{CHINESE_NUMBER_CHARS})+(?:{CHINESE_NUMBER_CHARS}|{ARABIC_NUMBER_PATTERN})*)'

# 通用数字匹配模式（整合上述所有模式）
NUMBER_PATTERN = rf'({ARABIC_NUMBER_PATTERN}|{CHINESE_NUMBER_PATTERN})'

# 复合数字模式（用于匹配复杂的数字表达，包括带单位的混合表达形式，如"一千二百三十四"，"两万三千"，"1千5百"等）
COMPLEX_NUMBER_PATTERN = rf'({MIXED_NUMBER_PATTERN})'

def chinese_to_num(chinese_str: str) -> float:
    """
    将中文数字转换为阿拉伯数字
    
    支持复合中文数字和混合形式，如：
    - "一千二百三十四"
    - "一亿两千万"
    - "1千5百"
    - "2万3000"
    
    Args:
        chinese_str: 中文数字字符串或混合数字字符串
        
    Returns:
        float: 转换后的数值
    """
    if not chinese_str or chinese_str == "":
        return 0
    
    # 使用正则表达式提取数字部分
    match = re.search(COMPLEX_NUMBER_PATTERN, chinese_str)
    if not match:
        return 0
        
    chinese_str = match.group()
    
    # 如果直接是阿拉伯数字，直接返回
    if re.match(r'^\d+(\.\d+)?$', chinese_str):
        return float(chinese_str)
    
    # 预处理：将混合数字表达式中的阿拉伯数字部分替换为对应的中文表达
    # 例如，将"1千5百"预处理为"一千五百"以便统一处理
    arabics = re.findall(r'\d+(?:\.\d+)?', chinese_str)
    for arabic in arabics:
        # 根据阿拉伯数字后面的单位来决定如何转换
        next_char_match = re.search(rf'{re.escape(arabic)}({CHINESE_NUMBER_CHARS}+)', chinese_str)
        if next_char_match:
            next_char = next_char_match.group(1)[0]  # 获取紧随阿拉伯数字后的第一个中文字符
            # 如果后面是单位（十、百、千、万、亿），则保留该结构
            chinese_str = chinese_str.replace(arabic, str(float(arabic)), 1)
        else:
            # 如果后面没有中文单位，则直接保留阿拉伯数字
            pass
    
    # 处理中文数字
    total = 0
    temp = 0
    
    # 替换一些特殊写法
    chinese_str = chinese_str.replace('零', '')
    
    # 处理亿以上的情况
    if '亿' in chinese_str or '億' in chinese_str:
        parts = re.split(r'亿|億', chinese_str)
        if parts[0]:  # 亿以上的部分
            # 递归处理亿以上部分，确保正确处理"一亿"这样的表达
            prefix = parts[0].strip()
            if prefix in CHINESE_DIGITS or prefix.isdigit():
                # 如果只是单个数字，比如"一亿"，直接乘以1亿
                prefix_val = float(prefix) if prefix.isdigit() else CHINESE_DIGITS[prefix]
                total += prefix_val * 100000000
            else:
                # 复杂表达，如"一千二百亿"
                total += chinese_to_num(prefix) * 100000000
        if len(parts) > 1 and parts[1]:  # 亿以下的部分
            chinese_str = parts[1]
        else:
            return total
    
    # 处理万以上的情况
    if '万' in chinese_str or '萬' in chinese_str:
        parts = re.split(r'万|萬', chinese_str)
        if parts[0]:  # 万以上的部分
            # 递归处理万以上部分，确保正确处理"一万"这样的表达
            prefix = parts[0].strip()
            if prefix in CHINESE_DIGITS or prefix.isdigit():
                # 如果只是单个数字，比如"一万"，直接乘以1万
                prefix_val = float(prefix) if prefix.isdigit() else CHINESE_DIGITS[prefix]
                total += prefix_val * 10000
            else:
                # 复杂表达，如"一千二百万"
                total += chinese_to_num(prefix) * 10000
        if len(parts) > 1 and parts[1]:  # 万以下的部分
            chinese_str = parts[1]
        else:
            return total
    
    # 处理千、百、十和阿拉伯数字
    result = 0
    i = 0
    while i < len(chinese_str):
        char = chinese_str[i]
        # 处理阿拉伯数字序列
        if char.isdigit():
            num_start = i
            while i < len(chinese_str) and (chinese_str[i].isdigit() or chinese_str[i] == '.'):
                i += 1
            temp = float(chinese_str[num_start:i])
            # 如果阿拉伯数字后面没有单位，直接加到结果中
            if i >= len(chinese_str) or chinese_str[i] not in CHINESE_DIGITS or CHINESE_DIGITS[chinese_str[i]] < 10:
                result += temp
                temp = 0
            continue
        
        if char in CHINESE_DIGITS:
            value = CHINESE_DIGITS[char]
            if value >= 10:  # 是单位
                if temp == 0:
                    temp = 1
                result += temp * value
                temp = 0
            else:  # 是数字
                temp = temp * 10 + value if temp >= 10 else value
        i += 1
        
    result += temp
    return total + result

def extract_numbers(text: str) -> List[Tuple[str, float]]:
    """
    从文本中提取所有可能的数字表达（包括阿拉伯数字、中文数字和混合形式）
    
    Args:
        text: 要处理的文本
        
    Returns:
        List[Tuple[str, float]]: 提取的数字表达及其转换后的数值列表
    """
    results = []
    
    # 匹配所有可能的数字表达
    matches = re.finditer(COMPLEX_NUMBER_PATTERN, text)
    
    for match in matches:
        matched_text = match.group()
        value = chinese_to_num(matched_text)
        results.append((matched_text, value))
        
    return results

if __name__ == "__main__":
    # 测试用例
    test_cases = [
        ("一千二百三十四", 1234),
        ("一亿两千万", 120000000),
        ("1千5百", 1500),
        ("2万3千", 23000),
        ("两亿五千万六千七百八十九", 250006789),
        ("三百零五", 305),
        ("十一", 11),
        ("零", 0),
        ("9527", 9527),
        ("23.12", 23.12),
        ("一万二千三百四十五点六七", 12345.67),
    ]
    
    print("中文数字转换测试：")
    for test_str, expected in test_cases:
        result = chinese_to_num(test_str)
        print(f"{test_str} -> {result} {'✓' if result == expected else f'✗ (期望值: {expected})'}")
    
    print("\n文本提取数字测试：")
    text_samples = [
        "我有一千五百元钱",
        "这本书售价35元",
        "这家公司市值两亿三千万美元",
        "房间里有2千3百个箱子和15个袋子",
        "项目预算为3亿4千5百万人民币",
        "1百元能够买什么",
        "108块5毛五能够买什么",
    ]
    
    for text in text_samples:
        numbers = extract_numbers(text)
        print(f"文本: {text}")
        print(f"提取结果: {numbers}")
        print("-" * 40)
