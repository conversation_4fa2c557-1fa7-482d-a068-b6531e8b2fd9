"""
通用工具类和函数，用于所有抽取器共享
"""
from typing import Dict, Tuple, List, Any, Optional, Union, Literal

def create_result(
    match: str, 
    position: Tuple[int, int],
    slot: Optional[str],
    slot_value: Optional[str],
    value: Optional[float] = None,
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
) -> Dict[str, Any]:
    """创建标准化的结果对象

    Args:
        match: 匹配到的原文文本
        position: 在原文中的位置 (start, end)
        slot: 槽位类型，如'time_range', 'number', '<keyword>'等
        slot_value: 槽位值，如'time_range', 'amount_range', 'specific_amount'等
        value: 具体数值，对于具体值类型结果
        min_value: 范围最小值，对于范围类型结果
        max_value: 范围最大值，对于范围类型结果

    Returns:
        Dict[str, Any]: 标准格式的结果字典
    """
    result = {
        "match": match,
        "position": position,
        "slot": slot,
        "slot_value": slot_value
    }
    
    # 添加范围值（如果有其中一个值）
    if min_value is not None or max_value is not None:
        result["minValue"] = min_value
        result["maxValue"] = max_value

    # 添加具体值（如果有）
    if value is not None:
        result["value"] = value
        
    return result
