"""
收益率抽取器

专注于提取金融文本中的收益率信息

输入格式:
    text: str - 包含收益率信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的收益率信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'rate_range' 或 'specific_rate'
        "minValue": float,       # 范围最小值（小数形式，如0.05表示5%）
        "maxValue": float,       # 范围最大值（小数形式），可能为 None
    }
"""

import re   
from typing import Dict, List, Any, Tuple, Pattern, Optional
from app.utils.extractor.shared.shared_utils import create_result, logger
from app.utils.extractor.num.num_utils import (
    chinese_to_num,
    RATE_UNIT_EXPR, RATE_PERIOD_PATTERN, RATE_RANGE_CONNECTOR,
    RATE_APPROXIMATE_PREFIX, RATE_APPROXIMATE_SUFFIX,
    RATE_UPPER_LIMIT_PREFIX, RATE_UPPER_LIMIT_SUFFIX,
    RATE_LOWER_LIMIT_PREFIX, RATE_LOWER_LIMIT_SUFFIX,
    SPECIFIC_RATE, RATE_RANGE, COMPLEX_NUMBER_PATTERN, RATE_UNIT_PATTERN,
    RateUnitHandler, RateModifierHandler, PERCENT_PATTERN
)

# 收益率超级模式：统一匹配各类收益率表达式
RATE_SUPER_PATTERN_STR = fr'''
(?: 
  # 近似前缀修饰词（可选）
  (?P<approximate_prefix>{RATE_APPROXIMATE_PREFIX})?
  
  # 上限前缀修饰词（可选）
  (?P<upper_limit_prefix>{RATE_UPPER_LIMIT_PREFIX})?
  
  # 下限前缀修饰词（可选）
  (?P<lower_limit_prefix>{RATE_LOWER_LIMIT_PREFIX})?
  
  # 可选周期（非捕获，实际周期会在rate_unit中捕获）
  (?:{RATE_PERIOD_PATTERN})?
  
  (?:
    # 格式1：数字+单位+连接词+数字+单位（确保两边都有单位或周期词）
    (?P<rate_unit1>{RATE_UNIT_EXPR})
    (?P<connector>{RATE_RANGE_CONNECTOR})
    (?P<rate_unit2>{RATE_UNIT_EXPR})
    |
    # 格式2：单个收益率单元（必须包含单位或周期词）
    (?P<rate_unit>{RATE_UNIT_EXPR})
    |
    # 格式3：数字+连接词+数字+单位（只有一个单位在最后）
    (?P<complex_num1>{COMPLEX_NUMBER_PATTERN})
    (?P<connector2>{RATE_RANGE_CONNECTOR})
    (?P<complex_num2>{COMPLEX_NUMBER_PATTERN})
    (?P<unit_pattern>{RATE_UNIT_PATTERN})
    |
    # 格式4：百分之X到百分之Y
    (?P<percent_expr1>{PERCENT_PATTERN})
    (?P<connector3>{RATE_RANGE_CONNECTOR})
    (?P<percent_expr2>{PERCENT_PATTERN})
    |
    # 格式5：百分之X
    (?P<percent_expr>{PERCENT_PATTERN})
  )
  
  # 上限后缀修饰词（可选）
  (?P<upper_limit_suffix>{RATE_UPPER_LIMIT_SUFFIX})?
  
  # 下限后缀修饰词（可选）
  (?P<lower_limit_suffix>{RATE_LOWER_LIMIT_SUFFIX})?
  
  # 近似后缀修饰词（可选）
  (?P<approximate_suffix>{RATE_APPROXIMATE_SUFFIX})?
)
'''

# 编译正则表达式
RATE_SUPER_PATTERN = re.compile(RATE_SUPER_PATTERN_STR, re.VERBOSE)

class RateExtractor:
    """
    收益率抽取器
    专注于从文本中提取收益率信息（通常为年化收益率、月化收益率等带有百分比的值）
    """
    
    def extract_rate(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取收益率信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的收益率信息列表
        """
        try:
            results = []
            covered_positions = set()  # 记录已覆盖的位置
            # 处理统一收益率超级模式
            for match in RATE_SUPER_PATTERN.finditer(text):
                result = self.process_unified_rate(match)
                if result and not self._is_position_covered(match.start(), match.end(), covered_positions):
                    self._mark_covered_positions(match.start(), match.end(), covered_positions)
                    results.append(result)
            
            # 过滤和标准化提取的值
            return self._filter_values(results)
            
        except Exception as e:
            logger.warning(f"[RATE_EXTRACTOR] Warning: {e.__class__.__name__}: {e}, 原始文本: '{text}'")
            return []
    
    def process_unified_rate(self, match) -> Optional[Dict[str, Any]]:
        """处理统一收益率模式的匹配结果"""
        match_text = match.group(0)
        
        # 提取命名捕获组
        components = {name: value for name, value in match.groupdict().items() if value is not None}
        min_value = None
        max_value = None
        
        try:
            # 处理连接词形式的收益率范围（如"5%到8%"）
            if 'rate_unit1' in components and 'rate_unit2' in components:
                value1 = RateUnitHandler.normalize_rate(components['rate_unit1'])
                value2 = RateUnitHandler.normalize_rate(components['rate_unit2'])
                min_value = min(value1, value2)
                max_value = max(value1, value2)
                slot_value = RATE_RANGE
            
            # 处理数字+连接词+数字+单位的形式（如"5-10%"）
            elif 'complex_num1' in components and 'complex_num2' in components and 'unit_pattern' in components:
                # 从文本中解析两个数字
                num1 = chinese_to_num(components['complex_num1'])
                num2 = chinese_to_num(components['complex_num2'])
                
                # 处理单位
                unit = components['unit_pattern']
                
                # 将数值转换为小数形式
                value1 = RateUnitHandler.to_decimal(num1, unit)
                value2 = RateUnitHandler.to_decimal(num2, unit)
                
                # 确定最小值和最大值
                min_value = min(value1, value2)
                max_value = max(value1, value2)
                slot_value = RATE_RANGE
            
            # 处理单个收益率单元（如"5%"）
            elif 'rate_unit' in components:
                value = RateUnitHandler.normalize_rate(components['rate_unit'])
                # 如果没有上下限修饰词和近似修饰词，则认为是单一收益率
                if not RateModifierHandler.has_modifier(match_text):
                    return create_result(
                        match=match_text,
                        position=(match.start(), match.end()),
                        slot="number",
                        slot_value=SPECIFIC_RATE,
                        min_value=value,
                        max_value=value
                    )
                else:
                    min_value = value
                    max_value = value
                    slot_value = RATE_RANGE
            
            # 处理百分之X格式
            elif 'percent_expr' in components:
                # 处理"百分之X"格式
                percent_text = components['percent_expr']
                
                number_value = chinese_to_num(percent_text)
                
                min_value = number_value
                max_value = number_value
                slot_value = SPECIFIC_RATE
                
                # 如果有修饰词，则应用修饰词调整范围
                if RateModifierHandler.has_modifier(match_text):
                    slot_value = RATE_RANGE
            
            # 处理百分之X到百分之Y格式
            elif 'percent_expr1' in components and 'percent_expr2' in components:
                # 处理"百分之X"格式
                percent_text1 = components['percent_expr1']
                percent_text2 = components['percent_expr2']
                
                number_value1 = chinese_to_num(percent_text1)
                number_value2 = chinese_to_num(percent_text2)
                
                min_value = min(number_value1, number_value2)
                max_value = max(number_value1, number_value2)
                slot_value = RATE_RANGE
            
            # 应用修饰词的影响（如"至少"、"不超过"等）
            if min_value is not None or max_value is not None:
                # 如果有上下限修饰词，应用修饰词调整范围
                min_value, max_value = RateModifierHandler.apply_modifiers(
                    min_value if min_value is not None else max_value,
                    max_value if max_value is not None else min_value,
                    match_text
                )
                
                # 创建结果
                return create_result(
                    match=match_text,
                    position=(match.start(), match.end()),
                    slot="number",
                    slot_value=slot_value,
                    min_value=min_value,
                    max_value=max_value
                )
                
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] Warning: {e}，匹配文本: '{match_text}'")
        except Exception as e:
            logger.warning(f"[RATE_EXTRACTOR] Warning: {e.__class__.__name__}: {e}")
            
        return None

    def _filter_values(self, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤无效的收益率值"""
        filtered_values = []
        
        for value in values:
            # 获取最小值和最大值
            min_value = value.get("minValue")
            max_value = value.get("maxValue")
            
            # 跳过无效数据情况
            if min_value is None and max_value is None:
                continue
                
            # 检查有效值是否在合理范围内（不超过1.0，即100%）
            if min_value is not None and min_value > 1.0:
                continue
                
            if max_value is not None and max_value > 1.0:
                continue
                
            # 检查最小值是否非负
            if min_value is not None and min_value < 0:
                continue
                
            # 如果最小值和最大值都存在，确保最大值大于等于最小值
            if min_value is not None and max_value is not None and max_value < min_value:
                continue
                
            # 通过所有检查，添加到结果列表
            filtered_values.append(value)
                
        return filtered_values
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos) 