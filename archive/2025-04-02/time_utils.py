"""
时间抽取相关常量和通用工具类
"""
from typing import Tuple, List
import re
from app.utils.extractor.num.num_utils import chinese_to_num, COMPLEX_NUMBER_PATTERN

# =========== 基础常量 ===========
# 中文数字映射
CHINESE_DIGITS = {
    '零': 0, '〇': 0, '0': 0,
    '一': 1, '壹': 1, '1': 1,
    '二': 2, '贰': 2, '兩': 2, '两': 2, '2': 2,
    '三': 3, '叁': 3, '3': 3,
    '四': 4, '肆': 4, '4': 4,
    '五': 5, '伍': 5, '5': 5,
    '六': 6, '陆': 6, '陸': 6, '6': 6,
    '七': 7, '柒': 7, '7': 7,
    '八': 8, '捌': 8, '8': 8,
    '九': 9, '玖': 9, '9': 9,
    '十': 10, '拾': 10,
    '百': 100, '佰': 100,
    '千': 1000, '仟': 1000,
    '万': 10000, '萬': 10000,
    '亿': 100000000, '億': 100000000,
    '半': 0.5  # 添加"半"对应的数值
}

# 时间单位定义
class TimeUnits:
    """时间单位定义类"""
    
    # 时间单位映射（原始单位 -> 标准单位）
    UNIT_MAPPING = {
        # 年相关
        "年": "年", "年份": "年",
        # 月相关
        "月": "月", "个月": "月", "個月": "月", "月份": "月", "个月份": "月",
        # 周相关
        "周": "周", "星期": "周", "礼拜": "周", "週": "周", "个周": "周", "个星期": "周", "個周": "周",
        # 天相关
        "天": "天", "日": "天", "号": "天", "號": "天", "个工作日": "天", "工作日": "天",
        # 季度相关
        "季度": "季度", "季": "季度", "个季度": "季度", "个季": "季度",
    }
    
    # 时间单位转换为天数的比例
    DAYS_CONVERSION = {
        "年": 366,
        "月": 30, 
        "季度": 90,
        "周": 7,
        "天": 1
    }
    
    @classmethod
    def get_all_units(cls) -> List[str]:
        """获取所有时间单位"""
        return list(cls.UNIT_MAPPING.keys())
    
    @classmethod
    def get_standard_unit(cls, unit: str) -> str:
        """获取标准化的时间单位"""
        return cls.UNIT_MAPPING.get(unit, unit)
    
    @classmethod
    def get_pattern(cls) -> str:
        """获取时间单位的正则表达式模式"""
        return f"({'|'.join(cls.get_all_units())})"

# 时间单位模式（使用TimeUnits类中的定义）
TIME_UNIT_PATTERN = TimeUnits.get_pattern()

# 槽位类型
RELATIVE_TIME_RANGE = "relative_time_range"
DURATION_TIME_RANGE = "duration_time_range"

# 时间范围连接词
TIME_RANGE_CONNECTOR = r'(到|至|~|-|—|－|→|、|和|与|或|及|–|⁓|‐|‑|‒|∼|～|∿|➝|➞|⟶|⇒|_|⁃|…)'

# 复合时间连接词：用于连接多个时间单位形成复合时间
TIME_COMPOUND_CONNECTOR = r'(零|又|和|加|以及|再)'

# 半单位模式（"半年"、"半个月"等）
HALF_UNIT_PATTERN = fr'半(?:个|個)?({TIME_UNIT_PATTERN})'

# 单位加半模式（"一年半"、"两月半"等）
UNIT_HALF_PATTERN = fr'({COMPLEX_NUMBER_PATTERN})({TIME_UNIT_PATTERN})半'

# 定义基本时间单元模式
TIME_UNIT_EXPR = fr'''
(?:
  # 复合时间（如"1年3个月"）
  (?:{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN}
    (?:{TIME_COMPOUND_CONNECTOR}?{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN})+)
  |
  # 单位加半表达式（如"一年半"）
  (?:{UNIT_HALF_PATTERN})
  |
  # 半单位表达式（如"半年"、"半个月"）
  (?:{HALF_UNIT_PATTERN})
  |
  # 简单时间单位（数字+单位）
  (?:{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN})
  
)
'''
# =========== 持续时间提取器常量 ===========

# 近似修饰前缀：出现在时间单位前的近似修饰词
DURATION_APPROXIMATE_PREFIX = r'(大约|大概|约|约为|接近|差不多|估计|粗略|基本|大致|几乎|将近|可能|或许|差点|近|将|快|近乎)'

# 近似修饰后缀：出现在时间单位后的近似修饰词
DURATION_APPROXIMATE_SUFFIX = r'(左右|上下|内外|之间|出头|之久|多|许|不到|不足|开外|有余|上下浮动|前后|上下波动|大概)'

# 其他修饰后缀：集合了各类修饰词，用于模式匹配但不影响值的计算
DURATION_OTHER_SUFFIX = r'(的时间|的期限|的期间|的周期|时间|期限|期间|周期|的跨度|的长度|的过程|的进程|的阶段|的间隔|的持续时间|的范围|的区间)'

# 上限前缀修饰词：放在时间单位前，表示时间的最大限制
DURATION_UPPER_LIMIT_PREFIX = r'(不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|低于|低於|短於|短于|不会超过|不会多于|不会多於|充其量|至多|最长|最长不超过|上限是|不会超出|最大|最大不超过|控制在)'

# 上限后缀修饰词：放在时间单位后，表示时间的最大限制
DURATION_UPPER_LIMIT_SUFFIX = r'(内|以下|以内|之内|及以下|及以内|及之内|未满|不到|不足|为限|为界|为上限|内完成|内结束|内搞定|内完工|内解决)'

# 下限前缀修饰词：放在时间单位前，表示时间的最小限制
DURATION_LOWER_LIMIT_PREFIX = r'(最少|至少|不少于|不少於|多于|多於|起码|高于|高於|长于|长於|不低于|不低於|不短于|不短於|至少需要|需要|须|必须|要|怎么也得|最起码|最低|底线是|不会少于|下限是|最小)'

# 下限后缀修饰词：放在时间单位后，表示时间的最小限制
DURATION_LOWER_LIMIT_SUFFIX = r'(以上|之上|及以上|及之上|开外|起|起步|打底|为下限|为基础|为底线|以外|为起点|或更长|或更多|才可以|往上|或更久|以后)'

# 中缀模式特殊前缀
DURATION_FIRST_PREFIX = r'(短则|至少|最少|最短|短期内|短期来看|短的话|往短了说|保守估计|保守来看|最快|快则|短期|初期|早期|前期|开始阶段)'

# 中缀模式特殊后缀
DURATION_SECOND_PREFIX = r'(长则|最多|最长|长期来看|长的话|往长了说|乐观估计|乐观来看|最慢|慢则|长期|后期|晚期|末期|结束阶段)'

# 定义中缀分隔符
DURATION_INFIX_SEPARATOR = r'(?:[,，;；、:：]|\s+|而|但|但是|\n|\r\n)?\s*'

# 短期
SHORT_TERM_PATTERN = r'(短期不用|短期|较短|短时间|一阵子|一小段时间|短期内)'

# 中期
MIDDLE_TERM_PATTERN = r'(中短期|最近一段时间不用|可以放几个月|最近几个月不用|暂时不需要用|中期|适中时间|不长不短|过渡期|中等时间|一段时期)'

# 长期
LONG_TERM_PATTERN = r'(较长|长期不用|可以放久一点|很久不用|期限久一点|长一些|长期|最近几年都不用)'

# T+0
T_PLUS_0_PATTERN = r'(T\+0|T0|T0交易|T0赎回)'

# T+1
T_PLUS_1_PATTERN = r'(T\+1|T1|T1赎回)'

# =========== 公共数据结构定义 ===========

class TimeUnitHandler:
    """时间单位处理器"""
    
    @classmethod
    def standardize(cls, unit: str) -> str:
        """标准化时间单位"""
        return TimeUnits.get_standard_unit(unit)
    
    @classmethod
    def to_days(cls, value: float, unit: str) -> float:
        """将时间单位转换为天数"""
        standard_unit = cls.standardize(unit)
        return value * TimeUnits.DAYS_CONVERSION.get(standard_unit, 1)
    
    @classmethod
    def parse_number_and_unit(cls, text: str) -> Tuple[float, str]:
        """从文本中解析数字和单位"""
        # 提取数字
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        number_text = number_match.group(0) if number_match else "1"
        number = chinese_to_num(number_text)
        
        # 提取单位
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else "天"
        
        # 处理"半"字
        if "半" in text:
            if text.startswith("半"):
                number = 0.5
            elif "半" in text and number > 0:
                number += 0.5
                
        return number, unit
        
class TimeModifierHandler:
    """时间修饰词处理器"""
    
    @staticmethod
    def has_upper_limit_modifier(text: str) -> bool:
        """检查是否有上限修饰词"""
        return bool(re.search(fr'({DURATION_UPPER_LIMIT_PREFIX}|{DURATION_UPPER_LIMIT_SUFFIX})', text))
    
    @staticmethod
    def has_lower_limit_modifier(text: str) -> bool:
        """检查是否有下限修饰词"""
        return bool(re.search(fr'({DURATION_LOWER_LIMIT_PREFIX}|{DURATION_LOWER_LIMIT_SUFFIX})', text))
    
    @classmethod
    def apply_modifiers(cls, min_value: float, max_value: float, text: str) -> Tuple[float, float]:
        """
        应用修饰词调整时间范围
        
        Args:
            min_value: 初始最小值（天数）
            max_value: 初始最大值（天数）
            text: 原始文本
            
        Returns:
            Tuple[float, float]: 调整后的(最小值, 最大值)，最大值可能为None表示无上限
        """
        # 检查是否有各类修饰词
        has_lower_limit = cls.has_lower_limit_modifier(text)
        has_upper_limit = cls.has_upper_limit_modifier(text)
        
        # 情况1: 单一值 (min_value == max_value)
        if min_value == max_value:
            base_days = min_value
            
            # 下限修饰词：如"至少3个月"，表示最小值是3个月，最大值无限
            if has_lower_limit:
                min_value = base_days
                max_value = None
            
            # 上限修饰词：如"不超过3个月"，表示最大值是3个月，最小值为0
            elif has_upper_limit:
                min_value = 0
                max_value = base_days
                
        # 情况2: 范围值 (min_value != max_value)
        else:
            # 处理下限修饰词：例如"至少3-6个月"，表示最小值是6个月，最大值无限
            if has_lower_limit:
                min_value = max_value  # 取范围中的最大值作为下限
                max_value = None       # 无上限
            
            # 处理上限修饰词：例如"不超过3-6个月"，表示最大值是6个月，最小值为0
            elif has_upper_limit:
                min_value = 0          # 最小值设为0
                # max_value保持不变
        
        return min_value, max_value
