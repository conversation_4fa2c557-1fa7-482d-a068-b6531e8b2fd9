"""
金额抽取器

专注于提取金融文本中的金额值

输入格式:
    text: str - 包含金额信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的金额信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,                 # 槽位名称
        "slot_value": str,       # 槽位值类型：'amount_range' 或 'specific_amount'
        # 对于 amount_range:
        "minValue": float,       # 范围最小值（元），可能为 None
        "maxValue": float,       # 范围最大值（元），可能为 None
        # 对于 specific_amount:
        "value": float           # 具体金额值（元）
    }
"""

import re
from typing import Dict, List, Any, Optional
from app.utils.extractor.num.num_utils import (
    COMPLEX_NUMBER_PATTERN, chinese_to_num,
    AMOUNT_UNIT_PATTERN, AMOUNT_UNIT_EXPR, AMOUNT_RANGE_CONNECTOR,
    AMOUNT_APPROXIMATE_PREFIX, AMOUNT_APPROXIMATE_SUFFIX,
    AMOUNT_UPPER_LIMIT_PREFIX, AMOUNT_UPPER_LIMIT_SUFFIX,
    AMOUNT_LOWER_LIMIT_PREFIX, AMOUNT_LOWER_LIMIT_SUFFIX,
    SPECIFIC_AMOUNT, AMOUNT_RANGE, 
    AmountUnitHandler, AmountModifierHandler,
)
from app.utils.extractor.shared.shared_utils import create_result, logger

# 金额超级模式：统一匹配各类金额表达式
AMOUNT_SUPER_PATTERN_STR = fr'''
(?: 
  # 近似前缀修饰词（可选）
  (?P<approximate_prefix>{AMOUNT_APPROXIMATE_PREFIX})?
  
  # 上限前缀修饰词（可选）
  (?P<upper_limit_prefix>{AMOUNT_UPPER_LIMIT_PREFIX})?
  
  # 下限前缀修饰词（可选）
  (?P<lower_limit_prefix>{AMOUNT_LOWER_LIMIT_PREFIX})?
  
  (?:
    # 格式1：数字+单位+连接词+数字+单位
    (?P<amount_unit1>{AMOUNT_UNIT_EXPR})
    (?P<connector>{AMOUNT_RANGE_CONNECTOR})
    (?P<amount_unit2>{AMOUNT_UNIT_EXPR})
    |
    # 格式2：单个金额单元
    (?P<amount_unit>{AMOUNT_UNIT_EXPR})
    |
    # 格式3：数字+连接词+数字+单位
    (?P<number1>{COMPLEX_NUMBER_PATTERN})
    (?P<connector2>{AMOUNT_RANGE_CONNECTOR})
    (?P<number2>{COMPLEX_NUMBER_PATTERN})
    (?P<shared_unit>{AMOUNT_UNIT_PATTERN})
    |
    # 格式4: 纯数字格式(无需单位)
    (?P<number_only>{COMPLEX_NUMBER_PATTERN})
  )
  
  # 上限后缀修饰词（可选）
  (?P<upper_limit_suffix>{AMOUNT_UPPER_LIMIT_SUFFIX})?
  
  # 下限后缀修饰词（可选）
  (?P<lower_limit_suffix>{AMOUNT_LOWER_LIMIT_SUFFIX})?
  
  # 近似后缀修饰词（可选）
  (?P<approximate_suffix>{AMOUNT_APPROXIMATE_SUFFIX})?
)
'''

# 编译正则表达式
AMOUNT_SUPER_PATTERN = re.compile(AMOUNT_SUPER_PATTERN_STR, re.VERBOSE)

class AmountExtractor:
    """
    金额抽取器
    专注于从文本中提取金额值信息（通常为元、万元等带有货币单位的值）
    """
    
    # 纯数字被视为金额的最小阈值
    MIN_NUMBER_THRESHOLD = 1000
    
    def extract_amount(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取金额信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的金额信息列表
        """
        try:
            results = []
            covered_positions = set()  # 记录已覆盖的位置
            
            # 处理统一金额超级模式
            for match in AMOUNT_SUPER_PATTERN.finditer(text):
                result = self.process_unified_amount(match)
                if result and not self._is_position_covered(match.start(), match.end(), covered_positions):
                    self._mark_covered_positions(match.start(), match.end(), covered_positions)
                    results.append(result)
            
            # 过滤和标准化提取的值
            return self._filter_values(results)
            
        except Exception as e:
            logger.warning(f"[AMOUNT_EXTRACTOR] Warning: {e.__class__.__name__}: {e}, 原始文本: '{text}'")
            return []
    
    def process_unified_amount(self, match) -> Optional[Dict[str, Any]]:
        """处理统一金额模式的匹配结果"""
        match_text = match.group(0)
        
        # 提取命名捕获组
        components = {name: value for name, value in match.groupdict().items() if value is not None}

        # 处理不同的金额格式
        if 'amount_unit1' in components and 'amount_unit2' in components:
            return self._process_amount_range_with_units(match_text, components, match)
        elif 'amount_unit' in components:
            return self._process_single_amount_unit(match_text, components, match)
        elif 'number1' in components and 'number2' in components and 'shared_unit' in components:
            return self._process_range_with_shared_unit(match_text, components, match)
        elif 'number_only' in components:
            return self._process_number_only(match_text, components, match)
                
        return None
    
    def _process_amount_range_with_units(self, match_text: str, components: Dict, match) -> Optional[Dict[str, Any]]:
        """处理带有两个金额单位的范围表达式"""
        value1 = AmountUnitHandler.normalize_amount(components['amount_unit1'])
        value2 = AmountUnitHandler.normalize_amount(components['amount_unit2'])
        min_value = min(value1, value2)
        max_value = max(value1, value2)
        
        return self._create_amount_range_result(match_text, min_value, max_value, match)
    
    def _process_single_amount_unit(self, match_text: str, components: Dict, match) -> Optional[Dict[str, Any]]:
        """处理单个金额单元"""
        value = AmountUnitHandler.normalize_amount(components['amount_unit'])
     
        # 如果没有修饰词，则认为是单一金额
        if not AmountModifierHandler.has_modifier(match_text):
            return create_result(
                match=match_text,
                position=(match.start(), match.end()),
                slot="number",
                slot_value=SPECIFIC_AMOUNT,
                value=value
            )
        
        # 有修饰词的情况
        return self._create_amount_range_result(match_text, value, value, match)
    
    def _process_range_with_shared_unit(self, match_text: str, components: Dict, match) -> Optional[Dict[str, Any]]:
        """处理共享单位的范围表达"""
        number1 = chinese_to_num(components['number1'])
        number2 = chinese_to_num(components['number2'])
        unit = components['shared_unit']
        
        value1 = AmountUnitHandler.to_yuan(number1, unit)
        value2 = AmountUnitHandler.to_yuan(number2, unit)
        
        min_value = min(value1, value2)
        max_value = max(value1, value2)
        
        return self._create_amount_range_result(match_text, min_value, max_value, match)
    
    def _process_number_only(self, match_text: str, components: Dict, match) -> Optional[Dict[str, Any]]:
        """处理纯数字格式"""
        number_str = components['number_only']
        
        # 检查是否以单位开头
        for unit in ['万', '萬', 'w', "W"]:
            if number_str.lower().startswith(unit):
                return None
        
        value = chinese_to_num(number_str)
        has_modifier = AmountModifierHandler.has_modifier(match_text)
        
        # 如果没有修饰词，检查数值是否超过阈值
        if not has_modifier and value < self.MIN_NUMBER_THRESHOLD:
            return None
        
        # 如果没有修饰词，并且数值足够大，认为是单一金额
        if not has_modifier:
            return create_result(
                match=match_text,
                position=(match.start(), match.end()),
                slot="number",
                slot_value=SPECIFIC_AMOUNT,
                value=value
            )
        
        # 有修饰词的情况
        return self._create_amount_range_result(match_text, value, value, match)
    
    def _create_amount_range_result(self, match_text: str, min_value: float, max_value: float, match) -> Dict[str, Any]:
        """创建金额范围结果"""
        # 应用修饰词的影响
        min_value, max_value = AmountModifierHandler.apply_modifiers(
            min_value if min_value is not None else max_value,
            max_value if max_value is not None else min_value,
            match_text
        )
        
        # 创建结果
        return create_result(
            match=match_text,
            position=(match.start(), match.end()),
            slot="number",
            slot_value=AMOUNT_RANGE,
            min_value=min_value,
            max_value=max_value
        )
    
    def _filter_values(self, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤无效的金额值"""
        filtered_values = []
        
        for value in values:
            is_valid = False
            
            if value["slot_value"] == SPECIFIC_AMOUNT:
                # 对于具体金额，值必须大于0
                if value.get("value", 0) > 0:
                    is_valid = True
            elif value["slot_value"] == AMOUNT_RANGE:
                # 对于金额范围，至少有一个边界值且有效
                min_val = value.get("minValue")
                max_val = value.get("maxValue")
                
                if (min_val is not None and min_val >= 0) or (max_val is not None and max_val > 0):
                    # 如果两个值都存在，确保范围有效
                    if min_val is not None and max_val is not None:
                        if max_val >= min_val:
                            is_valid = True
                    else:
                        is_valid = True
            
            if is_valid:
                filtered_values.append(value)
                
        return filtered_values
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos) 