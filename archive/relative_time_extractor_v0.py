"""
相对时间抽取器

专注于提取金融文本中的时间信息，抽取相对时间范围：

如"最近七天"、"过去三到六个月"、"今年以来"等

特别说明：
    1. 一年按366天计算，一个月按30天计算
    2. 相对时间范围的默认锚点是当前时间，除非有明确的时间锚点，当前时间锚点的天数默认为0，
       过去时间锚点的天数为负数，未来时间锚点的天数为正数。

输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'relative_time_range'
        "minValue": float,       # 范围最小值（天数），可能为 None，但不与maxValue同时为None
        "maxValue": float,       # 范围最大值（天数），可能为 None，但不与minValue同时为None
    }
"""

from typing import Dict, Tuple, List, Any, Optional, Literal
import re
from datetime import datetime, timedelta
import calendar
from .time_utils import (
    RELATIVE_TIME_RANGE, RELATIVE_TIME_SUPER_PATTERN_STR,
    YEAR_ANCHOR_PATTERN, MONTH_ANCHOR_PATTERN, QUARTER_ANCHOR_PATTERN, 
    WEEK_ANCHOR_PATTERN, DAY_ANCHOR_PATTERN,
    PAST_MODIFIERS, CURRENT_MODIFIERS, FUTURE_MODIFIERS, 
    TIME_POSITION_MODIFIERS, TIME_UNIT_WITH_NUMBER,
    TIME_RANGE_CONNECTOR, SPECIAL_UP_PATTERN, SPECIAL_FEW_DAYS_PATTERN, 
    FISCAL_YEAR_ANCHORS, TimeUnitHandler, get_last_day_of_month, 
    create_time_result, TimeMatch, TimeRange, TimeProcessor, TimeParserPipeline
)
from ..num.num_utils import chinese_to_num, COMPLEX_NUMBER_PATTERN


class TimeCalculator:
    """时间计算器，提供统一的时间计算功能"""
    
    def __init__(self):
        """初始化时间计算器，获取当前日期信息"""
        self.today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        self.current_year = self.today.year
        self.current_month = self.today.month
        self.current_day = self.today.day
        self.current_weekday = self.today.weekday()
        self.current_quarter = (self.current_month - 1) // 3 + 1
    
    def calculate_date_range_days(self, start_date: datetime, end_date: datetime) -> Tuple[float, float]:
        """计算日期范围相对今天的天数差"""
        start_days = (start_date - self.today).days
        end_days = (end_date - self.today).days
        
        # 如果结束日期在将来，但开始日期在过去或现在，则将结束日期设为今天（0）
        if end_days > 0 and start_days <= 0:
            end_days = 0
        
        return start_days, end_days
    
    def calculate_time_entity(self, 
                             period_type: Literal["year", "half_year", "quarter", "month", "week", "day", "fiscal_year"],
                             offsets: Dict[str, Any] = None) -> Tuple[float, float]:
        """统一计算时间实体的范围（天数）
        
        Args:
            period_type: 时间实体类型
            offsets: 包含各种偏移量的字典，可能包含：
                     year_offset, is_first_half, quarter_number, month_number, day_number, etc.
        
        Returns:
            Tuple[float, float]: 时间范围的最小和最大天数
        """
        if not offsets:
            offsets = {}
        
        year_offset = offsets.get('year_offset', 0)
        year = self.current_year + year_offset
        
        if period_type == "year":
            # 年份
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            
        elif period_type == "half_year":
            # 半年
            is_first_half = offsets.get('is_first_half', True)
            if is_first_half:
                start_date = datetime(year, 1, 1)
                end_date = datetime(year, 6, 30)
            else:
                start_date = datetime(year, 7, 1)
                end_date = datetime(year, 12, 31)
                
        elif period_type == "quarter":
            # 季度
            quarter = offsets.get('quarter_number', self.current_quarter)
            start_month = (quarter - 1) * 3 + 1
            end_month = quarter * 3
            
            start_date = datetime(year, start_month, 1)
            last_day = get_last_day_of_month(year, end_month)
            end_date = datetime(year, end_month, last_day)
            
        elif period_type == "month":
            # 月份
            month = offsets.get('month_number', self.current_month)
            month_offset = offsets.get('month_offset', 0)
            
            # 处理月份偏移
            if month_offset != 0:
                total_months = year * 12 + month + month_offset
                year = total_months // 12
                month = total_months % 12
                if month == 0:
                    month = 12
                    year -= 1
                    
            start_date = datetime(year, month, 1)
            last_day = get_last_day_of_month(year, month)
            end_date = datetime(year, month, last_day)
            
        elif period_type == "week":
            # 周
            week_offset = offsets.get('week_offset', 0)
            
            # 当前周的开始（周一）
            week_start = self.today - timedelta(days=self.current_weekday)
            
            # 计算目标周
            start_date = week_start + timedelta(weeks=week_offset)
            end_date = start_date + timedelta(days=6)
            
        elif period_type == "day":
            # 日期
            day = offsets.get('day_number', self.current_day)
            month = offsets.get('month_number', self.current_month)
            
            # 确保日期有效
            last_day = get_last_day_of_month(year, month)
            if day > last_day:
                day = last_day
                
            start_date = end_date = datetime(year, month, day)
            
        elif period_type == "fiscal_year":
            # 财年（通常从4月1日到次年3月31日，可根据实际需求调整）
            if self.current_month <= 3:
                start_date = datetime(year - 1, 4, 1)
                end_date = datetime(year, 3, 31)
            else:
                start_date = datetime(year, 4, 1)
                end_date = datetime(year + 1, 3, 31)
                
        return self.calculate_date_range_days(start_date, end_date)


class UnifiedRelativeTimeProcessor(TimeProcessor):
    """统一的相对时间处理器，处理大多数相对时间表达式"""
    
    def __init__(self, pattern: str, processor_name: str):
        """初始化相对时间处理器
        
        Args:
            pattern: 用于匹配相对时间的正则表达式
            processor_name: 处理器名称
        """
        super().__init__(pattern, processor_name, use_verbose=True)
        self.calculator = TimeCalculator()
    
    def extract_components(self, match) -> Optional[Dict[str, Any]]:
        """从匹配中提取组件
        
        Args:
            match: 正则表达式匹配对象
            
        Returns:
            Dict[str, Any]: 提取的组件字典，或None表示无法提取
        """
        text = match.group()
        components = {"text": text}
        
        # 提取类型和相关参数
        if self._match_past_time_with_number(text):
            # 过去时间：如"过去三个月"
            components["type"] = "past_relative"
            number, unit = TimeUnitHandler.parse_number_and_unit(text)
            components["number"] = number
            components["unit"] = unit
            
        elif self._match_future_time_with_number(text):
            # 未来时间：如"未来两周"
            components["type"] = "future_relative"
            number, unit = TimeUnitHandler.parse_number_and_unit(text)
            components["number"] = number
            components["unit"] = unit
            
        elif self._match_time_range(text):
            # 时间范围：如"三到六个月"
            components["type"] = "time_range"
            # 拆分范围
            parts = self._split_range(text)
            if parts:
                first_part, second_part = parts
                first_number, first_unit = TimeUnitHandler.parse_number_and_unit(first_part)
                second_number, second_unit = TimeUnitHandler.parse_number_and_unit(second_part)
                
                components["first_number"] = first_number
                components["first_unit"] = first_unit
                components["second_number"] = second_number
                components["second_unit"] = second_unit
            else:
                return None
            
        elif self._match_year_anchor(text):
            # 年份锚点：如"去年"、"2023年"
            components["type"] = "year_anchor"
            components["year_offset"] = self._extract_year_offset(text)
            
        elif self._match_half_year(text):
            # 半年：如"上半年"
            components["type"] = "half_year"
            components["is_first_half"] = "上半年" in text
            components["year_offset"] = self._extract_year_offset(text)
            
        elif self._match_quarter_anchor(text):
            # 季度：如"第一季度"、"本季度"
            components["type"] = "quarter_anchor"
            components["quarter_number"] = self._extract_quarter_number(text)
            components["year_offset"] = self._extract_year_offset(text)
            
        elif self._match_month_anchor(text):
            # 月份：如"上个月"、"本月"
            components["type"] = "month_anchor"
            month_number, month_offset = self._extract_month_component(text)
            components["month_number"] = month_number
            components["month_offset"] = month_offset
            
        elif self._match_week_anchor(text):
            # 周：如"本周"、"上周"
            components["type"] = "week_anchor"
            components["week_offset"] = self._extract_week_offset(text)
            
        elif self._match_day_anchor(text):
            # 日期：如"今天"、"明天"、"20日"
            components["type"] = "day_anchor"
            components["day_number"] = self._extract_day_number(text)
            
        elif self._match_year_month(text) or self._match_year_quarter(text) or self._match_month_day(text):
            # 复合时间表达：如"2023年3月"、"去年第四季度"、"3月15日"
            if self._match_year_month(text):
                components["type"] = "year_month"
                year_part, month_part = self._split_year_month(text)
                components["year_offset"] = self._extract_year_offset(year_part)
                month_number, _ = self._extract_month_component(month_part)
                components["month_number"] = month_number
                
            elif self._match_year_quarter(text):
                components["type"] = "year_quarter"
                year_part, quarter_part = self._split_year_quarter(text)
                components["year_offset"] = self._extract_year_offset(year_part)
                components["quarter_number"] = self._extract_quarter_number(quarter_part)
                
            elif self._match_month_day(text):
                components["type"] = "month_day"
                month_part, day_part = self._split_month_day(text)
                month_number, month_offset = self._extract_month_component(month_part)
                components["month_number"] = month_number
                components["month_offset"] = month_offset
                components["day_number"] = self._extract_day_number(day_part)
                
        else:
            # 无法识别的模式
            return None
            
        return components
    
    def process(self, match: TimeMatch) -> Optional[TimeRange]:
        """处理匹配结果，转换为时间范围
        
        Args:
            match: 时间匹配对象
            
        Returns:
            TimeRange: 时间范围对象，或None表示无法处理
        """
        components = match.components
        time_type = components.get("type", "")
        
        if time_type == "past_relative":
            # 过去时间
            number = components["number"]
            unit = components["unit"]
            days = TimeUnitHandler.to_days(number, unit)
            return TimeRange(-days, 0, RELATIVE_TIME_RANGE)
            
        elif time_type == "future_relative":
            # 未来时间
            number = components["number"]
            unit = components["unit"]
            days = TimeUnitHandler.to_days(number, unit)
            return TimeRange(0, days, RELATIVE_TIME_RANGE)
            
        elif time_type == "time_range":
            # 时间范围
            first_number = components["first_number"]
            first_unit = components["first_unit"]
            second_number = components["second_number"]
            second_unit = components["second_unit"]
            
            days1 = TimeUnitHandler.to_days(first_number, first_unit)
            days2 = TimeUnitHandler.to_days(second_number, second_unit)
            
            # 假设范围通常是从小到大，如果没有明确方向词
            # 默认假设是过去时间范围，所以转换为负数
            days1 = -days1
            days2 = -days2
            
            return TimeRange(min(days1, days2), max(days1, days2), RELATIVE_TIME_RANGE)
            
        elif time_type in ["year_anchor", "half_year", "quarter_anchor", "month_anchor", 
                          "week_anchor", "day_anchor", "year_month", "year_quarter", "month_day"]:
            # 时间锚点类型映射到计算器的period_type
            period_map = {
                "year_anchor": "year",
                "half_year": "half_year",
                "quarter_anchor": "quarter",
                "month_anchor": "month",
                "week_anchor": "week",
                "day_anchor": "day",
                "year_month": "month",
                "year_quarter": "quarter",
                "month_day": "day"
            }
            
            period_type = period_map[time_type]
            min_value, max_value = self.calculator.calculate_time_entity(period_type, components)
            return TimeRange(min_value, max_value, RELATIVE_TIME_RANGE)
            
        return None
    
    # 匹配函数
    def _match_past_time_with_number(self, text: str) -> bool:
        """匹配带数字的过去时间表达式"""
        return bool(re.search(fr'{PAST_MODIFIERS["prefix"]}的?{TIME_UNIT_WITH_NUMBER}', text))
    
    def _match_future_time_with_number(self, text: str) -> bool:
        """匹配带数字的未来时间表达式"""
        return bool(re.search(fr'{FUTURE_MODIFIERS["prefix"]}的?{TIME_UNIT_WITH_NUMBER}', text))
    
    def _match_time_range(self, text: str) -> bool:
        """匹配时间范围表达式"""
        return bool(re.search(fr'{TIME_UNIT_WITH_NUMBER}{TIME_RANGE_CONNECTOR}{TIME_UNIT_WITH_NUMBER}', text))
    
    def _match_year_anchor(self, text: str) -> bool:
        """匹配年份锚点"""
        return bool(re.search(YEAR_ANCHOR_PATTERN, text))
    
    def _match_half_year(self, text: str) -> bool:
        """匹配半年表达式"""
        return "上半年" in text or "下半年" in text
    
    def _match_quarter_anchor(self, text: str) -> bool:
        """匹配季度锚点"""
        return bool(re.search(QUARTER_ANCHOR_PATTERN, text))
    
    def _match_month_anchor(self, text: str) -> bool:
        """匹配月份锚点"""
        return bool(re.search(MONTH_ANCHOR_PATTERN, text))
    
    def _match_week_anchor(self, text: str) -> bool:
        """匹配周锚点"""
        return bool(re.search(WEEK_ANCHOR_PATTERN, text))
    
    def _match_day_anchor(self, text: str) -> bool:
        """匹配日期锚点"""
        return bool(re.search(DAY_ANCHOR_PATTERN, text))
    
    def _match_year_month(self, text: str) -> bool:
        """匹配年月组合"""
        return bool(re.search(fr'({YEAR_ANCHOR_PATTERN})的?({MONTH_ANCHOR_PATTERN})', text))
    
    def _match_year_quarter(self, text: str) -> bool:
        """匹配年季度组合"""
        return bool(re.search(fr'({YEAR_ANCHOR_PATTERN})的?({QUARTER_ANCHOR_PATTERN})', text))
    
    def _match_month_day(self, text: str) -> bool:
        """匹配月日组合"""
        return bool(re.search(fr'({MONTH_ANCHOR_PATTERN})的?({DAY_ANCHOR_PATTERN})', text))
    
    # 提取函数
    def _split_range(self, text: str) -> Optional[Tuple[str, str]]:
        """分割时间范围表达式"""
        connector_match = re.search(TIME_RANGE_CONNECTOR, text)
        if connector_match:
            connector_pos = connector_match.start()
            return text[:connector_pos], text[connector_pos + len(connector_match.group()):]
        return None
    
    def _split_year_month(self, text: str) -> Tuple[str, str]:
        """分割年月组合"""
        match = re.search(fr'({YEAR_ANCHOR_PATTERN})的?({MONTH_ANCHOR_PATTERN})', text)
        if match:
            return match.group(1), match.group(2)
        return "", ""
    
    def _split_year_quarter(self, text: str) -> Tuple[str, str]:
        """分割年季度组合"""
        match = re.search(fr'({YEAR_ANCHOR_PATTERN})的?({QUARTER_ANCHOR_PATTERN})', text)
        if match:
            return match.group(1), match.group(2)
        return "", ""
    
    def _split_month_day(self, text: str) -> Tuple[str, str]:
        """分割月日组合"""
        match = re.search(fr'({MONTH_ANCHOR_PATTERN})的?({DAY_ANCHOR_PATTERN})', text)
        if match:
            return match.group(1), match.group(2)
        return "", ""
    
    def _extract_year_offset(self, text: str) -> int:
        """提取年份偏移"""
        if "去年" in text:
            return -1
        elif "前年" in text:
            return -2
        elif "明年" in text:
            return 1
        elif "后年" in text:
            return 2
        elif "今年" in text or "本年" in text:
            return 0
            
        # 具体年份
        year_match = re.search(r'(\d{4})年', text)
        if year_match:
            year = int(year_match.group(1))
            return year - self.calculator.current_year
            
        return 0
    
    def _extract_quarter_number(self, text: str) -> int:
        """提取季度数字"""
        if "第一季" in text or "一季" in text:
            return 1
        elif "第二季" in text or "二季" in text:
            return 2
        elif "第三季" in text or "三季" in text:
            return 3
        elif "第四季" in text or "四季" in text:
            return 4
        elif "本季" in text or "这个季" in text:
            return self.calculator.current_quarter
        elif "上季" in text:
            return (self.calculator.current_quarter - 2) % 4 + 1
            
        return 0
    
    def _extract_month_component(self, text: str) -> Tuple[int, int]:
        """提取月份组件，返回(月份, 偏移量)"""
        month_number = 0
        month_offset = 0
        
        if "本月" in text or "这个月" in text:
            month_number = self.calculator.current_month
        elif "上个月" in text or "上一个月" in text or "上月" in text:
            month_offset = -1
            month_number = self.calculator.current_month - 1
            if month_number == 0:
                month_number = 12
        else:
            # 尝试解析具体月份
            month_match = re.search(r'(\d+|[一二三四五六七八九十]+)月', text)
            if month_match:
                month_str = month_match.group(1)
                month_number = int(chinese_to_num(month_str))
                
                # 默认当前年的月份
                if month_number < self.calculator.current_month:
                    month_offset = 0  # 不再默认为下一年的该月
                
        return month_number, month_offset
    
    def _extract_week_offset(self, text: str) -> int:
        """提取周偏移"""
        if "上周" in text or "上個星期" in text or "上个星期" in text:
            return -1
        elif "本周" in text or "这周" in text or "这个星期" in text:
            return 0
        elif "下周" in text or "下個星期" in text or "下个星期" in text:
            return 1
            
        return 0
    
    def _extract_day_number(self, text: str) -> int:
        """提取日期数字"""
        if "今天" in text:
            return self.calculator.current_day
        elif "昨天" in text:
            return self.calculator.current_day - 1
        elif "前天" in text:
            return self.calculator.current_day - 2
        elif "明天" in text:
            return self.calculator.current_day + 1
        elif "后天" in text:
            return self.calculator.current_day + 2
            
        # 尝试解析具体日期
        day_match = re.search(r'(\d+|[一二三四五六七八九十]+)(号|日)', text)
        if day_match:
            day_str = day_match.group(1)
            return int(chinese_to_num(day_str))
            
        return 0


class SpecialTimeProcessor(TimeProcessor):
    """处理特殊时间表达式的处理器"""
    
    def __init__(self, pattern: str, processor_name: str, time_type: str):
        """初始化特殊时间处理器
        
        Args:
            pattern: 用于匹配特殊时间的正则表达式
            processor_name: 处理器名称
            time_type: 特殊时间类型
        """
        super().__init__(pattern, processor_name, use_verbose=False)
        self.time_type = time_type
        self.calculator = TimeCalculator()
    
    def extract_components(self, match) -> Optional[Dict[str, Any]]:
        """从匹配中提取组件"""
        return {"text": match.group(), "type": self.time_type}
    
    def process(self, match: TimeMatch) -> Optional[TimeRange]:
        """处理特殊时间表达式"""
        time_type = match.components["type"]
        
        if time_type == "to_now":
            # "至今"、"迄今为止"等
            return TimeRange(-365 * 10, 0, RELATIVE_TIME_RANGE)  # 假设从很久以前到现在
        
        elif time_type == "few_days":
            # "前几天"
            return TimeRange(-7, -2, RELATIVE_TIME_RANGE)  # 假设为2-7天前
        
        elif time_type == "fiscal_year":
            # 财年
            min_value, max_value = self.calculator.calculate_time_entity("fiscal_year")
            return TimeRange(min_value, max_value, RELATIVE_TIME_RANGE)
            
        return None


class RelativeTimeExtractor:
    """相对时间抽取器，用于提取文本中的相对时间表达（如"上个月"、"今年"等）"""
    
    def __init__(self):
        """初始化相对时间抽取器"""
        # 创建处理器
        processors = [
            # 主要处理器 - 处理超级模式匹配的大部分表达式
            UnifiedRelativeTimeProcessor(RELATIVE_TIME_SUPER_PATTERN_STR, "unified_relative_time"),
            
            # 特殊处理器 - 处理特殊时间表达式
            SpecialTimeProcessor(SPECIAL_UP_PATTERN, "special_up_pattern", "to_now"),
            SpecialTimeProcessor(SPECIAL_FEW_DAYS_PATTERN, "special_few_days", "few_days"),
            SpecialTimeProcessor(FISCAL_YEAR_ANCHORS, "fiscal_year", "fiscal_year")
        ]
        
        # 创建解析管道
        self.pipeline = TimeParserPipeline(processors)
    
    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取相对时间表达式
        
        Args:
            text: 要处理的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的时间信息列表
        """
        return self.pipeline.process(text)