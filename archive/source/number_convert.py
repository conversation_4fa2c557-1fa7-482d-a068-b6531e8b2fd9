"""
数字转换工具
包含中文数字、阿拉伯数字和混合数字的转换功能
"""
from typing import Dict, List, Any
from decimal import Decimal
from resource.regex_patterns.num_patterns import (
    CHINESE_SIGN_LIST,
    CHINESE_PER_COUNTING_STRING_LIST,
    CHINESE_PER_COUNTING_SEG,
    CHINESE_PURE_NUMBER_LIST,
    CHINESE_PURE_COUNTING_UNIT_LIST,
    CHINESE_SIGN_DICT,
    CHINESE_CONNECTING_SIGN_DICT,
    TRADITIONAL_CONVERT_DICT,
    SPECIAL_TRADITIONAL_COUNTING_UNIT_CHAR_DICT,
    SPECIAL_NUMBER_CHAR_DICT,
    TAKING_DIGITS_RE,
    TAKING_CHINESE_DIGITS_MIX_RE,
    CHINESE_DIGITS,
    DIGITS_CHAR_CH_DICT
)

class ComplexNumberConvert:
    """复杂数字转换器，支持中文数字、阿拉伯数字和混合数字的转换"""
    
    @staticmethod
    def text_to_num(text: str) -> float:
        """
        将文本数字转换为阿拉伯数字
        
        支持复合中文数字和混合形式，如：
        - "一千二百三十四"
        - "一亿两千万"
        - "1千5百"
        - "2万3000"
        - "1000W"
        - "250w"
        - "100w5000" (100万5000)
        - "百分之五" (0.05)
        - "百分之十五点五" (0.155)
        - "2%"
        - "10 万" (100000) - 支持空格分隔的数字和单位
        - "1.5e3" (1500) - 支持科学计数法
        - "半" (0.5)
        
        Args:
            text: 数字字符串或混合数字字符串
            
        Returns:
            float: 转换后的数值
        """
        # 使用现有的转换函数
        result = take_number_from_string(text, percent_convert=True)
        if not result['digitsStringList']:
            return 0.0
        
        # 返回第一个转换结果
        try:
            return float(result['digitsStringList'][0])
        except (ValueError, IndexError):
            return 0.0

def core_chinese_to_digits(chinese_chars: str) -> str:
    """
    将中文数字转换为阿拉伯数字的核心函数
    
    Args:
        chinese_chars: 中文数字字符串
        
    Returns:
        str: 转换后的阿拉伯数字字符串
    """
    total = 0
    temp_val = ''  # 用以记录临时是否建议数字拼接的字符串 例如 三零万 的三零
    counting_unit = 1  # 表示单位：个十百千,用以计算单位相乘 例如八百万 百万是相乘的方法，但是如果万前面有 了一千八百万 这种，千和百不能相乘，要相加...
    counting_unit_from_string = [1]  # 原始字符串提取的单位应该是一个list  在计算的时候，新的单位应该是本次取得的数字乘以已经发现的最大单位，例如 4千三百五十万， 等于 4000万+300万+50万
    
    for i in range(len(chinese_chars) - 1, -1, -1):
        val = CHINESE_DIGITS.get(chinese_chars[i])
        if val is None:
            continue
            
        if val >= 10 and i == 0:  # 应对 十三 十四 十*之类，说明为十以上的数字，看是不是十三这种
            # 说明循环到了第一位 也就是最后一个循环 看看是不是单位开头
            # 取最近一次的单位
            if val > counting_unit:  # 如果val大于 contingUnit 说明 是以一个更大的单位开头 例如 十三 千二这种
                counting_unit = val  # 赋值新的计数单位
                total = total + val  # 总值等于  全部值加上新的单位 类似于13 这种
                counting_unit_from_string.append(val)
            else:
                counting_unit_from_string.append(val)
                # 计算用的单位是最新的单位乘以字符串中最大的原始单位  为了计算四百万这种
                counting_unit = max(counting_unit_from_string) * val
        elif val >= 10:
            if val > counting_unit:
                counting_unit = val
                counting_unit_from_string.append(val)
            else:
                counting_unit_from_string.append(val)
                # 计算用的单位是最新的单位乘以字符串中最大的原始单位 为了计算四百万这种
                counting_unit = max(counting_unit_from_string) * val
        else:
            if i > 0:
                # 如果下一个不是单位 则本次也是拼接
                if CHINESE_DIGITS.get(chinese_chars[i-1], 0) < 10:
                    temp_val = str(val) + temp_val
                else:
                    # 说明已经有大于10的单位插入 要数学计算了
                    # 先拼接再计算
                    # 如果取值不大于10 说明是0-9 则继续取值 直到取到最近一个大于10 的单位   应对这种30万20千 这样子
                    total = total + counting_unit * int(str(val) + temp_val)
                    # 计算后 把临时字符串置位空
                    temp_val = ''
            else:
                # 那就是无论如何要收尾了
                # 如果counting unit 等于1  说明所有字符串都是直接拼接的，不用计算，不然会丢失前半部分的零
                if counting_unit == 1:
                    temp_val = str(val) + temp_val
                else:
                    total = total + counting_unit * int(str(val) + temp_val)

    # 如果 total 为0  但是 countingUnit 不为0  说明结果是 十万这种  最终直接取结果 十万
    # 如果countingUnit 大于10 说明他是就是 汉字零
    if total == 0:
        if counting_unit > 10:
            total = str(counting_unit)
        else:
            if temp_val != "":
                total = temp_val
            else:
                total = str(total)
    else:
        total = str(total)
    return total

def chinese_to_digits(chinese_digits_mix_string: str, percent_convert: bool = True) -> str:
    """
    将中文数字转换为阿拉伯数字
    
    Args:
        chinese_digits_mix_string: 中文数字字符串
        percent_convert: 是否转换百分比
        
    Returns:
        str: 转换后的阿拉伯数字字符串
    """
    # 分之 分号切割  要注意
    chinese_chars_list_by_div = chinese_digits_mix_string.split('分之')
    convert_result_list = []
    
    for k in range(len(chinese_chars_list_by_div)):
        temp_chinese_chars = chinese_chars_list_by_div[k]
        chinese_chars_dot_split_list = []

        # 看有没有符号
        sign = ''
        for chars in temp_chinese_chars:
            if CHINESE_SIGN_DICT.get(chars) is not None:
                sign = CHINESE_SIGN_DICT.get(chars)
                temp_chinese_chars = temp_chinese_chars.replace(chars, '')
        
        # 防止没有循环完成就替换 报错
        chinese_chars = temp_chinese_chars

        # 小数点切割，看看是不是有小数点
        for chars in list(CHINESE_CONNECTING_SIGN_DICT.keys()):
            if chars in chinese_chars:
                chinese_chars_dot_split_list = chinese_chars.split(chars)

        if len(chinese_chars_dot_split_list) == 0:
            convert_result = core_chinese_to_digits(chinese_chars)
        else:
            # 如果小数点右侧有 单位 比如 2.55万  4.3百万 的处理方式
            # 先把小数点右侧单位去掉
            temp_count_string = ''
            for ii in range(len(chinese_chars_dot_split_list[-1]) - 1, -1, -1):
                if chinese_chars_dot_split_list[-1][ii] in CHINESE_PURE_COUNTING_UNIT_LIST:
                    temp_count_string = chinese_chars_dot_split_list[-1][ii] + temp_count_string
                else:
                    chinese_chars_dot_split_list[-1] = chinese_chars_dot_split_list[-1][0:(ii+1)]
                    break
                    
            if temp_count_string != '':
                temp_count_num = Decimal(core_chinese_to_digits(temp_count_string))
            else:
                temp_count_num = Decimal(1.0)
                
            if chinese_chars_dot_split_list[0] == '':
                # .01234 这种开头  用0 补位
                convert_result = '0.' + core_chinese_to_digits(chinese_chars_dot_split_list[1])
            else:
                # 小数点右侧要注意，有可能是00开头
                convert_result = core_chinese_to_digits(chinese_chars_dot_split_list[0]) + '.' + core_chinese_to_digits(chinese_chars_dot_split_list[1])

            convert_result = str(Decimal(convert_result) * temp_count_num)
            
        # 如果 convertResult 是空字符串， 表示可能整体字符串是 负百分之10 这种  或者 -百分之10
        if convert_result == '':
            convert_result = '1'

        convert_result = sign + convert_result
        convert_result_list.append(convert_result)
        
    if len(convert_result_list) > 1:
        # 是否转换分号及百分比
        if percent_convert:
            final_total = str(Decimal(convert_result_list[1]) / Decimal(convert_result_list[0]))
        else:
            if convert_result_list[0] == '100':
                final_total = convert_result_list[1] + '%'
            elif convert_result_list[0] == '1000':
                final_total = convert_result_list[1] + '‰'
            elif convert_result_list[0] == '10000':
                final_total = convert_result_list[1] + '‱'
            else:
                final_total = convert_result_list[1] + '/' + convert_result_list[0]
    else:
        final_total = convert_result_list[0]
        
    # 处理小数点右边的0
    if '.' in final_total:
        final_total = final_total.rstrip('0')
        final_total = final_total.rstrip('.')
        
    return final_total

def chinese_to_digits_high_tolerance(
    chinese_digits_mix_string: str, 
    percent_convert: bool = True, 
    skip_error: bool = False, 
    error_char: List[str] = None, 
    error_msg: List[str] = None
) -> str:
    """
    高容错的中文数字转阿拉伯数字函数
    
    Args:
        chinese_digits_mix_string: 中文数字字符串
        percent_convert: 是否转换百分比
        skip_error: 是否跳过错误
        error_char: 错误字符列表
        error_msg: 错误信息列表
        
    Returns:
        str: 转换后的阿拉伯数字字符串
    """
    if error_char is None:
        error_char = []
    if error_msg is None:
        error_msg = []
        
    if skip_error:
        try:
            total = chinese_to_digits(chinese_digits_mix_string, percent_convert=percent_convert)
        except Exception as e:
            # 返回类型不能是none 是空字符串
            total = ''
            error_char.append(chinese_digits_mix_string)
            error_msg.append(str(e))
    else:
        total = chinese_to_digits(chinese_digits_mix_string, percent_convert=percent_convert)
    return total

def check_chinese_number_reasonable(ch_number: str) -> bool:
    """
    检查中文数字是否合理
    
    Args:
        ch_number: 中文数字字符串
        
    Returns:
        bool: 是否合理
    """
    if len(ch_number) > 0:
        # 由于在上个检查点 已经把阿拉伯数字转为中文 因此不用检查阿拉伯数字部分
        # 如果汉字长度大于0 则判断是不是 万  千  单字这种
        for i in CHINESE_PURE_NUMBER_LIST:
            if i in ch_number:
                # 只要有数字在字符串 就说明不是 千千万万这种只有单位的表述
                return True
    return False

def traditional_text_convert(ch_string: str, traditional_convert_switch: bool = True) -> str:
    """
    繁体简体转换 及  单位  特殊字符转换 两千变二千
    
    Args:
        ch_string: 中文字符串
        traditional_convert_switch: 是否转换繁体
        
    Returns:
        str: 转换后的字符串
    """
    ch_string_list = list(ch_string)
    string_length = len(ch_string_list)

    if traditional_convert_switch:
        for i in range(string_length):
            # 繁体中文数字转简体中文数字
            if TRADITIONAL_CONVERT_DICT.get(ch_string_list[i], '') != '':
                ch_string_list[i] = TRADITIONAL_CONVERT_DICT.get(ch_string_list[i], '')
                
    if string_length > 1:
        # 检查繁体单体转换
        for i in range(string_length):
            # 如果 前后有 pure 汉字数字 则转换单位为简体
            if SPECIAL_TRADITIONAL_COUNTING_UNIT_CHAR_DICT.get(ch_string_list[i], '') != '':
                # 如果前后有单纯的数字 则进行单位转换
                if i == 0:
                    if ch_string_list[i+1] in CHINESE_PURE_NUMBER_LIST:
                        ch_string_list[i] = SPECIAL_TRADITIONAL_COUNTING_UNIT_CHAR_DICT.get(ch_string_list[i], '')
                elif i == string_length-1:
                    if ch_string_list[i-1] in CHINESE_PURE_NUMBER_LIST:
                        ch_string_list[i] = SPECIAL_TRADITIONAL_COUNTING_UNIT_CHAR_DICT.get(ch_string_list[i], '')
                else:
                    if ch_string_list[i-1] in CHINESE_PURE_NUMBER_LIST or \
                            ch_string_list[i+1] in CHINESE_PURE_NUMBER_LIST:
                        ch_string_list[i] = SPECIAL_TRADITIONAL_COUNTING_UNIT_CHAR_DICT.get(ch_string_list[i], '')
                        
            # 特殊变换 俩变二
            if SPECIAL_NUMBER_CHAR_DICT.get(ch_string_list[i], '') != '':
                # 如果前后有单位 则进行转换
                if i == 0:
                    if ch_string_list[i+1] in CHINESE_PURE_COUNTING_UNIT_LIST:
                        ch_string_list[i] = SPECIAL_NUMBER_CHAR_DICT.get(ch_string_list[i], '')
                elif i == string_length-1:
                    if ch_string_list[i-1] in CHINESE_PURE_COUNTING_UNIT_LIST:
                        ch_string_list[i] = SPECIAL_NUMBER_CHAR_DICT.get(ch_string_list[i], '')
                else:
                    if ch_string_list[i-1] in CHINESE_PURE_COUNTING_UNIT_LIST or \
                            ch_string_list[i+1] in CHINESE_PURE_COUNTING_UNIT_LIST:
                        ch_string_list[i] = SPECIAL_NUMBER_CHAR_DICT.get(ch_string_list[i], '')
                        
    return ''.join(ch_string_list)

def standard_chinese_number_convert(ch_number_string: str) -> str:
    """
    标准表述转换  三千二 变成 三千二百 三千十二变成 三千零一十二 四万十五变成四万零十五
    
    Args:
        ch_number_string: 中文数字字符串
        
    Returns:
        str: 转换后的字符串
    """
    ch_number_string_list = list(ch_number_string)

    # 大于2的长度字符串才有检测和补位的必要
    if len(ch_number_string_list) > 2:
        # 十位补一：
        try:
            ten_number_index = ch_number_string_list.index('十')
            if ten_number_index == 0:
                ch_number_string_list.insert(ten_number_index, '一')
            else:
                # 如果没有左边计数数字 插入1
                if ch_number_string_list[ten_number_index - 1] not in CHINESE_PURE_NUMBER_LIST:
                    ch_number_string_list.insert(ten_number_index, '一')
        except ValueError:
            pass

        # 差位补零
        # 逻辑 如果最后一个单位 不是十结尾 而是百以上 则数字后面补一个比最后一个出现的单位小一级的单位
        # 从倒数第二位开始看,且必须是倒数第二位就是单位的才符合条件
        try:
            last_counting_unit = CHINESE_PURE_COUNTING_UNIT_LIST.index(ch_number_string_list[len(ch_number_string_list)-2])
            # 如果最末位的是百开头
            if last_counting_unit >= 1:
                # 则字符串最后拼接一个比最后一个单位小一位的单位 例如四万三 变成四万三千

                # 如果最后一位结束的是亿 则补千万
                if last_counting_unit == 4:
                    ch_number_string_list.append('千万')
                else:
                    ch_number_string_list.append(CHINESE_PURE_COUNTING_UNIT_LIST[last_counting_unit - 1])
        except (ValueError, IndexError):
            pass
            
    # 检查是否是 万三  千四点五这种表述 百三百四
    per_count_switch = 0
    if len(ch_number_string_list) > 1:
        if ch_number_string_list[0] in ['千', '万', '百']:
            for i in range(1, len(ch_number_string_list)):
                # 其余位数都是纯数字 才能执行
                if ch_number_string_list[i] in CHINESE_PURE_NUMBER_LIST:
                    per_count_switch = 1
                else:
                    per_count_switch = 0
                    # y有一个不是数字 直接退出循环
                    break
                    
    if per_count_switch == 1:
        ch_number_string_list = ch_number_string_list[:1] + ['分', '之'] + ch_number_string_list[1:]
        
    return ''.join(ch_number_string_list)

def check_number_seg(chinese_number_list: List[str], origin_text: str) -> List[str]:
    """
    检查数字分段
    
    Args:
        chinese_number_list: 中文数字列表
        origin_text: 原始文本
        
    Returns:
        List[str]: 处理后的中文数字列表
    """
    new_chinese_number_list = []
    # 用来控制是否前一个已经合并过  防止多重合并
    temp_pre_text = ''
    temp_mixed_string = ''
    seg_len = len(chinese_number_list)
    
    if seg_len > 0:
        # 加入唯一的一个 或者第一个
        if chinese_number_list[0][:2] in CHINESE_PER_COUNTING_SEG:
            # 如果以分之开头 记录本次 防止后面要用 是否出现连续的 分之
            temp_pre_text = chinese_number_list[0]
            new_chinese_number_list.append(chinese_number_list[0][2:])
        else:
            new_chinese_number_list.append(chinese_number_list[0])

        if len(chinese_number_list) > 1:
            for i in range(1, seg_len):
                # 判断本字符是不是以  分之  开头  
                if chinese_number_list[i][:2] in CHINESE_PER_COUNTING_SEG:
                    # 如果是以 分之 开头 那么检查他和他见面的汉子数字是不是连续的 即 是否在原始字符串出现
                    temp_mixed_string = chinese_number_list[i-1] + chinese_number_list[i]
                    if temp_mixed_string in origin_text:
                        # 如果连续的上一个字段是以分之开头的  本字段又以分之开头  
                        if temp_pre_text != '':
                            # 检查上一个字段的末尾是不是 以 百 十 万 的单位结尾
                            if temp_pre_text[-1] in CHINESE_PURE_COUNTING_UNIT_LIST:
                                # 先把上一个记录进去的最后一位去掉
                                new_chinese_number_list[-1] = new_chinese_number_list[-1][:-1]
                                # 如果结果是确定的，那么本次的字段应当加上上一个字段的最后一个字
                                new_chinese_number_list.append(temp_pre_text[-1] + chinese_number_list[i])
                            else:
                                # 如果上一个字段不是以单位结尾  同时他又是以分之开头，那么 本次把分之去掉
                                new_chinese_number_list.append(chinese_number_list[i][2:])
                        else:
                            # 上一个字段不以分之开头，那么把两个字段合并记录
                            if len(new_chinese_number_list) > 0:
                                new_chinese_number_list[-1] = temp_mixed_string
                            else:
                                new_chinese_number_list.append(temp_mixed_string)
                    else:
                        # 说明前一个数字 和本数字不是连续的
                        # 本数字去掉分之二字
                        new_chinese_number_list.append(chinese_number_list[i][2:])

                    # 记录以 分之 开头的字段  用以下一个汉字字段判别
                    temp_pre_text = chinese_number_list[i]
                else:
                    # 不是  分之 开头 那么把本数字加入序列
                    new_chinese_number_list.append(chinese_number_list[i])
                    # 记录把不是 分之 开头的字段  临时变量记为空
                    temp_pre_text = ''
                    
    return new_chinese_number_list

def check_sign_seg(chinese_number_list: List[str]) -> List[str]:
    """
    检查符号分段
    
    Args:
        chinese_number_list: 中文数字列表
        
    Returns:
        List[str]: 处理后的中文数字列表
    """
    new_chinese_number_list = []
    temp_sign = ''
    for i in range(len(chinese_number_list)):
        # 新字符串 需要加上上一个字符串 最后1位的判断结果
        new_ch_number_string = temp_sign + chinese_number_list[i]
        last_string = new_ch_number_string[-1:]
        # 如果最后1位是正负号 那么本字符去掉最后1位  下一个数字加上最后3位
        if last_string in CHINESE_SIGN_LIST:
            temp_sign = last_string
            # 如果最后1位 是  那么截掉最后1位
            new_ch_number_string = new_ch_number_string[:-1]
        else:
            temp_sign = ''
        new_chinese_number_list.append(new_ch_number_string)
    return new_chinese_number_list

def digits_to_chinese_chars(mixed_string_list: List[str]) -> List[str]:
    """
    阿拉伯数字转中文
    
    Args:
        mixed_string_list: 混合字符串列表
        
    Returns:
        List[str]: 转换后的中文数字列表
    """
    result_list = []
    for mixed_string in mixed_string_list:
        if mixed_string.startswith('.'):
            mixed_string = '0' + mixed_string
        for key in DIGITS_CHAR_CH_DICT.keys():
            if key in mixed_string:
                # 应当记录下来有转换，然后再操作  在核心函数里 通过小数点判断是否应该强制  
                mixed_string = mixed_string.replace(key, DIGITS_CHAR_CH_DICT.get(key))
                # 应当是只要有百分号 就挪到前面 阿拉伯数字没有四百分之的说法
                # 防止这种 3%万 这种问题
                for k in CHINESE_PER_COUNTING_STRING_LIST:
                    if k in mixed_string:
                        temp = k + mixed_string.replace(k, '')
                        mixed_string = temp

        result_list.append(mixed_string)
    return result_list

def take_chinese_number_from_string(
    ch_text: str, 
    percent_convert: bool = True, 
    traditional_convert: bool = True, 
    digits_number_switch: bool = False, 
    verbose: bool = False
) -> Dict[str, Any]:
    """
    从字符串中提取中文数字
    
    Args:
        ch_text: 中文字符串
        percent_convert: 是否转换百分比
        traditional_convert: 是否转换繁体
        digits_number_switch: 是否只提取数字
        verbose: 是否显示详细信息
        
    Returns:
        Dict[str, Any]: 提取结果
    """
    # 是否只提取数字
    if digits_number_switch:
        return take_digits_number_from_string(ch_text, percent_convert=percent_convert)

    # 简体转换开关
    converted_ch_string = traditional_text_convert(ch_text, traditional_convert)

    # 字符串 汉字数字字符串切割提取
    # 正则表达式方法
    ch_number_string_list_temp = TAKING_CHINESE_DIGITS_MIX_RE.findall(converted_ch_string)
    # 检查是不是  分之 切割不完整问题
    ch_number_string_list_temp = check_number_seg(ch_number_string_list_temp, converted_ch_string)

    # 检查末位是不是正负号
    ch_number_string_list_temp = check_sign_seg(ch_number_string_list_temp)

    # 备份一个原始的提取，后期处结果的时候显示用
    origin_ch_number_take = ch_number_string_list_temp.copy()

    # 将阿拉伯数字变成汉字  不然合理性检查 以及后期 如果不是300万这种乘法  而是 四分之345  这种 就出错了
    ch_number_string_list_temp = digits_to_chinese_chars(ch_number_string_list_temp)

    # 检查合理性 是否是单纯的单位  等
    ch_number_string_list = []
    origin_ch_number_for_output = []
    for i in range(len(ch_number_string_list_temp)):
        temp_text = ch_number_string_list_temp[i]
        if check_chinese_number_reasonable(temp_text):
            # 如果合理  则添加进被转换列表
            ch_number_string_list.append(temp_text)
            # 则添加把原始提取的添加进来
            origin_ch_number_for_output.append(origin_ch_number_take[i])

    # 进行标准汉字字符串转换 例如 二千二  转换成二千零二
    ch_number_string_list_temp = list(map(lambda x: standard_chinese_number_convert(x), ch_number_string_list))

    # 将中文转换为数字
    digits_string_list = []
    replaced_text = converted_ch_string
    error_char_list = []
    error_msg_list = []
    
    if len(ch_number_string_list_temp) > 0:
        for kk in range(len(ch_number_string_list_temp)):
            digits_string_list.append(
                chinese_to_digits_high_tolerance(
                    ch_number_string_list_temp[kk],
                    percent_convert=percent_convert,
                    skip_error=verbose,
                    error_char=error_char_list,
                    error_msg=error_msg_list
                )
            )
            
        tuple_to_replace = [
            (d, c, i) for d, c, i in zip(
                origin_ch_number_for_output, 
                digits_string_list, 
                list(map(len, origin_ch_number_for_output))
            ) if c != ''
        ]

        # 按照提取出的中文数字字符串长短排序，然后替换。防止百分之二十八 ，二十八，这样的先把短的替换完了的情况
        tuple_to_replace = sorted(tuple_to_replace, key=lambda x: -x[2])
        for i in range(len(tuple_to_replace)):
            replaced_text = replaced_text.replace(tuple_to_replace[i][0], tuple_to_replace[i][1])

    final_result = {
        'inputText': ch_text,
        'replacedText': replaced_text,
        'CHNumberStringList': origin_ch_number_for_output,
        'digitsStringList': digits_string_list
    }
    return final_result

def take_number_from_string(
    ch_text: str, 
    percent_convert: bool = True, 
    traditional_convert: bool = True, 
    digits_number_switch: bool = False, 
    verbose: bool = False
) -> Dict[str, Any]:
    """
    从字符串中提取数字
    
    Args:
        ch_text: 中文字符串
        percent_convert: 是否转换百分比
        traditional_convert: 是否转换繁体
        digits_number_switch: 是否只提取数字
        verbose: 是否显示详细信息
        
    Returns:
        Dict[str, Any]: 提取结果
    """
    final_result = take_chinese_number_from_string(
        ch_text,
        percent_convert=percent_convert,
        traditional_convert=traditional_convert,
        digits_number_switch=digits_number_switch,
        verbose=verbose
    )
    return final_result

def take_digits_number_from_string(text_to_extract: str, percent_convert: bool = False) -> Dict[str, Any]:
    """
    从字符串中提取阿拉伯数字
    
    Args:
        text_to_extract: 要提取的字符串
        percent_convert: 是否转换百分比
        
    Returns:
        Dict[str, Any]: 提取结果
    """
    digits_number_string_list = TAKING_DIGITS_RE.findall(text_to_extract)
    
    # 最后检查有没有百分号
    digits_string_list = []
    replaced_text = text_to_extract
    
    if len(digits_number_string_list) > 0:
        tuple_to_replace = list(zip(digits_number_string_list, digits_string_list, list(map(len, digits_number_string_list))))

        # 按照提取出的中文数字字符串长短排序，然后替换。防止百分之二十八 ，二十八，这样的先把短的替换完了的情况
        tuple_to_replace = sorted(tuple_to_replace, key=lambda x: -x[2])
        for i in range(len(tuple_to_replace)):
            replaced_text = replaced_text.replace(tuple_to_replace[i][0], tuple_to_replace[i][1])

    final_result = {
        'inputText': text_to_extract,
        'replacedText': replaced_text,
        'digitsNumberStringList': digits_number_string_list,
        'digitsStringList': digits_string_list
    }
    return final_result
