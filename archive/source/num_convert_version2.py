"""
通用中文数字提取与转换工具
支持各种格式的中文数字、繁体数字、混合阿拉伯数字的转换
"""

from typing import Tuple, List, Dict, Union, Optional
import re
from functools import lru_cache

# 基础中文数字映射
BASE_DIGIT_MAP = {
    '零': 0, '〇': 0, 
    '一': 1,
    '二': 2, 
    '三': 3, 
    '四': 4, 
    '五': 5, 
    '六': 6,
    '七': 7,
    '八': 8,
    '九': 9, 
}

# 中文数字单位映射
UNIT_MAP = {
    '十': 10,
    '百': 100,
    '千': 1000, 
    '万': 10000, 
    '亿': 100000000, 
}

# 繁体数字和特殊形式映射到标准中文数字
TRADITIONAL_MAP = {
    # 数字
    '壹': "一",
    '贰': "二", '兩': "二", '两': "二", '双': "二", '貳': "二",
    '叁': "三", '叄': "三",
    '肆': "四",
    '伍': "五",
    '陆': "六", '陸': "六",
    '柒': "七",
    '捌': "八",
    '玖': "九",
    # 单位
    '拾': "十",
    '佰': "百",
    '仟': "千", 'K': "千", 'k': "千",
    '萬': "万", 'W': "万", 'w': "万",
    '億': "亿",
}

# 小数点字符集合
DECIMAL_POINTS = {'点', '點', '.'}

# 正则表达式模式
# 阿拉伯数字模式
ARABIC_PATTERN = r'(\d+(?:\.\d+)?)'

# 中文数字字符集（包含所有可能的形式）
CN_CHARS = ''.join(set(list(BASE_DIGIT_MAP.keys()) + list(UNIT_MAP.keys()) + list(TRADITIONAL_MAP.keys()) + list(DECIMAL_POINTS)))
CN_CHAR_PATTERN = f'[{CN_CHARS}]'

# 中文数字模式
CN_NUMBER_PATTERN = f'({CN_CHAR_PATTERN}+)'

# 混合数字模式
MIXED_PATTERN = f'((?:{ARABIC_PATTERN}\\s*|{CN_CHAR_PATTERN})+(?:{CN_CHAR_PATTERN}|\\s*{CN_CHAR_PATTERN}|\\s*{ARABIC_PATTERN})*)'

# 小数点模式
DECIMAL_PATTERN = f'[{"".join(DECIMAL_POINTS)}]'

# 带小数点的混合数字模式
DECIMAL_MIXED_PATTERN = f'({MIXED_PATTERN}{DECIMAL_PATTERN}{MIXED_PATTERN})'

# 复合数字模式（用于完整匹配）
COMPLEX_PATTERN = f'({DECIMAL_MIXED_PATTERN}|{MIXED_PATTERN}|(?:\\d+\\s*[Ww]\\d+)|(?:\\d+\\s*[WwKk]))'

# 合并所有数字映射（用于查询）
ALL_DIGIT_MAP = {**BASE_DIGIT_MAP, **{k: v for k, v in UNIT_MAP.items()}}

class ChineseNumberConverter:
    """中文数字转换器，支持各种形式的中文数字转阿拉伯数字"""
    
    def __init__(self):
        """初始化转换器"""
        # 合并所有数字和单位映射
        self.digit_map = {**BASE_DIGIT_MAP, **UNIT_MAP}
        # 编译正则表达式
        self.complex_pattern = re.compile(COMPLEX_PATTERN)
    
    @lru_cache(maxsize=1024)
    def convert(self, text: str) -> Optional[float]:
        """
        将中文数字转换为阿拉伯数字
        
        支持多种形式：
        - 纯中文数字：一千二百三十四
        - 繁体中文：壹萬貳仟叁佰肆拾伍
        - 混合形式：1千5百、2万3
        - 特殊形式：1.5w、250K、100w5000
        - 带小数点：五点五、二十五点五万
        - 省略单位：三万五（表示三万五千）、一千二（表示一千二百）

        Args:
            text: 要转换的文本
            
        Returns:
            Optional[float]: 转换后的数值，如果无法解析或输入不规范则返回 None
        """
        if not text or not text.strip():
            return None
        
        # 去除空格
        text = text.replace(" ", "")
        
        # 纯阿拉伯数字
        if re.match(r'^\d+(\.\d+)?$', text):
            return float(text)
        
        # 3. 标准化处理
        standardized = self._standardize(text)
      
        # 6. 处理小数点形式
        for point in DECIMAL_POINTS:
            if point in standardized:
                return self._parse_decimal_number(standardized, point)
        
        # 7. 补充省略单位
        fixed_text = self._fix_implicit_units(standardized)
        
        # 8. 执行主解析
        return self._parse_number(fixed_text)
    
    def _standardize(self, text: str) -> str:
        """将繁体字和特殊形式标准化为简体中文"""
        for old, new in TRADITIONAL_MAP.items():
            text = text.replace(old, new)
        return text

    def _fix_implicit_units(self, text: str) -> str:
        """补充省略的单位，如'三万五'转为'三万五千'"""
        if len(text) >= 2:
            second_last_char = text[-2]
            last_char = text[-1]
            
            # 定义单位顺序
            next_smaller_unit = {
                '百': '十',
                '千': '百',
                '万': '千',
                '亿': '千万'
            }
            
            if second_last_char in next_smaller_unit and (last_char in BASE_DIGIT_MAP or last_char.isdigit()):
                text += next_smaller_unit[second_last_char]
        
        return text
    
    def _parse_number(self, text: str) -> Optional[float]:
        """分层解析不同量级的单位（亿、万、基本单位）"""
        # 优化：使用统一的单位处理方式
        total = 0.0
        
        # 单位列表，按从大到小排序
        units = [('亿', 100000000), ('万', 10000), ('千', 1000), ('百', 100), ('十', 10)]
        
        # 从大到小处理各个单位
        for unit, unit_value in units:
            if unit not in text:
                continue
            parts = text.split(unit, 1)  # 只分割一次，处理最左边的单位
            if len(parts) <= 1:
                continue
                
            left, right = parts
            # # 如果左侧为空且右侧包含单位，说明单位之间没有数字，需要检查单位顺序
            # if not left:
            #     # 如果不是第一个处理的单位(total>0)，则出现了不规范表达
            #     if total > 0:
            #         return None
            
            # 获取单位前数值
            prefix_value = self._parse_section(left) if left else 1.0
            if prefix_value is None:
                return None
                
            # 累加结果并更新待处理文本
            total += prefix_value * unit_value
            text = right
            
            # 在处理完单位后添加
            if right.startswith(tuple(UNIT_MAP.keys())):
                # 如果剩余文本直接以单位开头，拒绝解析
                return None
            
        # 处理剩余可能的数字部分
        if text:
            final_value = self._parse_section(text)
            if final_value is None:
                return None
            total += final_value
            
        return total
    
    def _parse_section(self, text: str) -> Optional[float]:
        """解析一个数值段落（亿、万之间或小于万的部分）"""
        if not text:
            return 0.0
            
        # 处理零：移除单位前的零和连续零
        processed_text = self._normalize_zeros(text)
        
        if not processed_text or processed_text == '零':
            return 0.0
        
        # 修改：检查是否包含多个单位，只有多个单位时才递归
        unit_count = 0
        for unit in UNIT_MAP.keys():
            if unit in processed_text:
                unit_count += 1
                if unit_count > 1:
                    # 发现多个单位，递归调用_parse_number处理
                    return self._parse_number(processed_text)
        
        # 如果只有一个或没有单位，使用_parse_units_and_digits处理
        return self._parse_units_and_digits(processed_text)
    
    def _normalize_zeros(self, text: str) -> str:
        """标准化零的处理：移除单位前的零和连续零"""
        processed_text = ""
        for i, char in enumerate(text):
            if char == '零':
                # 如果零后面是单位或者零是最后一个字符，则移除
                if (i + 1 < len(text) and text[i+1] in UNIT_MAP) or i == len(text) - 1:
                    continue
                # 如果前一个字符也是零，则移除（连续零）
                if i > 0 and text[i-1] == '零':
                    continue
            processed_text += char
        return processed_text
    
    def _parse_units_and_digits(self, text: str) -> Optional[float]:
        """解析包含基本单位和数字的文本段落"""
        # 如果全是阿拉伯数字，直接返回
        if re.fullmatch(r'\d+(\.\d+)?', text):
            return float(text)
            
        result = 0.0
        i = 0
        n = len(text)
        
        # 解析状态变量
        current_number = 0.0
        last_unit_value = 1.0
        last_was_digit = False
        
        while i < n:
            char = text[i]
       
            # 处理阿拉伯数字序列
            if self._is_arabic_digit_start(char, i, text):
                num_value, next_i = self._read_arabic_number(text, i)
                current_number = num_value
                last_unit_value = 1.0
                last_was_digit = False
                i = next_i
                continue
                
            # 处理中文数字
            if char in BASE_DIGIT_MAP:
                if char == '零':
                    current_number = 0.0
                    last_was_digit = False
                else:
                    if last_was_digit:
                        return None  # 拒绝连续非零数字
                    current_number = float(BASE_DIGIT_MAP[char])
                    last_was_digit = True
                last_unit_value = 1.0
                i += 1
                continue
            
            # 处理单位
            if char in UNIT_MAP:
                unit_value = float(UNIT_MAP[char])
                num_before_unit = current_number if current_number != 0 else 1.0
                current_number = 0.0

                result += num_before_unit * last_unit_value * unit_value
                # 更新单位值，为下一个可能的连续单位做准备
                last_unit_value = unit_value
                last_was_digit = False
                i += 1
                continue
                
            # 处理无法识别的字符
            last_unit_value = 1.0
            current_number = 0.0
            last_was_digit = False
            i += 1
     
        # 加上最后一个数字（如果有）
        if current_number != 0:
            result += current_number * last_unit_value

        return result
    
    def _is_arabic_digit_start(self, char: str, pos: int, text: str) -> bool:
        """检查是否为阿拉伯数字序列的开始"""
        return char.isdigit() or (char == '.' and pos > 0 and text[pos-1].isdigit())
    
    def _read_arabic_number(self, text: str, start: int) -> Tuple[float, int]:
        """读取阿拉伯数字序列"""
        i = start
        is_float = (text[i] == '.')
        
        while i < len(text) and (text[i].isdigit() or (text[i] == '.' and not is_float)):
            if text[i] == '.':
                is_float = True
            i += 1
            
        try:
            num = float(text[start:i])
            return num, i
        except ValueError:
            # 无效数字，返回0并前进一位
            return 0.0, start + 1
    
    def _parse_decimal_number(self, text: str, decimal_point: str) -> Optional[float]:
        """解析带小数点的数字"""
        # 分割整数和小数部分
        parts = text.split(decimal_point, 1)
        if len(parts) != 2:
            return None
            
        integer_part, decimal_part = parts
        
        # 解析整数部分
        if not integer_part:
            integer_value = 0.0
        elif integer_part == '零':
            integer_value = 0.0
        else:
            # 优化：补充单位后再解析
            fixed_integer = self._fix_implicit_units(integer_part)
            integer_result = self._parse_number(fixed_integer)
            if integer_result is None:
                return None
            integer_value = integer_result
            
        # 查找小数部分的第一个单位
        main_unit, main_unit_value, decimal_digits, remainder = self._split_decimal_part(decimal_part)
        
        # 转换小数部分数字为阿拉伯数字
        arabic_decimal = self._convert_to_arabic_digits(decimal_digits)
        
        # 组合成基础浮点数
        try:
            if arabic_decimal:
                base_value = float(f"{integer_value:.0f}.{arabic_decimal}" if integer_value.is_integer() else f"{integer_value}.{arabic_decimal}")
            else:
                base_value = float(integer_value)
        except ValueError:
            base_value = float(integer_value)
            
        # 计算主值和余数值
        main_value = base_value * main_unit_value
        
        # 解析余数部分
        remainder_value = 0.0
        if remainder:
            fixed_remainder = self._fix_implicit_units(remainder)
            remainder_result = self._parse_number(fixed_remainder)
            if remainder_result is None:
                return None
            remainder_value = remainder_result
            
        return main_value + remainder_value
    
    def _split_decimal_part(self, decimal_part: str) -> Tuple[Optional[str], float, str, str]:
        """分析小数部分，提取单位、数字和余数"""
        main_unit = None
        main_unit_value = 1.0
        decimal_digits = decimal_part
        remainder = ""
        
        # 查找第一个单位
        for i, char in enumerate(decimal_part):
            if char in UNIT_MAP:
                main_unit = char
                main_unit_value = UNIT_MAP[char]
                decimal_digits = decimal_part[:i]
                remainder = decimal_part[i+1:]
                break
            elif not (char.isdigit() or char in BASE_DIGIT_MAP or char == '零'):
                decimal_digits = decimal_part[:i]
                remainder = decimal_part[i:]
                break
                
        return main_unit, main_unit_value, decimal_digits, remainder
    
    def _convert_to_arabic_digits(self, text: str) -> str:
        """将中文数字字符转换为阿拉伯数字字符串"""
        if not text:
            return "0"
            
        arabic_str = ""
        for char in text:
            if char.isdigit():
                arabic_str += char
            elif char in BASE_DIGIT_MAP:
                arabic_str += str(BASE_DIGIT_MAP[char])
                
        return arabic_str if arabic_str else "0"

def chinese_to_num(chinese_str: str) -> Optional[float]:
    """
    将中文数字转换为阿拉伯数字
    
    支持复合中文数字和混合形式，如：
    - "一千二百三十四"
    - "一亿两千万"
    - "1千5百"
    - "2万3000"
    - "1000W"
    - "250w"
    - "1000k"
    - "100w5000" (100万5000)
    - "五点五" (5.5)
    - "10 万" (100000)
    - "二十五点五万" (255000)
    - "壹萬貳仟叁佰肆拾伍" (12345)
    - "三万五" (35000) - 省略单位的情况，自动补全为"三万五千"
    - "一千二" (1200) - 省略单位的情况，自动补全为"一千二百"
    - "一千零一" (1001) - 带"零"的表达
    
    Args:
        chinese_str: 中文数字字符串或混合数字字符串
        
    Returns:
        Optional[float]: 转换后的数值, 如果无法解析或输入不规范则返回 None
    """
    return _CONVERTER.convert(chinese_str)

# 创建全局转换器实例
_CONVERTER = ChineseNumberConverter()

if __name__ == "__main__":
    test_cases = [
        # 基本测试
        ('abc', None),
        ('万千', None),
        ('百十', None),
        ("一", 1),
        ("十", 10),
        ("十一", 11),
        ("一十一", 11),
        ("二十", 20),
        ("二十三", 23),
        ("一百万", 1000000),
        ("一千万", 10000000),
        ("一千亿", 100000000000),
        ("一万亿", 1000000000000),

        # 复合单位测试
        ("百", 100),
        ("千", 1000),
        ("万", 10000),
        ("亿", 100000000),
        ("百万", 1000000),
        ("千万", 10000000),
        ("万亿", 1000000000000),
        ("一百", 100),
        ("一千", 1000),
        ("一万", 10000),
        ("一亿", 100000000),
        ("一百二十三", 123),
        ("一千二百三十四", 1234),
        ("一万二千三百四十五", 12345),
        ("壹萬貳仟叁佰肆拾伍", 12345),
        ("一亿两千万", 120000000),

        # 阿拉伯数字混合测试
        ("1千", 1000),
        ("1千5", 1500),
        ("1千五百", 1500),
        ("2万3千", 23000),
        ("2万3", 23000),
        ("10万", 100000),
        ("10 万", 100000),

        # K/W单位测试
        ("1000K", 1000000),
        ("1000k", 1000000),
        ("250w", 2500000),
        ("1000W", 10000000),
        ("100w5000", 1005000),
        ("1w1k", 11000),
        ("1.5w", 15000),

        # 小数点测试
        ("五点五", 5.5),
        ("十五点五", 15.5),
        ("二十五点五万", 255000),
        ("一点五万", 15000),
        ("三百点一五六", 300.156),
        ("三百点一千", 300100),
        ("五点二万三千", 55000),
        ("一百点零五万", 1000500),
        ("点五万", 5000),
        ("五点万", 50000),
        ("五点零零叁伍", 5.0035),
        ("一百二十三万四千二百五十", 1234250),

        # 特殊写法测试
        ("两千", 2000),
        ("贰佰", 200),
        ("壹仟", 1000),
        ("三万三", 33000),
        ("一千二", 1200),
        ("一千二百三", 1230),

        # 边界条件测试
        ("", None),
        ("零", 0),
        ("一百零一", 101),
        ("一千零一", 1001),
        ("一万零一", 10001),
        ("一亿零一", 100000001),
        ("一千万零一百", 10000100),

        # 异常情况和单位连用测试
        ("abc", None),
        ("亿万", None),
        ("千百", None),
        ("百十", None),
        ("五千百", None),
        
        # 拒绝模式测试
        ("千百万", None),
        ("百千万", 1000000000),

        # 附加测试
        ("三万零五百", 30500),
        ("三亿二千零五万零三", 320050003),
        ("1.5万3千", 18000),
        ("三千二", 3200),

        # 零的复杂情况
        ("一千零五十", 1050),
        ("一千零五", 1005),
        ("一万零三百零二", 10302),
        ("一亿零三百万零四千", 103004000),
        ("一亿零三百零二万", 103020000),
        ("一亿零三", 100000003),

        # 混合数字与复杂单位/零
        ("500万3千", 5003000),
        ("100万零5百", 1000500),
        ("一亿零三百万", 103000000),
        ("一亿零三百", 100000300),
        ("一千万零一百", 10000100),
        ("一亿零一万零一百", 100010100),

        # 无效/模糊输入
        ("一二三四", None), 
        ("八九十", None),
    ]

    print("中文数字转换测试结果 (优化版):")
    print("=" * 80)
    print("{:<20s} {:<20s} {:<15s} {:<15s} {:<5s}".format(
        "案例片段", "匹配片段", "解析结果", "预期结果", "状态"))
    print("-" * 80)
    
    passed = 0
    failed = 0
    
    _CONVERTER = ChineseNumberConverter() 
    
    for test_case, expected in test_cases:
        # 匹配测试
        match = _CONVERTER.complex_pattern.search(test_case)
        match_str = match.group() if match else "未匹配"
        
        if match_str == "未匹配":
            result = None
        else:
            # 转换测试
            result = chinese_to_num(test_case)
        status = "" 
        if result is None and expected is None:
            status = "✓"
        elif result is not None and expected is not None and abs(result - expected) < 1e-6:
            status = "✓"
        else:
            status = "✗"
        
        if status == "✓":
            passed += 1
        else:
            failed += 1
            print(f"失败案例: {test_case}, 预期: {expected}, 实际: {result}")

        # 格式化数字结果
        result_str = "None"
        if result is not None:
            if result == int(result):
                result_str = f"{int(result)}"
            else:
                result_str = f"{result:.8f}".rstrip('0').rstrip('.')
            
        expected_str = "None"
        if expected is not None:
            if expected == int(expected):
                expected_str = f"{int(expected)}"
            else:
                expected_str = f"{expected:.8f}".rstrip('0').rstrip('.')

        print("{:<20s} {:<20s} {:<15s} {:<15s} {:<5s}".format(
            test_case, match_str, result_str, expected_str, status))
    
    print("-" * 80)
    print(f"测试完成: 总计 {len(test_cases)} 个测试案例")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print("=" * 80)

