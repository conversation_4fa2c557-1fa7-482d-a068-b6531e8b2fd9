"""
通用提取工具
包含各类提取器共用的工具函数和常量
"""

from typing import Tuple, List
import re

# 中文数字映射
CHINESE_DIGITS = {
    '零': 0, '〇': 0, '0': 0,
    '一': 1, '壹': 1, '1': 1,
    '二': 2, '贰': 2, '兩': 2, '两': 2, '2': 2,
    '三': 3, '叁': 3, '3': 3,
    '四': 4, '肆': 4, '4': 4,
    '五': 5, '伍': 5, '5': 5,
    '六': 6, '陆': 6, '陸': 6, '6': 6,
    '七': 7, '柒': 7, '7': 7,
    '八': 8, '捌': 8, '8': 8,
    '九': 9, '玖': 9, '9': 9,
    '十': 10, '拾': 10,
    '百': 100, '佰': 100,
    '千': 1000, '仟': 1000,
    '万': 10000, '萬': 10000, 'W': 10000, 'w': 10000,
    '亿': 100000000, '億': 100000000
}

# 通用阿拉伯数字模式
ARABIC_NUMBER_PATTERN = r'(\d+(?:\.\d+)?)'

# 中文数字基本单位
CHINESE_NUMBER_CHARS = r'[一二三四五六七八九十百千万亿零兩两壹贰叁肆伍陆柒捌玖拾佰仟萬億Ww]'

# 中文数字模式（纯中文数字表达式）
CHINESE_NUMBER_PATTERN = rf'({CHINESE_NUMBER_CHARS}+)'

# 混合数字模式（允许中文数字和阿拉伯数字混合，例如"1千5百"，"2万3千"等）
MIXED_NUMBER_PATTERN = rf'((?:{ARABIC_NUMBER_PATTERN}|{CHINESE_NUMBER_CHARS})+(?:{CHINESE_NUMBER_CHARS}|{ARABIC_NUMBER_PATTERN})*)'

# 通用数字匹配模式（整合上述所有模式）
NUMBER_PATTERN = rf'({ARABIC_NUMBER_PATTERN}|{CHINESE_NUMBER_PATTERN})'

# 复合数字模式（用于匹配复杂的数字表达，包括带单位的混合表达形式，如"一千二百三十四"，"两万三千"，"1千5百"等）
COMPLEX_NUMBER_PATTERN = rf'({MIXED_NUMBER_PATTERN}|(?:\d+[Ww]\d+)|(?:\d+[Ww]))'

# =========== 金额抽取器相关常量 ===========

# 槽位类型
SPECIFIC_AMOUNT = "specific_amount"
AMOUNT_RANGE = "amount_range"

# 金额单位定义
class AmountUnits:
    """金额单位定义类"""
    
    # 金额单位映射（原始单位 -> 标准单位）
    UNIT_MAPPING = {
        # 人民币基本单位
        "元": "元", "块": "元", "人民币": "元", "rmb": "元", "RMB": "元", "￥": "元",
        # 美元
        "美元": "美元", "usd": "美元", "USD": "美元", "$": "美元",
        # 港币
        "港币": "港币", "hkd": "港币", "HKD": "港币", "HK$": "港币",
        # 欧元
        "欧元": "欧元", "eur": "欧元", "EUR": "欧元", "€": "欧元",
        # 英镑
        "英镑": "英镑", "gbp": "英镑", "GBP": "英镑", "£": "英镑",
        # 日元
        "日元": "日元", "jpy": "日元", "JPY": "日元", "¥": "日元",
        # 澳元
        "澳元": "澳元", "aud": "澳元", "AUD": "澳元", "$": "澳元",
    }
    
    # 金额单位转换为元的比例
    YUAN_CONVERSION = {
        "元": 1,
    }
    
    @classmethod
    def get_all_units(cls) -> List[str]:
        """获取所有金额单位"""
        return list(cls.UNIT_MAPPING.keys())
    
    @classmethod
    def get_standard_unit(cls, unit: str) -> str:
        """获取标准化的金额单位"""
        return cls.UNIT_MAPPING.get(unit, unit)
    
    @classmethod
    def get_pattern(cls) -> str:
        """获取金额单位的正则表达式模式"""
        return f"({'|'.join(cls.get_all_units())})"

# 金额单位模式
AMOUNT_UNIT_PATTERN = AmountUnits.get_pattern()

# 金额范围连接词
AMOUNT_RANGE_CONNECTOR = r'(到|至|~|-|—|－|→|、|和|与|或|及)'

# 近似修饰前缀：出现在金额单位前的近似修饰词
AMOUNT_APPROXIMATE_PREFIX = r'(大约|大概|约|约为|接近|差不多|估计|粗略|基本|大致)'

# 近似修饰后缀：出现在金额单位后的近似修饰词
AMOUNT_APPROXIMATE_SUFFIX = r'(左右|上下|内外|之间|出头|之久|多)'

# 上限前缀修饰词：放在金额单位前，表示金额的最大限制
AMOUNT_UPPER_LIMIT_PREFIX = r'(不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|低于|低於)'

# 上限后缀修饰词：放在金额单位后，表示金额的最大限制
AMOUNT_UPPER_LIMIT_SUFFIX = r'(以下|以内|之内|内|未满)'

# 下限前缀修饰词：放在金额单位前，表示金额的最小限制
AMOUNT_LOWER_LIMIT_PREFIX = r'(最少|至少|不少于|不少於|多于|多於|起码|高于|高於|不低于|不低於)'

# 下限后缀修饰词：放在金额单位后，表示金额的最小限制
AMOUNT_LOWER_LIMIT_SUFFIX = r'(以上|之上|开外|起|起步|打底)'

# 定义基本金额单元模式
AMOUNT_UNIT_EXPR = fr'''
(?:
  # 简单金额单位（数字+单位）
  (?:{COMPLEX_NUMBER_PATTERN}{AMOUNT_UNIT_PATTERN})
)
'''

# =========== 收益率抽取器相关常量 ===========

# 槽位类型
SPECIFIC_RATE = "specific_rate"
RATE_RANGE = "rate_range"

# 收益率单位定义
class RateUnits:
    """收益率单位定义类"""
    
    # 收益率单位映射（原始单位 -> 标准单位）
    UNIT_MAPPING = {
        # 百分比
        "%": "percent", "％": "percent", "个百分点": "percent", "百分点": "percent", "个点": "percent",
        # 千分比
        "‰": "permille", "千分点": "permille",
        # 基点
        "BP": "basic_point", "bp": "basic_point", "个基点": "basic_point", "基点": "basic_point"
    }
    
    # 收益率周期
    PERIOD_MAPPING = {
        # 年化
        "年化": "year", "年收益率": "year", "年息": "year", "年利率": "year", "年回报率": "year", "/年": "year", "每年": "year",
        # 月化
        "月化": "month", "月收益率": "month", "月息": "month", "月利率": "month", "月回报率": "month", "/月": "month", "每月": "month",
        # 日化
        "日化": "day", "日收益率": "day", "日息": "day", "日利率": "day", "日回报率": "day", "/日": "day", "每日": "day",
        # 周化
        "周化": "week", "周收益率": "week", "周息": "week", "周利率": "week", "周回报率": "week", "/周": "week", "每周": "week"
    }
    
    # 单位转换为小数比例
    DECIMAL_CONVERSION = {
        "percent": 0.01,     # 百分比：除以100
        "permille": 0.001,   # 千分比：除以1000
        "basic_point": 0.0001 # 基点：除以10000
    }
    
    @classmethod
    def get_all_units(cls) -> List[str]:
        """获取所有收益率单位"""
        return list(cls.UNIT_MAPPING.keys())
    
    @classmethod
    def get_all_periods(cls) -> List[str]:
        """获取所有收益率周期"""
        return list(cls.PERIOD_MAPPING.keys())
    
    @classmethod
    def get_standard_unit(cls, unit: str) -> str:
        """获取标准化的收益率单位"""
        return cls.UNIT_MAPPING.get(unit, "percent")
    
    @classmethod
    def get_standard_period(cls, period: str) -> str:
        """获取标准化的收益率周期"""
        return cls.PERIOD_MAPPING.get(period, "year")
    
    @classmethod
    def get_unit_pattern(cls) -> str:
        """获取收益率单位的正则表达式模式"""
        return f"({'|'.join(cls.get_all_units())})"
    
    @classmethod
    def get_period_pattern(cls) -> str:
        """获取收益率周期的正则表达式模式"""
        return f"({'|'.join(cls.get_all_periods())})"

# 收益率单位模式
RATE_UNIT_PATTERN = RateUnits.get_unit_pattern()

# 收益率周期模式
RATE_PERIOD_PATTERN = RateUnits.get_period_pattern()

# 收益率范围连接词（复用金额范围连接词）
RATE_RANGE_CONNECTOR = AMOUNT_RANGE_CONNECTOR

# 近似修饰前缀：出现在收益率前的近似修饰词
RATE_APPROXIMATE_PREFIX = r'(大约|大概|约|约为|接近|差不多|估计|粗略|基本|大致|预期|预计|目标|期望)'

# 近似修饰后缀：出现在收益率后的近似修饰词
RATE_APPROXIMATE_SUFFIX = r'(左右|上下|内外|之间|出头|多一点|多点|多些|多)'

# 上限前缀修饰词：放在收益率前，表示收益率的最大限制
RATE_UPPER_LIMIT_PREFIX = AMOUNT_UPPER_LIMIT_PREFIX

# 上限后缀修饰词：放在收益率后，表示收益率的最大限制
RATE_UPPER_LIMIT_SUFFIX = AMOUNT_UPPER_LIMIT_SUFFIX

# 下限前缀修饰词：放在收益率前，表示收益率的最小限制
RATE_LOWER_LIMIT_PREFIX = AMOUNT_LOWER_LIMIT_PREFIX

# 下限后缀修饰词：放在收益率后，表示收益率的最小限制
RATE_LOWER_LIMIT_SUFFIX = AMOUNT_LOWER_LIMIT_SUFFIX

# 定义基本收益率单元模式
RATE_UNIT_EXPR = fr'''
(?:
  # 包含周期词的情况
  (?:{RATE_PERIOD_PATTERN}{COMPLEX_NUMBER_PATTERN}(?:{RATE_UNIT_PATTERN})?)
  |
  # 包含收益率单位的情况
  (?:{COMPLEX_NUMBER_PATTERN}{RATE_UNIT_PATTERN})
)
'''

def chinese_to_num(chinese_str: str) -> float:
    """
    将中文数字转换为阿拉伯数字
    
    支持复合中文数字和混合形式，如：
    - "一千二百三十四"
    - "一亿两千万"
    - "1千5百"
    - "2万3000"
    - "1000W"
    - "250w"
    - "100w5000" (100万5000)
    
    Args:
        chinese_str: 中文数字字符串或混合数字字符串
        
    Returns:
        float: 转换后的数值
    """
    if not chinese_str or chinese_str == "":
        return 0
    
    # 处理W/w形式的万元表示
    if re.match(r'^\d+[Ww]$', chinese_str):
        number = float(chinese_str[:-1])
        return number * 10000
        
    # 处理数字+w+数字的形式，如100w5000
    match_complex_w = re.match(r'^(\d+)[Ww](\d+)$', chinese_str)
    if match_complex_w:
        wan_part = float(match_complex_w.group(1)) * 10000
        ge_part = float(match_complex_w.group(2))
        return wan_part + ge_part
    
    # 使用正则表达式提取数字部分
    match = re.search(COMPLEX_NUMBER_PATTERN, chinese_str)
    if not match:
        return 0
        
    chinese_str = match.group()
    
    # 如果直接是阿拉伯数字，直接返回
    if re.match(r'^\d+(\.\d+)?$', chinese_str):
        return float(chinese_str)
    
    # 替换特殊写法并预处理
    chinese_str = chinese_str.replace('零', '')
    
    # 分步处理不同单位级别
    return _process_number_units(chinese_str)

def _process_number_units(chinese_str: str) -> float:
    """
    分级处理中文数字单位
    
    Args:
        chinese_str: 预处理后的中文数字字符串
        
    Returns:
        float: 转换后的数值
    """
    # 处理混合的阿拉伯数字部分
    chinese_str = _preprocess_arabic_parts(chinese_str)
    
    # 处理亿级别
    total, remaining = _process_unit(chinese_str, ['亿', '億'], 100000000)
    
    # 处理万级别
    if remaining:
        万_total, remaining = _process_unit(remaining, ['万', '萬'], 10000)
        total += 万_total
    
    # 处理剩余的千、百、十和个位数
    if remaining:
        total += _process_basic_units(remaining)
        
    return total

def _preprocess_arabic_parts(chinese_str: str) -> str:
    """预处理混合数字中的阿拉伯数字部分"""
    arabics = re.findall(r'\d+(?:\.\d+)?', chinese_str)
    for arabic in arabics:
        next_char_match = re.search(rf'{re.escape(arabic)}({CHINESE_NUMBER_CHARS}+)', chinese_str)
        if next_char_match:
            chinese_str = chinese_str.replace(arabic, str(float(arabic)), 1)
    return chinese_str

def _process_unit(chinese_str: str, unit_chars: List[str], unit_value: int) -> Tuple[float, str]:
    """
    处理特定单位的中文数字部分
    
    Args:
        chinese_str: 要处理的字符串
        unit_chars: 单位字符列表 (如 ['亿', '億'])
        unit_value: 单位对应的值 (如 100000000)
        
    Returns:
        Tuple[float, str]: (处理后的总值, 剩余待处理的字符串)
    """
    total = 0
    
    # 检查是否包含该单位
    has_unit = False
    for unit in unit_chars:
        if unit in chinese_str:
            has_unit = True
            break
            
    if not has_unit:
        return 0, chinese_str
        
    # 按单位分割字符串
    parts = re.split('|'.join([re.escape(u) for u in unit_chars]), chinese_str)
    
    if parts[0]:  # 单位以上的部分
        prefix = parts[0].strip()
        if prefix in CHINESE_DIGITS or prefix.isdigit():
            # 处理简单表达，如"一亿"
            prefix_val = float(prefix) if prefix.isdigit() else CHINESE_DIGITS[prefix]
            total += prefix_val * unit_value
        else:
            # 处理复杂表达，如"一千二百亿"
            # 递归处理更小单位
            total += _process_basic_units(prefix) * unit_value
    
    # 处理剩余部分
    remaining = ""
    if len(parts) > 1 and parts[1]:
        remaining = parts[1]
    
    return total, remaining

def _process_basic_units(chinese_str: str) -> float:
    """处理基本单位（千、百、十）和数字"""
    result = 0
    temp = 0
    i = 0
    
    while i < len(chinese_str):
        char = chinese_str[i]
        
        # 处理阿拉伯数字序列
        if char.isdigit():
            num_start = i
            while i < len(chinese_str) and (chinese_str[i].isdigit() or chinese_str[i] == '.'):
                i += 1
            temp = float(chinese_str[num_start:i])
            
            # 如果后面没有单位，直接加到结果中
            if i >= len(chinese_str) or chinese_str[i] not in CHINESE_DIGITS or CHINESE_DIGITS[chinese_str[i]] < 10:
                result += temp
                temp = 0
            continue
        
        # 处理中文数字和单位
        if char in CHINESE_DIGITS:
            value = CHINESE_DIGITS[char]
            if value >= 10:  # 是单位（十、百、千）
                if temp == 0:
                    temp = 1
                result += temp * value
                temp = 0
            else:  # 是数字（一至九）
                temp = temp * 10 + value if temp >= 10 else value
        
        i += 1
    
    # 加上最后可能剩余的数字
    result += temp
    return result

def extract_numbers(text: str) -> List[Tuple[str, float]]:
    """
    从文本中提取所有可能的数字表达（包括阿拉伯数字、中文数字和混合形式）
    
    Args:
        text: 要处理的文本
        
    Returns:
        List[Tuple[str, float]]: 提取的数字表达及其转换后的数值列表
    """
    results = []
    
    # 匹配所有可能的数字表达
    matches = re.finditer(COMPLEX_NUMBER_PATTERN, text)
    
    for match in matches:
        num_text = match.group(0)
        num_value = chinese_to_num(num_text)
        results.append((num_text, num_value))
    
    return results

class AmountUnitHandler:
    """金额单位处理器"""
    
    @classmethod
    def standardize(cls, unit: str) -> str:
        """标准化金额单位"""
        return AmountUnits.get_standard_unit(unit)
    
    @classmethod
    def to_yuan(cls, value: float, unit: str) -> float:
        """将金额单位转换为元"""
        standard_unit = cls.standardize(unit)
        return value * AmountUnits.YUAN_CONVERSION.get(standard_unit, 1)
    
    @classmethod
    def parse_number_and_unit(cls, text: str) -> Tuple[float, str]:
        """从文本中解析数字和单位"""
        # 提取数字
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        number_text = number_match.group(0) if number_match else "1"
        number = chinese_to_num(number_text)
        
        # 提取单位
        unit_match = re.search(AMOUNT_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else "元"
                
        return number, unit
    
    @classmethod
    def normalize_amount(cls, text: str, context: str = None) -> float:
        """
        将金额文本标准化为元
        
        Args:
            text: 金额文本
            context: 上下文文本，有助于识别单位
            
        Returns:
            float: 标准化后的金额（元）
        """
        number, unit = cls.parse_number_and_unit(text if context is None else text + " " + context)
 
        return cls.to_yuan(number, unit)
        
class AmountModifierHandler:
    """金额修饰词处理器"""
    
    @staticmethod
    def has_upper_limit_modifier(text: str) -> bool:
        """检查是否有上限修饰词"""
        return bool(re.search(fr'({AMOUNT_UPPER_LIMIT_PREFIX}|{AMOUNT_UPPER_LIMIT_SUFFIX})', text))
    
    @staticmethod
    def has_lower_limit_modifier(text: str) -> bool:
        """检查是否有下限修饰词"""
        return bool(re.search(fr'({AMOUNT_LOWER_LIMIT_PREFIX}|{AMOUNT_LOWER_LIMIT_SUFFIX})', text))
    
    @staticmethod
    def has_approximate_modifier(text: str) -> bool:
        """检查是否有近似修饰词"""
        return bool(re.search(fr'({AMOUNT_APPROXIMATE_PREFIX}|{AMOUNT_APPROXIMATE_SUFFIX})', text))
    
    @staticmethod
    def has_modifier(text: str) -> bool:
        """检查是否有修饰词"""
        return bool(re.search(fr'({AMOUNT_UPPER_LIMIT_PREFIX}|{AMOUNT_UPPER_LIMIT_SUFFIX}|{AMOUNT_LOWER_LIMIT_PREFIX}|{AMOUNT_LOWER_LIMIT_SUFFIX}|{AMOUNT_APPROXIMATE_PREFIX}|{AMOUNT_APPROXIMATE_SUFFIX})', text))
    
    @classmethod
    def apply_modifiers(cls, min_value: float, max_value: float, text: str) -> Tuple[float, float]:
        """
        应用修饰词调整金额范围
        
        Args:
            min_value: 初始最小值（元）
            max_value: 初始最大值（元）
            text: 原始文本
            
        Returns:
            Tuple[float, float]: 调整后的(最小值, 最大值)，最大值可能为None表示无上限
        """
        # 检查是否有各类修饰词
        has_lower_limit = cls.has_lower_limit_modifier(text)
        has_upper_limit = cls.has_upper_limit_modifier(text)
        has_approximate = cls.has_approximate_modifier(text)
        
        # 情况1: 单一值 (min_value == max_value) 且有近似修饰词
        if min_value == max_value and has_approximate:
            base_amount = min_value
            # 近似值的范围为±20%
            min_value = base_amount * 0.8
            max_value = base_amount * 1.2
            return min_value, max_value
            
        # 情况2: 单一值 (min_value == max_value)
        if min_value == max_value:
            base_amount = min_value
            
            # 下限修饰词：如"至少100元"，表示最小值是100元，最大值无限
            if has_lower_limit:
                min_value = base_amount
                max_value = None
            
            # 上限修饰词：如"不超过100元"，表示最大值是100元，最小值为0
            elif has_upper_limit:
                min_value = 0
                max_value = base_amount
                
        # 情况3: 范围值 (min_value != max_value)
        else:
            # 处理下限修饰词：例如"至少100-200元"，表示最小值是200元，最大值无限
            if has_lower_limit:
                min_value = max_value  # 取范围中的最大值作为下限
                max_value = None       # 无上限
            
            # 处理上限修饰词：例如"不超过100-200元"，表示最大值是200元，最小值为0
            elif has_upper_limit:
                min_value = 0          # 最小值设为0
                # max_value保持不变
        
        return min_value, max_value

class RateUnitHandler:
    """收益率单位处理器"""
    
    @classmethod
    def standardize_unit(cls, unit: str) -> str:
        """标准化收益率单位"""
        return RateUnits.get_standard_unit(unit)
    
    @classmethod
    def standardize_period(cls, period: str) -> str:
        """标准化收益率周期"""
        return RateUnits.get_standard_period(period)
    
    @classmethod
    def to_decimal(cls, value: float, unit: str) -> float:
        """将收益率转换为小数形式（如5%转为0.05）"""
        standard_unit = cls.standardize_unit(unit)
        return value * RateUnits.DECIMAL_CONVERSION.get(standard_unit, 0.01)
    
    @classmethod
    def parse_number_unit_period(cls, text: str) -> Tuple[float, str, str]:
        """从文本中解析数字、单位和周期"""
        # 提取数字
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        number_text = number_match.group(0) if number_match else "0"
        number = chinese_to_num(number_text)
        
        # 提取单位
        unit_match = re.search(RATE_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else ""
        
        # 提取周期
        period_match = re.search(RATE_PERIOD_PATTERN, text)
        period = period_match.group(0) if period_match else ""
                
        return number, unit, period
    
    @classmethod
    def normalize_rate(cls, text: str) -> float:
        """
        将收益率文本标准化为小数形式
        
        Args:
            text: 收益率文本
            
        Returns:
            float: 标准化后的收益率（小数形式，如5%返回0.05）
            
        Raises:
            ValueError: 如果文本中既没有单位也没有周期词，则认为不是有效的收益率表达
        """
        number, unit, period = cls.parse_number_unit_period(text)
        
        # 验证：必须至少含有单位或周期词之一
        if not unit and not period:
            raise ValueError(f"无效的收益率表达: '{text}' - 缺少单位或周期词")
            
        # 如果没有单位但有周期词，默认为百分比单位
        if not unit:
            unit = "%"
            
        return cls.to_decimal(number, unit)

class RateModifierHandler:
    """收益率修饰词处理器"""
    
    @staticmethod
    def has_upper_limit_modifier(text: str) -> bool:
        """检查是否有上限修饰词"""
        return bool(re.search(fr'({RATE_UPPER_LIMIT_PREFIX}|{RATE_UPPER_LIMIT_SUFFIX})', text))
    
    @staticmethod
    def has_lower_limit_modifier(text: str) -> bool:
        """检查是否有下限修饰词"""
        return bool(re.search(fr'({RATE_LOWER_LIMIT_PREFIX}|{RATE_LOWER_LIMIT_SUFFIX})', text))
    
    @staticmethod
    def has_approximate_modifier(text: str) -> bool:
        """检查是否有近似修饰词"""
        return bool(re.search(fr'({RATE_APPROXIMATE_PREFIX}|{RATE_APPROXIMATE_SUFFIX})', text))
    
    @staticmethod
    def has_modifier(text: str) -> bool:
        """检查是否有修饰词"""
        return bool(re.search(fr'({RATE_UPPER_LIMIT_PREFIX}|{RATE_UPPER_LIMIT_SUFFIX}|{RATE_LOWER_LIMIT_PREFIX}|{RATE_LOWER_LIMIT_SUFFIX}|{RATE_APPROXIMATE_PREFIX}|{RATE_APPROXIMATE_SUFFIX})', text))
    
    @classmethod
    def apply_modifiers(cls, min_value: float, max_value: float, text: str) -> Tuple[float, float]:
        """
        应用修饰词调整收益率范围
        
        Args:
            min_value: 初始最小值（小数形式）
            max_value: 初始最大值（小数形式）
            text: 原始文本
            
        Returns:
            Tuple[float, float]: 调整后的(最小值, 最大值)，最大值可能为None表示无上限
        """
        # 检查是否有各类修饰词
        has_lower_limit = cls.has_lower_limit_modifier(text)
        has_upper_limit = cls.has_upper_limit_modifier(text)
        has_approximate = cls.has_approximate_modifier(text)
        
        # 情况1: 单一值 (min_value == max_value) 且有近似修饰词
        if min_value == max_value and has_approximate:
            base_rate = min_value
            # 近似值的范围为±20%
            min_value = max(0, base_rate - base_rate * 0.2)
            max_value = base_rate + base_rate * 0.2
            return min_value, max_value
            
        # 情况2: 单一值 (min_value == max_value)
        if min_value == max_value:
            base_rate = min_value
            
            # 下限修饰词：如"至少5%"，表示最小值是5%，最大值无限
            if has_lower_limit:
                min_value = base_rate
                max_value = None
            
            # 上限修饰词：如"不超过5%"，表示最大值是5%，最小值为0
            elif has_upper_limit:
                min_value = 0
                max_value = base_rate
                
        # 情况3: 范围值 (min_value != max_value)
        else:
            # 处理下限修饰词：例如"至少5%-8%"，表示最小值是8%，最大值无限
            if has_lower_limit:
                min_value = max_value  # 取范围中的最大值作为下限
                max_value = None       # 无上限
            
            # 处理上限修饰词：例如"不超过5%-8%"，表示最大值是8%，最小值为0
            elif has_upper_limit:
                min_value = 0          # 最小值设为0
                # max_value保持不变
        
        return min_value, max_value

# if __name__ == "__main__":
#     # 测试用例
#     test_cases = [
#         ("一千二百三十四", 1234),
#         ("一亿两千万", 120000000),
#         ("1千5百", 1500),
#         ("2万3千", 23000),
#         ("两亿五千万六千七百八十九", 250006789),
#         ("三百零五", 305),
#         ("十一", 11),
#         ("零", 0),
#         ("9527", 9527),
#         ("23.12", 23.12),
#         ("一万二千三百四十五点六七", 12345.67),
#     ]
    
#     print("中文数字转换测试：")
#     for test_str, expected in test_cases:
#         result = chinese_to_num(test_str)
#         print(f"{test_str} -> {result} {'✓' if result == expected else f'✗ (期望值: {expected})'}")
    
#     print("\n文本提取数字测试：")
#     text_samples = [
#         "我有一千五百元钱",
#         "这本书售价35元",
#         "这家公司市值两亿三千万美元",
#         "房间里有2千3百个箱子和15个袋子",
#         "项目预算为3亿4千5百万人民币",
#         "1百元能够买什么",
#         "108块5毛五能够买什么",
#     ]
    
#     for text in text_samples:
#         numbers = extract_numbers(text)
#         print(f"文本: {text}")
#         print(f"提取结果: {numbers}")
#         print("-" * 40)
