"""
收益率抽取器

专注于提取金融文本中的收益率信息

输入格式:
    text: str - 包含收益率信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的收益率信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'rate_range' 或 'specific_rate'
        "minValue": float,       # 范围最小值（小数形式，如0.05表示5%）
        "maxValue": float,       # 范围最大值（小数形式），可能为 None
    }
"""

import re   
from typing import Dict, List, Any, Tuple, Pattern, Optional
from app.utils.extractor.shared.shared_utils import create_result, logger
from app.utils.extractor.num.num_utils import (
    chinese_to_num,
    RATE_UNIT_EXPR, RATE_PERIOD_PATTERN, RATE_RANGE_CONNECTOR,
    RATE_APPROXIMATE_PREFIX, RATE_APPROXIMATE_SUFFIX,
    RATE_UPPER_LIMIT_PREFIX, RATE_UPPER_LIMIT_SUFFIX,
    RATE_LOWER_LIMIT_PREFIX, RATE_LOWER_LIMIT_SUFFIX,
    SPECIFIC_RATE, RATE_RANGE, COMPLEX_NUMBER_PATTERN, RATE_UNIT_PATTERN,
    RateUnitHandler, RateModifierHandler, PERCENT_PATTERN,
    RATE_CONTEXT_KEYWORDS
)

# 收益率超级模式：统一匹配各类收益率表达式
RATE_SUPER_PATTERN_STR = fr'''
(?: 
  # 近似前缀修饰词（可选）
  (?P<approximate_prefix>{RATE_APPROXIMATE_PREFIX})?
  
  # 上限前缀修饰词（可选）
  (?P<upper_limit_prefix>{RATE_UPPER_LIMIT_PREFIX})?
  
  # 下限前缀修饰词（可选）
  (?P<lower_limit_prefix>{RATE_LOWER_LIMIT_PREFIX})?
  
  # 可选周期（非捕获，实际周期会在rate_unit中捕获）
  (?:{RATE_PERIOD_PATTERN})?
  
  (?:
    # 格式1：数字+单位+连接词+数字+单位（确保两边都有单位或周期词）
    (?P<rate_unit1>{RATE_UNIT_EXPR})
    (?P<connector>{RATE_RANGE_CONNECTOR})
    (?P<rate_unit2>{RATE_UNIT_EXPR})
    |
    # 格式3：数字+连接词+数字+单位（只有一个单位在最后）
    (?P<complex_num1>{COMPLEX_NUMBER_PATTERN})
    (?P<connector2>{RATE_RANGE_CONNECTOR})
    (?P<complex_num2>{COMPLEX_NUMBER_PATTERN})
    (?P<unit_pattern>{RATE_UNIT_PATTERN})
    |
    # 格式4：百分之X到百分之Y
    (?P<percent_expr1>{PERCENT_PATTERN})
    (?P<connector3>{RATE_RANGE_CONNECTOR})
    (?P<percent_expr2>{PERCENT_PATTERN})
    |
    # 格式5：百分之X
    (?P<percent_expr>{PERCENT_PATTERN})
    |
    # 格式2：单个收益率单元（必须包含单位或周期词）(移到后面，因为它可能匹配上下文模式中的数字部分)
    (?P<rate_unit>{RATE_UNIT_EXPR})
    |
    # 格式6：收益率上下文 + 数字 (无单位) - 添加负向先行断言
    (?P<rate_context>{RATE_CONTEXT_KEYWORDS})\s*(?:为|是|:|：|：\s*)?\s*(?!百分之)(?P<rate_context_num>{COMPLEX_NUMBER_PATTERN})
  )
  
  # 上限后缀修饰词（可选）
  (?P<upper_limit_suffix>{RATE_UPPER_LIMIT_SUFFIX})?
  
  # 下限后缀修饰词（可选）
  (?P<lower_limit_suffix>{RATE_LOWER_LIMIT_SUFFIX})?
  
  # 近似后缀修饰词（可选）
  (?P<approximate_suffix>{RATE_APPROXIMATE_SUFFIX})?
)
'''

# 编译正则表达式
RATE_SUPER_PATTERN = re.compile(RATE_SUPER_PATTERN_STR, re.VERBOSE)

class RateExtractor:
    """
    收益率抽取器
    专注于从文本中提取收益率信息（通常为年化收益率、月化收益率等带有百分比的值）
    """
    
    def extract_rate(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取收益率信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的收益率信息列表
        """
        try:
            results = []
            covered_positions = set()  # 记录已覆盖的位置
            # 处理统一收益率超级模式
            for match in RATE_SUPER_PATTERN.finditer(text):
                result = self.process_unified_rate(match)
                if result and not self._is_position_covered(match.start(), match.end(), covered_positions):
                    self._mark_covered_positions(match.start(), match.end(), covered_positions)
                    results.append(result)
            
            # 过滤和标准化提取的值
            return self._filter_values(results)
            
        except Exception as e:
            logger.warning(f"[RATE_EXTRACTOR] Warning: {e.__class__.__name__}: {e}, 原始文本: '{text}'")
            return []
    
    def _process_rate_range_units(self, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 格式1: 数字+单位+连接词+数字+单位"""
        try:
            value1 = RateUnitHandler.normalize_rate(components['rate_unit1'])
            value2 = RateUnitHandler.normalize_rate(components['rate_unit2'])
            min_value = min(value1, value2)
            max_value = max(value1, value2)
            return min_value, max_value, RATE_RANGE
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_rate_range_units Error: {e}")
            return None

    def _process_rate_range_numbers_unit(self, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 格式3: 数字+连接词+数字+单位"""
        try:
            num1 = chinese_to_num(components['complex_num1'])
            num2 = chinese_to_num(components['complex_num2'])
            unit = components['unit_pattern']
            value1 = RateUnitHandler.to_decimal(num1, unit)
            value2 = RateUnitHandler.to_decimal(num2, unit)
            min_value = min(value1, value2)
            max_value = max(value1, value2)
            return min_value, max_value, RATE_RANGE
        except (ValueError, KeyError) as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_rate_range_numbers_unit Error: {e}")
            return None

    def _process_single_rate_unit(self, match_text: str, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 格式2: 单个收益率单元（必须包含单位或周期词）"""
        try:
            value = RateUnitHandler.normalize_rate(components['rate_unit'])
            # 如果没有修饰词，直接返回 SPECIFIC_RATE
            if not RateModifierHandler.has_modifier(match_text):
                return value, value, SPECIFIC_RATE
            else:
                # 有修饰词，返回 RATE_RANGE，等待后续 apply_modifiers 处理
                return value, value, RATE_RANGE
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_single_rate_unit Error: {e}")
            return None

    def _process_percent_expr(self, match_text: str, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 格式5: 百分之X"""
        try:
            percent_text = components['percent_expr']
            number_value = chinese_to_num(percent_text)
            # 如果没有修饰词，返回 SPECIFIC_RATE
            if not RateModifierHandler.has_modifier(match_text):
                return number_value, number_value, SPECIFIC_RATE
            else:
                # 有修饰词，返回 RATE_RANGE，等待后续 apply_modifiers 处理
                return number_value, number_value, RATE_RANGE
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_percent_expr Error: {e}")
            return None

    def _process_percent_range(self, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 格式4: 百分之X到百分之Y"""
        try:
            percent_text1 = components['percent_expr1']
            percent_text2 = components['percent_expr2']
            number_value1 = chinese_to_num(percent_text1)
            number_value2 = chinese_to_num(percent_text2)
            min_value = min(number_value1, number_value2)
            max_value = max(number_value1, number_value2)
            return min_value, max_value, RATE_RANGE
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_percent_range Error: {e}")
            return None

    def _process_context_rate(self, match_text: str, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 格式6: 收益率上下文 + 数字 (无单位)"""
        try:
            number_text = components['rate_context_num']
            number_value = chinese_to_num(number_text)
            # 如果数字小于1，直接使用；否则假设是百分比，转换为小数
            decimal_value = number_value if number_value < 1 else number_value / 100.0
            # 默认是 SPECIFIC_RATE，但如果有修饰词或上下文本身就暗示范围，则为 RATE_RANGE
            slot_value = RATE_RANGE if RateModifierHandler.has_modifier(match_text) else SPECIFIC_RATE
            return decimal_value, decimal_value, slot_value
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_context_rate Error: {e}")
            return None

    def process_unified_rate(self, match) -> Optional[Dict[str, Any]]:
        """处理统一收益率模式的匹配结果"""
        match_text = match.group(0)
        components = {name: value for name, value in match.groupdict().items() if value is not None}

        initial_result: Optional[Tuple[float, float, str]] = None
        processed = False # 标记是否已通过某个helper处理

        try:
            # 尝试按优先级调用辅助处理函数
            if 'rate_unit1' in components and 'rate_unit2' in components:
                initial_result = self._process_rate_range_units(components)
                processed = True
            elif 'complex_num1' in components and 'complex_num2' in components and 'unit_pattern' in components:
                initial_result = self._process_rate_range_numbers_unit(components)
                processed = True
            elif 'percent_expr1' in components and 'percent_expr2' in components:
                initial_result = self._process_percent_range(components)
                processed = True
            elif 'percent_expr' in components:
                initial_result = self._process_percent_expr(match_text, components)
                processed = True
            elif 'rate_unit' in components:
                 # 需要在 context_rate 之前处理，因为它可能包含数字和单位
                 initial_result = self._process_single_rate_unit(match_text, components)
                 processed = True
            elif 'rate_context_num' in components:
                initial_result = self._process_context_rate(match_text, components)
                processed = True

            # 如果没有辅助函数处理成功，或返回None，则返回None
            if not processed or initial_result is None:
                logger.debug(f"[RATE_EXTRACTOR] No helper processed or returned None for: '{match_text}'")
                return None

            min_value, max_value, slot_value = initial_result

            # 应用修饰词的影响 (仅当有修饰词或上下文匹配时)
            # 注意：SPECIFIC_RATE 且有修饰词的情况，在 helper 中已经转为 RATE_RANGE
            apply_mod = RateModifierHandler.has_modifier(match_text) or 'rate_context_num' in components
            if apply_mod and slot_value == RATE_RANGE:
                # 确保传入 apply_modifiers 的值非 None
                current_min = min_value if min_value is not None else (max_value if max_value is not None else 0)
                current_max = max_value if max_value is not None else (min_value if min_value is not None else 0)
                min_value, max_value = RateModifierHandler.apply_modifiers(
                    current_min,
                    current_max,
                    match_text
                )
            elif slot_value == SPECIFIC_RATE:
                 # 如果是 SPECIFIC_RATE，确保 min 和 max 相等
                 # （处理helper返回 SPECIFIC_RATE 但 min/max 可能不同的理论情况）
                 final_value = min_value if min_value is not None else max_value
                 min_value = final_value
                 max_value = final_value

            # 创建最终结果
            if min_value is not None or max_value is not None:
                return create_result(
                    match=match_text,
                    position=(match.start(), match.end()),
                    slot="number",
                    slot_value=slot_value,
                    min_value=min_value,
                    max_value=max_value
                )
            else:
                logger.debug(f"[RATE_EXTRACTOR] Final min/max are None for: '{match_text}'")
                return None

        except Exception as e: # 捕获未预料的错误
            logger.warning(f"[RATE_EXTRACTOR] Unexpected Error in process_unified_rate for '{match_text}': {e.__class__.__name__}: {e}")
            return None

    def _filter_values(self, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤无效的收益率值"""
        filtered_values = []
        
        for value in values:
            # 获取最小值和最大值
            min_value = value.get("minValue")
            max_value = value.get("maxValue")
            
            # 跳过无效数据情况
            if min_value is None and max_value is None:
                continue
                
            # 检查有效值是否在合理范围内（不超过1.0，即100%）
            if min_value is not None and min_value > 1.0:
                continue
                
            if max_value is not None and max_value > 1.0:
                continue
                
            # 检查最小值是否非负
            if min_value is not None and min_value < 0:
                continue
                
            # 如果最小值和最大值都存在，确保最大值大于等于最小值
            if min_value is not None and max_value is not None and max_value < min_value:
                continue
                
            # 通过所有检查，添加到结果列表
            filtered_values.append(value)
                
        return filtered_values
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos) 