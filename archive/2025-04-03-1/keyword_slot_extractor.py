"""
基于关键词的槽位抽取器
负责从文本中提取基于关键词匹配的槽位信息

输入格式:
    text: str - 待提取的文本
    
输出格式:
    List[Dict[str, Any]] - 提取的槽位信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "value": str,            # 条件值名称
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值
        "position": Tuple[int, int]  # 在原文中的位置 (start, end)
    }
"""

from typing import Dict, List, Any, Tuple
import re
from resource.slot_definitions import SLOTS
from resource.keywords_mapping import COMPILED_KEYWORD_PATTERNS
from app.utils.extractor.shared.shared_utils import create_result, logger

class KeywordSlotExtractor:
    """
    基于关键词的槽位抽取器
    负责从文本中提取基于关键词匹配的槽位信息
    """
    
    def __init__(self):
        """
        初始化关键词槽位抽取器
        加载槽位定义和关键词映射
        """
        self.slots = SLOTS
        self.keyword_patterns = COMPILED_KEYWORD_PATTERNS
        # 连续期相关的正则表达式映射
        self.regex_patterns = self._compile_regex_patterns()
    
    def _compile_regex_patterns(self) -> Dict[str, re.Pattern]:
        """
        编译标记为"正则匹配"的槽位的正则表达式
        
        Returns:
            Dict[str, re.Pattern]: 槽位值对应的正则表达式模式
        """
        regex_patterns = {}
        
        # 数字映射
        num_mapping = {
            "3": "三",
            "4": "四",
            "5": "五",
            "6": "六",
            "7": "七",
            "8": "八",
            "9": "九",
            "10": "十",
            "15": "十五",
            "20": "二十"
        }
        
        # 处理连续期达到基准
        for slot, slot_info in self.slots.items():
            for value_key, value_info in slot_info["values"].items():
                # 检查keywords是否已加载并包含"正则匹配"标记
                keywords = value_info.get("keywords", [])
                if "正则匹配" not in keywords:
                    continue
                
                # 特殊处理连续期槽位
                if value_key.startswith("consecutive_") and "_periods_" in value_key:
                    # 从value_key中提取数字
                    parts = value_key.split("_")
                    if len(parts) >= 2 and parts[1].isdigit():
                        num = parts[1]
                        chinese_num = num_mapping.get(num, num)
                        
                        if "reached_benchmark" in value_key:
                            # 连续期达到基准的正则表达式模式
                            pattern = rf"(连续|持续|接连|连着|一连|不间断)\s*({num}|{chinese_num})\s*期\s*((收益|业绩|表现)?\s*((达到|达标|达|符合|满足)(基准|标准|指标|基准水平|基准收益)|收益不错)|(收益|业绩|表现)\s*(达到|达标|达|符合|满足)\s*(基准|标准|指标|基准水平|基准收益))"
                        elif "exceeded_benchmark" in value_key:
                            # 连续期超过基准的正则表达式模式
                            pattern = rf"(连续|持续|接连|连着|一连|不间断)\s*({num}|{chinese_num})\s*期\s*((收益|业绩|表现)?\s*((超过|超级|超出|胜过|胜|赢过|赢|优于|远超)(基准|标准|指标|基准水平|基准收益)|超标)|(收益|业绩|表现)\s*(超过|超级|超出|胜过|胜|赢过|赢|优于|远超)\s*(基准|标准|指标|基准水平|基准收益))"
                        elif "positive" in value_key:
                            # 连续期正收益的正则表达式模式
                            pattern = rf"(连续|持续|接连|连着|一连|不间断)\s*({num}|{chinese_num})\s*期\s*((正收益|正回报|正向收益|收益为正|业绩为正|表现为正|保持正收益)|(收益|业绩|表现)\s*(都|全部|均|总是|一直)\s*(为|是|保持)\s*(正|正向|正数|正值)|(每期|都|一直)\s*(有|获得|保持)\s*(正收益|正回报|正向收益))"
                        else:
                            continue
                       
                        regex_patterns[value_key] = re.compile(pattern)
        
        return regex_patterns
    
    def extract_keyword_slot(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取关键词槽位信息
        
        Args:
            text (str): 待提取的文本
            
        Returns:
            List[Dict[str, Any]]: 槽位匹配信息列表
        """
        try:
            # 1. 首先匹配所有可能的槽位值
            all_matches = self._find_all_matches(text)
            
            # 2. 匹配标记为正则的槽位
            regex_matches = self._find_regex_matches(text)
            
            all_matches.extend(regex_matches)
            
            # 3. 将内部格式转换为标准结果格式
            final_results = []
            for match in all_matches:
                final_results.append(create_result(
                    match=match["match"],
                    position=match["position"],
                    slot=match["slot"],
                    slot_value=match["slot_value"],
                    value=match.get("value", "")
                ))
            
            return final_results
            
        except Exception as e:
            logger.warn(f"[KEYWORD_SLOT_EXTRACTOR] Warning: {e}")
            return []
    
    def _find_regex_matches(self, text: str) -> List[Dict[str, Any]]:
        """
        查找标记为"正则匹配"的槽位
        
        Args:
            text: 待匹配的文本
            
        Returns:
            List[Dict[str, Any]]: 所有正则匹配结果
        """
        all_matches = []
        
        # 遍历所有正则表达式模式
        for value_key, pattern in self.regex_patterns.items():
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                # 找到对应的槽位和值
                slot_found = False
                for slot, slot_info in self.slots.items():
                    if value_key in slot_info["values"]:
                        # 获取槽位值名称
                        value_name = slot_info["values"][value_key]["name"]
                        is_bs_value = slot_info["values"][value_key].get("is_bs_value", True)
                        
                        value = value_name if is_bs_value else ""
                        
                        match_info = {
                            "match": match_text,
                            "value": value,
                            "slot_value": value_key,
                            "slot": slot,
                            "position": (start, end),
                            "length": len(match_text)
                        }
                        all_matches.append(match_info)
                        slot_found = True
                        break
                
                if not slot_found:
                    logger.warn(f"[KEYWORD_SLOT_EXTRACTOR] Warning: Cannot find slot for value_key {value_key}")
                
        return all_matches
    
    def _find_all_matches(self, text: str) -> List[Dict[str, Any]]:
        """
        查找文本中所有可能的关键词匹配
        
        Args:
            text: 待匹配的文本
            
        Returns:
            List[Dict[str, Any]]: 所有可能的匹配结果
        """
        all_matches = []  # 存储所有匹配结果
        
        # R值到数值的映射字典
        r_value_mapping = {
            "R1": 1,
            "R2": 2,
            "R3": 3,
            "R4": 4,
            "R5": 5
        }
        r_slot_value = "specific_risk"
        
        # 遍历所有槽位
        for slot, values in self.keyword_patterns.items():
            # 获取槽位名称
            slot_name = self.slots[slot]["name"]
            
            # 遍历该槽位下的所有条件值
            for value_key, patterns in values.items():
                # 检查这个值是否标记为正则匹配
                if self._is_regex_match(slot, value_key):
                    continue
                
                # 获取条件值的name属性
                slot_value = value_key
                value_name = self.slots[slot]["values"][value_key]["name"]
                is_bs_value = self.slots[slot]["values"][value_key].get("is_bs_value", True)
                
                # 遍历该条件值下的所有模式
                for pattern_tuple in patterns:
                    pattern, keyword = pattern_tuple
                    
                    # 使用正则表达式匹配
                    for match in pattern.finditer(text):
                        match_text = match.group()
                        start, end = match.span()
                        
                        if is_bs_value:
                            value = value_name
                        else:
                            value = ""
                        
                        # 检查并转换R值
                        converted_value = value
                        if value in r_value_mapping:
                            converted_value = r_value_mapping[value]
                            slot_value = r_slot_value
                            
                        # 记录匹配信息
                        match_info = {
                            "match": match_text,
                            "value": converted_value,  
                            "slot_value": slot_value,  
                            "slot": slot,
                            "position": (start, end),
                            "length": len(match_text)
                        }
                        all_matches.append(match_info)
        
        return all_matches
    
    def _is_regex_match(self, slot: str, value_key: str) -> bool:
        """
        检查槽位值是否标记为正则匹配
        
        Args:
            slot: 槽位名称
            value_key: 槽位值键名
            
        Returns:
            bool: 是否为正则匹配
        """
        try:
            keywords = self.slots[slot]["values"][value_key].get("keywords", [])
            return "正则匹配" in keywords
        except:
            return False