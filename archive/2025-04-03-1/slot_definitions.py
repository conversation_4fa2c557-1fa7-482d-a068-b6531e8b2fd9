"""
槽位定义文件
包含所有槽位和对应条件值的定义
"""

# 槽位定义
SLOTS = {
    "time": {
        "name": "时间期限",
        "description": "用于表示时间范围，包括相对时间范围和持续时间范围",
        "values": {
            "relative_time_range": {
                "is_bs_value": False,
                "name": "相对时间范围",
                "description": "相对于当前时间的时间范围，用于收益率查询，如近六月到近三月，统一单位为天",
                "keywords": []
            },
            "duration_time_range": {
                "is_bs_value": False,
                "name": "持续时间范围",
                "description": "持续时间的时间范围，如三个月到六个月，统一单位为天",
                "keywords": []
            },
        }
    },
    "number": {
        "name": "数值",
        "description": "用于表示产品的起购金额、收益率等数值",
        "values": {
            "amount_range": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "金额范围",
                "description": "匹配有明确金额单位的数值范围，包括最小金额～最大金额",
                "keywords": []
            },
            "specific_amount": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "具体金额",
                "description": "匹配具体金额，并将自然语言描述金额转为具体单位元",
                "keywords": []
            },
            "rate_range": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "收益率范围",
                "description": "匹配没有金额单位的收益率范围，包括最小收益率～最大收益率",
                "keywords": []
            },
            "specific_rate": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "具体收益率",
                "description": "匹配具体收益率，并将自然语言描述收益率转为具体单位百分数",
                "keywords": []
            }
        }
    },
    "risk_level": {
        "name": "风险等级",
        "description": "用于表示产品的风险等级",
        "values": {
            "R1": {
                "name": "R1",
                "description": "低风险",
                "keywords": []
            },
            "R2": {
                "name": "R2",
                "description": "中低风险",
                "keywords": []
            },
            "R3": {
                "name": "R3",
                "description": "中风险",
                "keywords": []
            },
            "R4": {
                "name": "R4",
                "description": "中高风险",
                "keywords": []
            },
            "R5": {
                "name": "R5",
                "description": "高风险",
                "keywords": []
            }
        }
    },
    "return_demand": {
        "name": "收益需求",
        "description": "用于表示收益的需求描述",
        "values": {
            "annual_return_demand": {
                "is_bs_value": False, # 表示该name值不直接等于业务码值
                "name": "年化收益率",
                "description": "年化收益率",
                "keywords": []
            },
            "daily_positive": {
                "name": "历史日日正收益",
                "description": "历史日日正收益",
                "keywords": []
            },
            "weekly_positive": {
                "name": "历史周周正收益",
                "description": "历史周周正收益",
                "keywords": []
            },
            "monthly_positive": {
                "name": "历史月月正收益",
                "description": "历史月月正收益",
                "keywords": []
            },
            "quarterly_positive": {
                "name": "历史季季正收益",
                "description": "历史季季正收益",
                "keywords": []
            },
            "yearly_positive": {
                "name": "历史年年正收益",
                "description": "历史年年正收益",
                "keywords": []
            },
            "period_positive": {
                "name": "历史期期正收益",
                "description": "历史期期正收益",
                "keywords": []
            }
        }
    },
    "specific_product": {
        "name": "产品名称",
        "description": "用于表示具体的产品名称或类型",
        "values": {
            "weekly_bao": {
                "name": "周周宝",
                "description": "周周宝产品",
                "keywords": []
            },
            "monthly_bao": {
                "name": "月月宝",
                "description": "月月宝产品",
                "keywords": []
            },
            "quarterly_bao": {
                "name": "季季宝",
                "description": "季季宝产品",
                "keywords": []
            },
            "half_year_bao": {
                "name": "半年宝",
                "description": "半年宝产品",
                "keywords": []
            },
            "multi_month_bao": {
                "name": "多月宝",
                "description": "多月宝产品",
                "keywords": []
            },
            "fixed_bao": {
                "name": "定期宝",
                "description": "定期宝产品",
                "keywords": []
            },
            "value_plus": {
                "name": "价值+",
                "description": "价值+产品",
                "keywords": []
            },
            "multi_plus": {
                "name": "多元+",
                "description": "多元+产品",
                "keywords": []
            },
            "quant_plus": {
                "name": "量化+",
                "description": "量化+产品",
                "keywords": []
            },
            "dividend_plus": {
                "name": "红利+",
                "description": "红利+产品",
                "keywords": []
            },
            "gold_plus": {
                "name": "黄金+",
                "description": "黄金+产品",
                "keywords": []
            },
            "global_plus": {
                "name": "全球+",
                "description": "全球+产品",
                "keywords": []
            },
            "structure_plus": {
                "name": "结构+",
                "description": "结构+产品",
                "keywords": []
            },
            "you_plus": {
                "name": "优+",
                "description": "优+产品",
                "keywords": []
            },
            "fast_redemption_zone": {
                "name": "快赎专区",
                "description": "快赎专区产品",
                "keywords": []
            },
            "daily_bao": {
                "name": "朝朝宝",
                "description": "朝朝宝产品",
                "keywords": []
            }
        }
    },
    "product_type": {
        "name": "产品形态",
        "description": "用于表示产品的运作方式",
        "values": {
            "closed_end": {
                "name": "封闭型",
                "description": "封闭型产品",
                "keywords": []
            },
            "regularly_open": {
                "name": "定期开放",
                "description": "定期开放产品",
                "keywords": []
            },
            "minimum_holding": {
                "name": "最短持有期",
                "description": "最短持有期产品",
                "keywords": []
            }
        }
    },
    "start_amount": {
        "name": "起购金额",
        "description": "用于表示产品的起购金额",
        "values": {
            "under_1000": {
                "name": "1千以下",
                "description": "1000元以下",
                "keywords": []
            },
            "1000_50000": {
                "name": "1千-5万",
                "description": "1000元-50000元",
                "keywords": []
            },
            "above_50000": {
                "name": "5万以上",
                "description": "50000元以上",
                "keywords": []
            }
        }
    },
    "raise_type": {
        "name": "募集方式",
        "description": "用于表示产品的募集方式",
        "values": {
            "public": {
                "name": "公募理财",
                "description": "公募理财产品",
                "keywords": []
            },
            "private": {
                "name": "私募理财",
                "description": "私募理财产品",
                "keywords": []
            }
        }
    },
    "investment_strategy": {
        "name": "投资策略",
        "description": "用于表示产品的投资策略",
        "values": {
            "cash_management": {
                "name": "活钱管理",
                "description": "活钱管理策略",
                "keywords": []
            },
            "stable_low_vol": {
                "name": "稳健低波",
                "description": "稳健低波动策略",
                "keywords": []
            },
            "stable_growth": {
                "name": "稳健增值",
                "description": "稳健增值策略",
                "keywords": []
            },
            "balanced_growth": {
                "name": "稳中求进",
                "description": "稳中求进策略",
                "keywords": []
            },
            "aggressive": {
                "name": "进取投资",
                "description": "进取投资策略",
                "keywords": []
            }
        }
    },
    "investment_direction": {
        "name": "投资方向",
        "description": "用于表示产品的投资标的",
        "values": {
            "fixed_income": {
                "name": "固定收益类",
                "description": "固定收益类资产",
                "keywords": []
            },
            "equity": {
                "name": "权益类资产",
                "description": "权益类资产",
                "keywords": []
            },
            "commodity": {
                "name": "商品及金融衍生品类资产",
                "description": "商品及金融衍生品类资产",
                "keywords": []
            },
            "cash": {
                "name": "现金类资产",
                "description": "现金类资产",
                "keywords": []
            },
            "mixed": {
                "name": "混合类资产",
                "description": "混合类资产",
                "keywords": []
            },
            "foreign_currency": {
                "name": "外币资产",
                "description": "外币类资产",
                "keywords": []
            },
            "foreign_currency_usd": {
                "name": "外币资产-美元",
                "description": "外币资产-美元",
                "keywords": []
            },
            "foreign_currency_hkd": {
                "name": "外币资产-港元",
                "description": "外币资产-港元",
                "keywords": []
            },
            "foreign_currency_eur": {
                "name": "外币资产-欧元",
                "description": "外币资产-欧元",
                "keywords": []
            },
            "foreign_currency_gbp": {
                "name": "外币资产-英镑",
                "description": "外币资产-英镑",
                "keywords": []
            },
            "foreign_currency_aud": {
                "name": "外币资产-澳元",
                "description": "外币资产-澳元",
                "keywords": []
            },
            "foreign_currency_sgd": {
                "name": "外币资产-新元",
                "description": "外币资产-新元",
                "keywords": []
            }
        }
    },
    "special_label": {
        "name": "其他标签",
        "description": "用于表示产品的特殊标签",
        "values": {
            "high_popularity": {
                "name": "高人气",
                "description": "高人气产品",
                "keywords": []
            },
            "low_drawdown": {
                "name": "历史回撤小",
                "description": "历史回撤小",
                "keywords": []
            },
            "early_benefit": {
                "name": "早一天享收益",
                "description": "早一天享收益",
                "keywords": []
            },
            "anti_decline": {
                "name": "抗跌表现好",
                "description": "抗跌表现好",
                "keywords": []
            },
            "long_term_finance": {
                "name": "长盈理财",
                "description": "长盈理财",
                "keywords": []
            },
            "locked_coupon": {
                "name": "锁定票息",
                "description": "锁定票息",
                "keywords": []
            },
            "fixed_plus_reits": {
                "name": "固收+公募REITs",
                "description": "固收+公募REITs",
                "keywords": []
            },
            "fixed_plus_structured": {
                "name": "固收+结构化",
                "description": "固收+结构化",
                "keywords": []
            },
            "absolute_return": {
                "name": "绝对收益",
                "description": "绝对收益",
                "keywords": []
            },
            "fixed_plus_high_dividend": {
                "name": "固收+高股息",
                "description": "固收+高股息",
                "keywords": []
            },
            "maturity_mismatch": {
                "name": "期限错配",
                "description": "期限错配",
                "keywords": []
            },
            "stable_dividend": {
                "name": "稳定分红",
                "description": "稳定分红",
                "keywords": []
            },
            "long_term_selection": {
                "name": "长钱优选",
                "description": "长钱优选",
                "keywords": []
            },
            "selected_assets": {
                "name": "精选资产",
                "description": "精选资产",
                "keywords": []
            },
            "target_profit": {
                "name": "目标止盈",
                "description": "目标止盈",
                "keywords": []
            },
            "fixed_plus_preferred": {
                "name": "固收+优先股",
                "description": "固收+优先股",
                "keywords": []
            },
            "quantitative_neutral": {
                "name": "量化中性",
                "description": "量化中性",
                "keywords": []
            },
            "low_vol_fixed_plus": {
                "name": "低波固收＋",
                "description": "低波固收＋",
                "keywords": []
            },
            "value_added_selection": {
                "name": "增值优选",
                "description": "增值优选",
                "keywords": []
            },
            "convertible_bond": {
                "name": "可转债",
                "description": "可转债",
                "keywords": []
            },
            "private_banking": {
                "name": "私行尊享",
                "description": "私行尊享",
                "keywords": []
            },
            "fast_redemption": {
                "name": "赎回到账快",
                "description": "赎回到账快",
                "keywords": []
            },
            "over_hundred_thousand_holders": {
                "name": "此系列超十万人持有",
                "description": "此系列超十万人持有",
                "keywords": []
            },
            "over_ten_thousand_repurchases": {
                "name": "此系列超万人复购",
                "description": "此系列超万人复购",
                "keywords": []
            },
            "support_regular_investment": {
                "name": "支持定投",
                "description": "支持定投",
                "keywords": []
            },
            "recent_week_hot_search": {
                "name": "近一周热搜",
                "description": "近一周热搜",
                "keywords": []
            },
            "latest_release": {
                "name": "最新发售",
                "description": "最新发售",
                "keywords": []
            },
            "recent_week_over_ten_thousand_views": {
                "name": "近一周超万人浏览",
                "description": "近一周超万人浏览",
                "keywords": []
            },
            "all_periods_reached_benchmark": {
                "name": "往期收益均达基准",
                "description": "往期收益均达基准",
                "keywords": []
            },
            "consecutive_20_periods_reached_benchmark": {
                "name": "连续20期收益达基准",
                "description": "连续20期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_15_periods_reached_benchmark": {
                "name": "连续15期收益达基准",
                "description": "连续15期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_10_periods_reached_benchmark": {
                "name": "连续10期收益达基准",
                "description": "连续10期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_9_periods_reached_benchmark": {
                "name": "连续9期收益达基准",
                "description": "连续9期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_8_periods_reached_benchmark": {
                "name": "连续8期收益达基准",
                "description": "连续8期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_7_periods_reached_benchmark": {    
                "name": "连续7期收益达基准",
                "description": "连续7期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_6_periods_reached_benchmark": {
                "name": "连续6期收益达基准",
                "description": "连续6期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_5_periods_reached_benchmark": {
                "name": "连续5期收益达基准",
                "description": "连续5期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_4_periods_reached_benchmark": {
                "name": "连续4期收益达基准",
                "description": "连续4期收益达基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_3_periods_reached_benchmark": {
                "name": "连续3期收益达基准",
                "description": "连续3期收益达基准",
                "keywords": ["正则匹配"]
            },
            "all_periods_exceeded_benchmark": {
                "name": "往期收益均超基准",
                "description": "往期收益均超基准",
                "keywords": []
            },
            "consecutive_20_periods_exceeded_benchmark": {
                "name": "连续20期收益超基准",
                "description": "连续20期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_10_periods_exceeded_benchmark": {
                "name": "连续10期收益超基准",
                "description": "连续10期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_9_periods_exceeded_benchmark": {
                "name": "连续9期收益超基准",
                "description": "连续9期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_8_periods_exceeded_benchmark": {
                "name": "连续8期收益超基准",
                "description": "连续8期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_7_periods_exceeded_benchmark": {
                "name": "连续7期收益超基准",
                "description": "连续7期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_6_periods_exceeded_benchmark": {
                "name": "连续6期收益超基准",
                "description": "连续6期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_5_periods_exceeded_benchmark": {
                "name": "连续5期收益超基准",
                "description": "连续5期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_4_periods_exceeded_benchmark": {
                "name": "连续4期收益超基准",
                "description": "连续4期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_3_periods_exceeded_benchmark": {
                "name": "连续3期收益超基准",
                "description": "连续3期收益超基准",
                "keywords": ["正则匹配"]
            },
            "consecutive_20_periods_positive": {
                "name": "连续20期正收益",
                "description": "连续20期正收益",
                "keywords": ["正则匹配"]
            },
            "consecutive_15_periods_positive": {
                "name": "连续15期正收益",
                "description": "连续15期正收益",
                "keywords": ["正则匹配"]
            },
            "consecutive_10_periods_positive": {
                "name": "连续10期正收益",
                "description": "连续10期正收益",
                "keywords": ["正则匹配"]
            },
            "consecutive_5_periods_positive": {
                "name": "连续5期正收益",
                "description": "连续5期正收益",
                "keywords": ["正则匹配"]
            }
            
        }
    }
} 