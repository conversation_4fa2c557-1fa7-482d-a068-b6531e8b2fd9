"""
时间抽取相关常量和通用工具类
"""
from typing import Tuple, List
import re
from app.utils.extractor.num.num_utils import chinese_to_num, COMPLEX_NUMBER_PATTERN

# =========== 基础常量 ===========
# 中文数字映射
CHINESE_DIGITS = {
    '零': 0, '〇': 0, '0': 0,
    '一': 1, '壹': 1, '1': 1,
    '二': 2, '贰': 2, '兩': 2, '两': 2, '2': 2,
    '三': 3, '叁': 3, '3': 3,
    '四': 4, '肆': 4, '4': 4,
    '五': 5, '伍': 5, '5': 5,
    '六': 6, '陆': 6, '陸': 6, '6': 6,
    '七': 7, '柒': 7, '7': 7,
    '八': 8, '捌': 8, '8': 8,
    '九': 9, '玖': 9, '9': 9,
    '十': 10, '拾': 10,
    '百': 100, '佰': 100,
    '千': 1000, '仟': 1000,
    '万': 10000, '萬': 10000,
    '亿': 100000000, '億': 100000000,
    '半': 0.5  # 添加"半"对应的数值
}

# 时间单位定义
class TimeUnits:
    """时间单位定义类"""
    
    # 时间单位映射（原始单位 -> 标准单位）
    UNIT_MAPPING = {
        # 年相关
        "年": "年", "年份": "年",
        # 月相关
        "月": "月", "个月": "月", "個月": "月", "月份": "月", "个月份": "月",
        # 周相关
        "周": "周", "星期": "周", "礼拜": "周", "週": "周", "个周": "周", "个星期": "周", "個周": "周",
        # 天相关
        "天": "天", "日": "天", "号": "天", "號": "天", "个工作日": "天", "工作日": "天",
        # 季度相关
        "季度": "季度", "季": "季度", "个季度": "季度", "个季": "季度",
    }
    
    # 时间单位转换为天数的比例
    DAYS_CONVERSION = {
        "年": 366,
        "月": 30, 
        "季度": 90,
        "周": 7,
        "天": 1
    }
    
    @classmethod
    def get_all_units(cls) -> List[str]:
        """获取所有时间单位"""
        return list(cls.UNIT_MAPPING.keys())
    
    @classmethod
    def get_standard_unit(cls, unit: str) -> str:
        """获取标准化的时间单位"""
        return cls.UNIT_MAPPING.get(unit, unit)
    
    @classmethod
    def get_pattern(cls) -> str:
        """获取时间单位的正则表达式模式"""
        return f"({'|'.join(cls.get_all_units())})"

# 时间单位模式（使用TimeUnits类中的定义）
TIME_UNIT_PATTERN = TimeUnits.get_pattern()

# 槽位类型
RELATIVE_TIME_RANGE = "relative_time_range"
DURATION_TIME_RANGE = "duration_time_range"

# 时间范围连接词
TIME_RANGE_CONNECTOR = r'(到|至|~|-|—|－|→|、|和|与|或|及|–|⁓|‐|‑|‒|∼|～|∿|➝|➞|⟶|⇒|_|⁃|…)'

# 复合时间连接词：用于连接多个时间单位形成复合时间
TIME_COMPOUND_CONNECTOR = r'(零|又|和|加|以及|再)'

# 半单位模式（"半年"、"半个月"等）
HALF_UNIT_PATTERN = fr'半(?:个|個)?({TIME_UNIT_PATTERN})'

# 单位加半模式（"一年半"、"两月半"等）
UNIT_HALF_PATTERN = fr'({COMPLEX_NUMBER_PATTERN})({TIME_UNIT_PATTERN})半'

# 定义基本时间单元模式
TIME_UNIT_EXPR = fr'''
(?:
  # 复合时间（如"1年3个月"）
  (?:{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN}
    (?:{TIME_COMPOUND_CONNECTOR}?{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN})+)
  |
  # 单位加半表达式（如"一年半"）
  (?:{UNIT_HALF_PATTERN})
  |
  # 半单位表达式（如"半年"、"半个月"）
  (?:{HALF_UNIT_PATTERN})
  |
  # 简单时间单位（数字+单位）
  (?:{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN})
  
)
'''
# =========== 持续时间提取器常量 ===========

# 近似修饰前缀：出现在时间单位前的近似修饰词
DURATION_APPROXIMATE_PREFIX = r'(大约|大概|约|约为|接近|差不多|估计|粗略|基本|大致|几乎|将近|可能|或许|差点|将|快|近乎)'

# 近似修饰后缀：出现在时间单位后的近似修饰词
DURATION_APPROXIMATE_SUFFIX = r'(左右|上下|内外|之间|出头|之久|多|许|不到|不足|开外|有余|上下浮动|上下波动|大概)'

# 其他修饰后缀：集合了各类修饰词，用于模式匹配但不影响值的计算
DURATION_OTHER_SUFFIX = r'(的时间|的期限|的期间|的周期|时间|期限|期间|周期|的跨度|的长度|的过程|的进程|的阶段|的间隔|的持续时间|的范围|的区间)'

# 上限前缀修饰词：放在时间单位前，表示时间的最大限制
DURATION_UPPER_LIMIT_PREFIX = r'(不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|低于|低於|短於|短于|不会超过|不会多于|不会多於|充其量|至多|最长|最长不超过|上限是|不会超出|最大|最大不超过|控制在)'

# 上限后缀修饰词：放在时间单位后，表示时间的最大限制
DURATION_UPPER_LIMIT_SUFFIX = r'(内|以下|以内|之内|及以下|及以内|及之内|未满|不到|不足|为限|为界|为上限|内完成|内结束|内搞定|内完工|内解决)'

# 下限前缀修饰词：放在时间单位前，表示时间的最小限制
DURATION_LOWER_LIMIT_PREFIX = r'(最少|至少|不少于|不少於|多于|多於|起码|高于|高於|长于|长於|不低于|不低於|不短于|不短於|至少需要|需要|须|必须|要|怎么也得|最起码|最低|底线是|不会少于|下限是|最小)'

# 下限后缀修饰词：放在时间单位后，表示时间的最小限制
DURATION_LOWER_LIMIT_SUFFIX = r'(以上|之上|及以上|及之上|开外|起|起步|打底|为下限|为基础|为底线|以外|为起点|或更长|或更多|才可以|往上|或更久|以后)'

# 中缀模式特殊前缀
DURATION_FIRST_PREFIX = r'(短则|至少|最少|最短|短期内|短期来看|短的话|往短了说|保守估计|保守来看|最快|快则|短期|初期|早期|前期|开始阶段)'

# 中缀模式特殊后缀
DURATION_SECOND_PREFIX = r'(长则|最多|最长|长期来看|长的话|往长了说|乐观估计|乐观来看|最慢|慢则|长期|后期|晚期|末期|结束阶段)'

# 定义中缀分隔符
DURATION_INFIX_SEPARATOR = r'(?:[,，;；、:：]|\s+|而|但|但是|\n|\r\n)?\s*'

# 短期
# 定义各种子模式
# 1. 短期形容词
SHORT_TERM_ADJ = r'(?:短|较短|很短|超短|相当短|特别短|极短|稍短|偏短|略短|快|小|少|即期)'
# 2. 时间名词
TIME_NOUNS = r'(?:期|时间|时长|周期|阶段|期限|封闭期)'
# 3. 常见修饰语
MODIFIERS = r'(?:点|一点|些|一些|点儿|些许|一段|一段时间|区间)'
# 4. 理财相关名词
FINANCE_NOUNS = r'(?:理财|投资|产品|项目|标的|品种|资金|钱|款项|操作|策略)'
# 5. 场景修饰词
SCENE_MODIFIERS = r'(?:内|来看|来说|而言|考虑)'
# 6. 短期时间单位
SHORT_TIME_UNITS = r'(?:天|日|小时|钟头|星期|礼拜|周)'
# 7. 短期数量词
SHORT_QUANTITY = r'(?:几|一两|两三|三五|一个|两个|几个)'
# 8. 需求动词
REQUIREMENT_VERBS = r'(?:有没有|有|找|需要|想要|想找|要找|推荐|希望|想|想要)'
# 9. 强度修饰词
INTENSITY_MODIFIERS = r'(?:很|相当|比较|足够|非常|更|再|稍|略|偏)'

# 组合短期模式
SHORT_TERM_PATTERN = r'(' + '|'.join([
    # 模式1: 短+时间名词 (短期、短时间等)
    f'{SHORT_TERM_ADJ}{TIME_NOUNS}(?:{SCENE_MODIFIERS})?(?:的{FINANCE_NOUNS})?',
    
    # 模式2: 短+修饰语 (短点、短一些等)
    f'{SHORT_TERM_ADJ}{MODIFIERS}(?:的{FINANCE_NOUNS})?',
    
    # 模式3: 时间名词+短 (时间短、周期短等)
    f'{TIME_NOUNS}{SHORT_TERM_ADJ}(?:{MODIFIERS})?(?:的{FINANCE_NOUNS})?',
    
    # 模式4: 强度词+短 (很短、比较短等)
    f'{INTENSITY_MODIFIERS}{SHORT_TERM_ADJ}(?:{MODIFIERS})?(?:的{FINANCE_NOUNS})?',
    
    # 模式5: 短期产品类型 (短线理财、短期投资等)
    f'(?:短期|短暂|短线|超短线){FINANCE_NOUNS}',
    
    # 模式6: 短+的+理财 (短的理财、快的投资等)
    f'{SHORT_TERM_ADJ}的{FINANCE_NOUNS}',
    
    # 模式7: 特定短期表达 (几天、几个周等)
    f'{SHORT_QUANTITY}{SHORT_TIME_UNITS}(?:的|内|之内|以内)?(?:{FINANCE_NOUNS})?',
    
    # 模式8: 需求+短期 (需要短期理财、找短期投资等)
    f'{REQUIREMENT_VERBS}{SHORT_TERM_ADJ}(?:{TIME_NOUNS})?(?:的)?(?:{FINANCE_NOUNS})?',
    
    # 模式9: 需求+特定时间 (需要几天的理财、想找一周的投资等)
    f'{REQUIREMENT_VERBS}(?:{SHORT_QUANTITY})?{SHORT_TIME_UNITS}(?:的|内|之内|以内)?(?:{FINANCE_NOUNS})?',
    
    # 模式10: 短期使用场景 (短期不用、短期闲置等)
    f'(?:短期|短暂|短线)(?:不用|不需要|用不到|不会用到|可用|闲置)(?:的)?(?:{FINANCE_NOUNS})?',
    
    # 模式11: 临时性表达 (临时一下子、暂时几天等)
    f'(?:临时|暂时)(?:一下子|一会儿|几天|很快)(?:不用|用不到|闲置|放一下)(?:的(?:{FINANCE_NOUNS})?)?',
    
    # 模式12: 快速操作意图 (快点到期、尽快回本等)
    f'(?:快点|尽快|尽早)(?:到期|回本|兑现|赎回|变现)'
]) + ')'

# 中期
MIDDLE_TERM_PATTERN = r'((?:季度|按季度|季度性|季|按季|每季度|每季)(?:的|型|为单位|周期|期限)?(?:理财|投资|产品|项目|标的|品种|操作|策略)?|(?:一时半会|一时三刻|这段时间|这一阵子|这一段)(?:不用|用不到|不需要|闲置|用不着)?(?:的)?(?:资金|钱|款项|理财|投资)?|(?:中|中等|适中|适度|一般|普通)(?:期|时间|周期|阶段|长度)(?:内|来看|来说|而言|考虑|的(?:理财|投资|产品|项目|标的|品种))?|(?:中长期|中期偏长)(?:的)?(?:理财|投资|产品|项目|标的|品种)?|(?:中短期|中期偏短)(?:的)?(?:理财|投资|产品|项目|标的|品种)?|(?:不长不短|不多不少|刚刚好|刚好|适中|适度|一般|普通)(?:的)?(?:时间|周期|期限|期间|理财|投资|产品)?|(?:过渡期|缓冲期|过渡阶段|中间期|中期阶段)(?:间)?(?:的)?(?:理财|投资|产品|项目)?|(?:暂缓|缓一缓|稍等|稍后|延后)(?:一段时间|一阵子|一下|片刻)(?:的)?(?:理财|投资)?|(?:阶段性)(?:放置|存放|不用|投资|理财|规划)?(?:的)?(?:资金|资产|钱款)?|(?:(?:几|一些|两三|三五|数|这几)(?:个)?月|半年(?:左右)?|(?:两|三|四|五|六)个月)(?:的时间|时间|左右|上下|内|之内)?(?:不用|不需要|用不到|的(?:理财|投资|产品))?|(?:放|存|搁置)(?:几|两三|三五|数)(?:个)?月(?:的)?(?:理财|投资|资金)?|(?:中等|适中)(?:期限|时长|周期)(?:的)?(?:理财|投资|产品)?|(?:季度性|月度性|季度级|月度级)(?:的)?(?:理财|投资|产品|周期)?|(?:最近|近期)(?:(?:几个月|一段时间|一阵子)(?:内|之内)?)?(?:不用|不需要|用不到|不想用|闲置)?(?:的)?(?:理财|投资|资金|产品|钱)?|(?:暂时|暂且)(?:(?:几个月|半年))?(?:不(?:需要|会|想|用)用|不打算用|用不到|闲置|不用)?(?:的)?(?:理财|投资|资金|产品|钱)?)'

# 长期
# 定义长期特有的子模式
# 1. 长期形容词
LONG_TERM_ADJ = r'(?:长|较长|很长|超长|相当长|特别长|极长|长远|稍长|偏长|略长|久|永久|恒久|持久|长久)'
# 2. 长期时间标识词
LONG_TIME_INDICATORS = r'(?:一直|始终|永远|永久|一辈子|长久|长期|长时间|很长时间|老是|总是|长线|中长线|很久|许久|相当久|很长一段时间|好长时间)'
# 3. 长期时间单位
LONG_TIME_UNITS = r'(?:年|几年|很多年|许多年)'
# 4. 存放动词
STORAGE_VERBS = r'(?:放着|存放|搁置|存|放)'
# 5. 闲置词汇
IDLE_WORDS = r'(?:不用|用不到|不需要|用不着|闲置|不会用到|不会用|放一边)'

# 组合长期模式
LONG_TERM_PATTERN = r'(' + '|'.join([
    # 模式1: 长期时间标识词 (长期、一直、永久等)
    f'{LONG_TIME_INDICATORS}(?:{IDLE_WORDS})?(?:的)?(?:{FINANCE_NOUNS})?',
    
    # 模式2: 长+时间名词 (长期、长时间等)
    f'{LONG_TERM_ADJ}(?:{TIME_NOUNS}|久)(?:内|限|{SCENE_MODIFIERS})?(?:的{FINANCE_NOUNS})?',
    
    # 模式3: 长+修饰语 (长点、长一些等)
    f'{LONG_TERM_ADJ}{MODIFIERS}(?:的{FINANCE_NOUNS})?',
    
    # 模式4: 时间名词+长 (时间长、周期长等)
    f'{TIME_NOUNS}{LONG_TERM_ADJ}(?:{MODIFIERS})?(?:的{FINANCE_NOUNS})?',
    
    # 模式5: 强度词+长 (很长、比较长等)
    f'{INTENSITY_MODIFIERS}{LONG_TERM_ADJ}(?:{MODIFIERS})?(?:的{FINANCE_NOUNS})?',
    
    # 模式6: 长+的+理财 (长的理财、久的投资等) - 确保匹配"长的理财"
    f'{LONG_TERM_ADJ}的{FINANCE_NOUNS}',
    
    # 模式7: 年为单位表达 (年为单位、几年、很多年等)
    f'{LONG_TIME_UNITS}(?:的时间|时间)?(?:内|之内)?(?:{IDLE_WORDS})?',
    
    # 模式8: 存放很久 (放很久、存几年等)
    f'(?:{STORAGE_VERBS})(?:个|一个|好几个)?(?:很|相当|比较|足够|非常)?(?:久|{LONG_TIME_UNITS})',
    
    # 模式9: 需求+长期 (需要长期理财、找长期投资等)
    f'{REQUIREMENT_VERBS}{LONG_TERM_ADJ}(?:{TIME_NOUNS})?(?:的)?(?:{FINANCE_NOUNS})?',
    
    # 模式10: 长期产品类型 (长线理财、中长线投资等)
    f'(?:长线|中长线){FINANCE_NOUNS}',
    
    # 模式11: 长久存放表达
    f'(?:年)(?:为单位|计|以上)的(?:时间|周期|存放|放置|{FINANCE_NOUNS})?',
    
    # 模式12: 长久类型修饰语
    f'(?:长久|持久|恒久|永久)(?:的|地)?(?:存放|保存|放置|{FINANCE_NOUNS})?'
]) + ')'

# T+0
T_PLUS_0_PATTERN = r'(T\+0|T0|T0交易|T0赎回)'

# T+1
T_PLUS_1_PATTERN = r'(T\+1|T1|T1赎回)'

# =========== 公共数据结构定义 ===========

class TimeUnitHandler:
    """时间单位处理器"""
    
    @classmethod
    def standardize(cls, unit: str) -> str:
        """标准化时间单位"""
        return TimeUnits.get_standard_unit(unit)
    
    @classmethod
    def to_days(cls, value: float, unit: str) -> float:
        """将时间单位转换为天数"""
        standard_unit = cls.standardize(unit)
        return value * TimeUnits.DAYS_CONVERSION.get(standard_unit, 1)
    
    @classmethod
    def parse_number_and_unit(cls, text: str) -> Tuple[float, str]:
        """从文本中解析数字和单位"""
        # 提取数字
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        number_text = number_match.group(0) if number_match else "1"
        number = chinese_to_num(number_text)
        
        # 提取单位
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else "天"
        
        # 处理"半"字
        if "半" in text:
            if text.startswith("半"):
                number = 0.5
            elif "半" in text and number > 0:
                number += 0.5
                
        return number, unit
    
    @classmethod
    def is_year_identifier(cls, text: str) -> bool:
        """
        判断文本是否为年份标识符（如2024年，2000年，2024等）
        
        Args:
            text: 要检查的文本
            
        Returns:
            bool: 如果文本匹配年份标识符规则，则返回True
        """
        # 检查是否为纯数字年份（如2024）
        if text.isdigit() and len(text) == 4 and int(text) > 1000:
            return True
            
        # 提取数字和单位
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        
        # 如果只有数字没有单位，但是符合年份格式
        if number_match and number_match.group(0) == text:
            number = chinese_to_num(number_match.group(1))
            if 1000 <= number <= 2100:  # 合理的年份范围
                return True
        
        # 检查带单位的年份
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        
        if number_match and unit_match:
            number = chinese_to_num(number_match.group(1))
            unit = unit_match.group(1)
            
            # 判断是否为年份标识
            if unit in ["年", "年份"] and number >= 1000:
                return True
                
        return False
        
class TimeModifierHandler:
    """时间修饰词处理器"""
    
    @staticmethod
    def has_upper_limit_modifier(text: str) -> bool:
        """检查是否有上限修饰词"""
        return bool(re.search(fr'({DURATION_UPPER_LIMIT_PREFIX}|{DURATION_UPPER_LIMIT_SUFFIX})', text))
    
    @staticmethod
    def has_lower_limit_modifier(text: str) -> bool:
        """检查是否有下限修饰词"""
        return bool(re.search(fr'({DURATION_LOWER_LIMIT_PREFIX}|{DURATION_LOWER_LIMIT_SUFFIX})', text))
    
    @classmethod
    def apply_modifiers(cls, min_value: float, max_value: float, text: str) -> Tuple[float, float]:
        """
        应用修饰词调整时间范围
        
        Args:
            min_value: 初始最小值（天数）
            max_value: 初始最大值（天数）
            text: 原始文本
            
        Returns:
            Tuple[float, float]: 调整后的(最小值, 最大值)，最大值可能为None表示无上限
        """
        # 检查是否有各类修饰词
        has_lower_limit = cls.has_lower_limit_modifier(text)
        has_upper_limit = cls.has_upper_limit_modifier(text)
        
        # 情况1: 单一值 (min_value == max_value)
        if min_value == max_value:
            base_days = min_value
            
            # 下限修饰词：如"至少3个月"，表示最小值是3个月，最大值无限
            if has_lower_limit:
                min_value = base_days
                max_value = None
            
            # 上限修饰词：如"不超过3个月"，表示最大值是3个月，最小值为0
            elif has_upper_limit:
                min_value = 0
                max_value = base_days
                
        # 情况2: 范围值 (min_value != max_value)
        else:
            # 处理下限修饰词：例如"至少3-6个月"，表示最小值是6个月，最大值无限
            if has_lower_limit:
                min_value = max_value  # 取范围中的最大值作为下限
                max_value = None       # 无上限
            
            # 处理上限修饰词：例如"不超过3-6个月"，表示最大值是6个月，最小值为0
            elif has_upper_limit:
                min_value = 0          # 最小值设为0
                # max_value保持不变
        
        return min_value, max_value
