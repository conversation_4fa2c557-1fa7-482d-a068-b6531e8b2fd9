"""
时间抽取器

专注于提取金融文本中的时间信息，抽取两类时间范围：
1. 相对时间范围：如"最近七天"、"过去三到六个月"、"今年以来"等
2. 持续时间范围：如"一年半"、"90天到180天"、"三年左右"等

特别说明：
1. 一年按366天计算，一个月按30天计算
2. 相对时间范围的默认锚点是当前时间，除非有明确的时间锚点，当前时间锚点的天数默认为0，过去时间锚点的天数为负数，未来时间锚点的天数为正数。
3. 持续时间是指具体的时间跨度，如果有最小最大时间跨度描述，则最小最大值为对应的描述转天数，如果只有单个时间跨度描述，则最小最大值为该描述转天数。

模式设计：

- 相对时间修饰词：相对某个时间锚点的描述，默认相对当前时间
    - 相对过去：
        prefix: [过去|最近|前|近|以来|至今|截至|截止|这|本|今|当前|現在|目前|此]
        suffix: [前|以前|之前|为止|以来]
        infix: [到|至|~|-|、|和|与|或|及]
        compound: [prefix] + 时间单位 + [infix] + [suffix]，如"过去三天以前"、"今年以来"
    - 相对未来：
        prefix: [接下来|未来|将来|即将|今后|往后]
        suffix: [后|以后|之后]
        infix: [到|至|~|-|、|和|与|或|及]
        compound: [prefix] + 时间单位 + [infix] + [suffix]，如"接下来三天之后"

- 时间范围修饰词：对时间跨度范围的限定修饰
    - 上限：[不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|以下|以内|之内|内|未满]
    - 下限：[以上|之上|开外|起|最少|至少|不少于|不少於|多于|多於|起码]
    - 近似：[约|大约|大概|差不多|将近|接近|几乎|幾乎|左右|上下|多|出头|来]
    - 复合：<上限><近似><下限>

- 时间跨度描述词：描述时间跨度的单位或数值
    - 基本单位：[天|日|来天|多天|几天|月|个月|個月|几月|来月|多个月|几个月|年|周年|来年|多年|几年|周|星期|礼拜|週|禮拜|来周|多周|几周|季度|季|个季度|個季度]
    - 数量描述：[一二三四五六七八九十百千万萬亿億兩壹贰叁肆伍陆柒捌玖拾佰仟]|[0-9]+]
    - 复合时间表达：如："三年零6个月"、"一千二百五六天"等

- 时间锚点描述词：描述具体或模糊的时间锚点
    - 年份标记：[今年|去年|前年|明年|后年|[0-9]{4}年|本年|年初|年中|年末|年底|上半年|下半年]
    - 月份标记：[[一二三四五六七八九十]{1,2}月份|[1-9]月|1[0-2]月|本月|上个月|上個月|上月|上一个月|这个月|这一个月|月初|月中|月底|月末]
    - 季度标记：[第[一二三四]季度|[一二三四]季度|本季度|上个季度|上個季度|本财年|本財年|今年财年|今年財年|上季度|这个季度|这一季度|季初|季中|季末|季度末]
    - 周标记：[本周|上周|上週|上个星期|上個星期|星期[一二三四五六日天]|周[一二三四五六日天]|禮拜[一二三四五六日天]]
    - 日标记：[今天|昨天|前天|明天|后天|大前天|大后天|[1-9]号|[1-2][0-9]号|3[0-1]号|[1-9]日|[1-2][0-9]日|3[0-1]日]
    - 财年表达: [本财年|本財年|今年财年|今年財年]
    - 特殊时间点: [迄今为止|截止目前|至今|前几天|前幾天]

处理原则：

1、相对时间（relative_time_range） > 时间跨度（duration_time_range）
2、组合时间表达式 > 单一时间表达式
3、带修饰限定的 > 不带修饰限定的
4、长文本 > 短文本
5、原则1 > 原则2 > 原则3 > 原则4

保证提供一个TimeExtractor类，以及一个extract_time方法，该方法接收一个字符串，返回一个列表。

输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'relative_time_range' 或 'duration_time_range'
        "minValue": float,       # 范围最小值（天数），可能为 None，但不与maxValue同时为None
        "maxValue": float,       # 范围最大值（天数），可能为 None，但不与minValue同时为None
    }

代码要求：
1、统一的模式、变量、常量定义。
2、尽可能清晰的流程和逻辑设计，注重代码函数复用。
3、时间标准化时设计多的处理步骤，简化每一步的处理，避免复杂的嵌套处理，同时最大程度保证处理的完整性。比如：
    1、统一复杂中文日期单位表达到标准中文日期单位表达，比如：月、个月、几月、来月、多个月 -> 月
    2、再统一中文日期单位到天数，比如：年 -> 366天，月 -> 30天，周 -> 7天，季度 -> 90天
    3、再进行限定词的使用，返回最终minValue和maxValue
    以上只是举例，要拆分复杂逻辑，简化每一步的处理，避免复杂的嵌套处理，同时最大程度保证处理的完整性。
4、相对时间的标准化特别注意，都是相对于当前时间，如果文本中明确指定了时间锚点，则以时间锚点为准。比如：
    1、"今年以来"，当前时间锚点为当前时间，minValue为当前时间到年初（1月1日）的天数的负数，maxValue为当前时间的天数0天。
    2、"过去三个月"，当前时间锚点为当前时间，minValue为当前时间到过去三个月（3个月）的天数的负数，maxValue为当前时间的天数0天。
    3、"过去三个月到未来三个月"，当前时间锚点为当前时间，minValue为当前时间到过去三个月（3个月）的天数，maxValue为当前时间到未来三个月（3个月）的天数
"""

import re
from typing import List, Dict, Tuple, Any, Optional, Pattern
import datetime

# 常量定义

# 时间单位标准化映射（复杂时间单位映射到标准时间单位）
SIMPLIFIED_TIME_UNITS = {
    # 天相关
    "天": "天", 
    "日": "天",
    "来天": "天",
    "多天": "天",
    "几天": "天",
    
    # 月相关
    "月": "月",
    "个月": "月",
    "個月": "月",
    "几月": "月",
    "来月": "月",
    "多个月": "月",
    "几个月": "月",
    
    # 年相关
    "年": "年",
    "周年": "年",
    "来年": "年",
    "多年": "年",
    "几年": "年",
    
    # 周相关
    "周": "周",
    "星期": "周",
    "礼拜": "周",
    "週": "周",
    "禮拜": "周",
    "来周": "周",
    "多周": "周",
    "几周": "周",
    
    # 季度相关
    "季度": "季度",
    "季": "季度",
    "个季度": "季度",
    "個季度": "季度",
}

# 标准时间单位转换为天数（只需要定义标准单位）
TIME_UNIT_TO_DAYS = {
    "天": 1,
    "月": 30,
    "年": 366,
    "周": 7,
    "季度": 90,
}

# 相对时间修饰词
PAST_PREFIX = r"过去|最近|前|近|以来|至今|截至|截止|这|本|今|当前|現在|目前|此"  # 移除单独的"上"
SPECIAL_PAST_PREFIX = r"上"  # 特殊处理的前缀，需要进行上下文判断
PAST_SUFFIX = r"前|以前|之前|为止|以来"
FUTURE_PREFIX = r"接下来|未来|将来|即将|今后|往后"  # 这些词单独出现时不匹配
FUTURE_SUFFIX = r"后|以后|之后"
TIME_INFIX = r"到|至|~|-|、|和|与|或|及"

# 时间范围修饰词
UPPER_LIMIT_WORDS = r"不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|以下|以内|之内|内|未满"
LOWER_LIMIT_WORDS = r"以上|之上|开外|起|最少|至少|不少于|不少於|多于|多於|起码"
APPROXIMATE_WORDS = r"约|大约|大概|差不多|将近|接近|几乎|幾乎|左右|上下|多|出头|来"
DURATION_INFIX_WORDS = r"之间|之中|范围内|范圍內|区间|區間"

# 时间单位 - 使用SIMPLIFIED_TIME_UNITS的所有键作为时间单位正则模式
TIME_UNITS = "|".join(SIMPLIFIED_TIME_UNITS.keys())

# 中文数字
CHINESE_DIGITS = {
    '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9,
    '十': 10, '百': 100, '千': 1000, '万': 10000, '亿': 100000000,
    '兩': 2, '两': 2,
    '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5, '陆': 6, '柒': 7, '捌': 8, '玖': 9,
    '拾': 10, '佰': 100, '仟': 1000, '萬': 10000, '億': 100000000
}

# 数字模式（用于正则表达式中的数字匹配，包括中文和阿拉伯数字）
NUMBER_PATTERN = r'([0-9]+(\.[0-9]+)?|[一二三四五六七八九十百千万亿零兩两壹贰叁肆伍陆柒捌玖拾佰仟萬億]+)'

# 时间锚点
YEAR_ANCHORS = r"今年|去年|前年|明年|后年|[0-9]{4}年|本年|年初|年中|年末|年底|上半年|下半年"
HALF_YEAR_ANCHORS = r"上半年|下半年"  # 专门的半年锚点，便于优先匹配
MONTH_ANCHORS = r"[一二三四五六七八九十]{1,2}月份|[1-9]月|1[0-2]月|本月|上个月|上個月|上月|上一个月|这个月|这一个月|月初|月中|月底|月末"
QUARTER_ANCHORS = r"第[一二三四]季度|[一二三四]季度|本季度|上个季度|上個季度|本财年|本財年|今年财年|今年財年|上季度|这个季度|这一季度|季初|季中|季末|季度末"
WEEK_ANCHORS = r"本周|上周|上週|上个星期|上個星期|星期[一二三四五六日天]|周[一二三四五六日天]|禮拜[一二三四五六日天]"
DAY_ANCHORS = r"今天|昨天|前天|明天|后天|大前天|大后天|[1-9]号|[1-2][0-9]号|3[0-1]号|[1-9]日|[1-2][0-9]日|3[0-1]日"
FISCAL_YEAR_ANCHORS = r"本财年|本財年|今年财年|今年財年"
SPECIAL_TIME_POINTS = r"迄今为止|截止目前|至今|前几天|前幾天"

# 插槽类型
RELATIVE_TIME_RANGE = "relative_time_range"
DURATION_TIME_RANGE = "duration_time_range"

# 辅助函数
def chinese_to_num(chinese_str: str) -> float:
    """将中文数字转换为阿拉伯数字"""
    if not chinese_str or chinese_str == "":
        return 0
    
    # 如果直接是数字，直接返回
    if re.match(r'^\d+(\.\d+)?$', chinese_str):
        return float(chinese_str)
    
    # 处理中文数字
    total = 0
    temp = 0
    
    # 替换一些特殊写法
    chinese_str = chinese_str.replace('零', '')
    
    for char in chinese_str:
        if char in CHINESE_DIGITS:
            value = CHINESE_DIGITS[char]
            if value >= 10:
                if temp == 0:
                    temp = 1
                temp *= value
                total += temp
                temp = 0
            else:
                temp = temp * 10 + value
        else:
            # 非数字字符跳过
            continue
    
    total += temp
    return float(total) if total > 0 else 1.0  # 默认为1，处理"几个月"这种表达

def standardize_time_unit(unit: str) -> str:
    """标准化时间单位"""
    return SIMPLIFIED_TIME_UNITS.get(unit, unit)

def convert_to_days(value: float, unit: str) -> float:
    """将不同时间单位转换为天数"""
    # 先对单位进行标准化处理
    standard_unit = standardize_time_unit(unit)
    # 再查找标准单位对应的天数
    days_ratio = TIME_UNIT_TO_DAYS.get(standard_unit, 1)  # 默认为1天
    return value * days_ratio

def parse_number_with_unit(text: str) -> Tuple[float, str]:
    """从文本中解析数字和单位"""
    # 匹配数字（阿拉伯数字或中文数字）
    number_match = re.search(NUMBER_PATTERN, text)
    unit_pattern = f"({TIME_UNITS})"
    unit_match = re.search(unit_pattern, text)
    
    number = 1.0  # 默认数值
    unit = "天"   # 默认单位
    
    if number_match:
        number_text = number_match.group(1)
        number = chinese_to_num(number_text)
    
    if unit_match:
        unit = unit_match.group(1)
    
    return number, unit

class TimeExtractor:
    """时间抽取器类"""
    
    def __init__(self):
        """初始化时间提取器"""
        # 基础正则模式
        self._init_regex_patterns()
        # 尝试修复"今年以来"的问题
        self._fix_special_time_patterns()
        
    def _fix_special_time_patterns(self):
        """修复特殊时间模式的问题，特别是"今年以来"的计算"""
        # 修复"今年以来"的计算函数
        for i, (pattern, _) in enumerate(self.special_time_patterns):
            if str(pattern.pattern).startswith(r"(今年|本年)"):
                # 替换为正确的计算函数
                self.special_time_patterns[i] = (pattern, self._calculate_this_year_range)
                print(f"DEBUG - 已修复今年以来的匹配模式")
    
    def _init_regex_patterns(self):
        """初始化正则表达式模式"""
        # 常用模式组合
        time_unit_pattern = f"({TIME_UNITS})"
        number_with_unit = rf"({NUMBER_PATTERN})({time_unit_pattern})"
        
        # 简单持续时间模式 - 用于_extract_simple_duration方法
        self.duration_pattern = re.compile(
            rf"({NUMBER_PATTERN})({TIME_UNITS})"
        )
        
        # 初始化持续时间范围正则模式
        self.prefix_modifier_pattern = re.compile(
            rf"(({UPPER_LIMIT_WORDS}|{LOWER_LIMIT_WORDS}|{APPROXIMATE_WORDS})({NUMBER_PATTERN})({TIME_UNITS})({UPPER_LIMIT_WORDS}|{LOWER_LIMIT_WORDS}|{APPROXIMATE_WORDS})?)"
        )
        
        self.suffix_modifier_pattern = re.compile(
            rf"(({NUMBER_PATTERN})({TIME_UNITS})({UPPER_LIMIT_WORDS}|{LOWER_LIMIT_WORDS}|{APPROXIMATE_WORDS}))"
        )
        
        # 带修饰词的持续时间，优化修饰词位置的灵活性
        upper_limit = rf"({UPPER_LIMIT_WORDS})"
        lower_limit = rf"({LOWER_LIMIT_WORDS})"
        approximate = rf"({APPROXIMATE_WORDS})"
        
        # 1. 修饰词在前: "至少X"、"不超过X"、"大约X"
        self.prefix_modifier_pattern = re.compile(
            rf"({upper_limit}|{lower_limit}|{approximate})({NUMBER_PATTERN})({TIME_UNITS})"
        )
        
        # 2. 修饰词在后: "X以上"、"X左右"
        self.suffix_modifier_pattern = re.compile(
            rf"({NUMBER_PATTERN})({TIME_UNITS})({upper_limit}|{lower_limit}|{approximate})"
        )
        
        self.compound_duration_pattern = re.compile(
            rf"(({NUMBER_PATTERN})({TIME_UNITS})(零|又|加|和|及)({NUMBER_PATTERN})({TIME_UNITS}))"
        )
        
        self.duration_range_pattern = re.compile(
            rf"(({NUMBER_PATTERN})({TIME_UNITS})?({TIME_INFIX})({NUMBER_PATTERN})({TIME_UNITS}))"
        )
        
        self.duration_range_with_infix_pattern = re.compile(
            rf"(在?({NUMBER_PATTERN})({TIME_UNITS})?({TIME_INFIX})({NUMBER_PATTERN})({TIME_UNITS})之?(间|内|中|以[内中]))"
        )
        
        self.simple_duration_pattern = re.compile(
            rf"(({NUMBER_PATTERN})({TIME_UNITS}))"
        )
        
        # 3. 持有时间/期限表达式: "持有时间不超过X"、"投资期限为X"
        self.holding_period_pattern = re.compile(
            rf"(持有|投资|投資|存续|存續|期限|周期)(时间|時間|期|为|為)?({upper_limit}|{lower_limit})?({NUMBER_PATTERN})({TIME_UNITS})({upper_limit}|{lower_limit}|{approximate})?"
        )
        
        # 4. 复合持续时间: "一年零三个月"
        self.compound_duration_pattern = re.compile(
            rf"({NUMBER_PATTERN})({TIME_UNITS})[零又和与與及]({NUMBER_PATTERN})({TIME_UNITS})(的)?(合同|合約|期限|期间|期間|时间|時間)?"
        )
        
        # 5. 简单期限表达式：如"期限5年"、"周期3个月"
        self.simple_period_pattern = re.compile(
            rf"(期限|周期|期间|期間)(为|為)?({NUMBER_PATTERN})({TIME_UNITS})"
        )
        
        # 6. 范围时间表达式：如"三到五年期"
        self.range_period_pattern = re.compile(
            rf"({NUMBER_PATTERN})({TIME_UNITS})({TIME_INFIX})({NUMBER_PATTERN})({TIME_UNITS})期"
        )
        
        # 7. 范围+近似词组合表达式：如"一到两年左右"
        # 使用具名组使结果更易于访问
        self.range_with_approximate_pattern = re.compile(
            rf"(?P<first_num>{NUMBER_PATTERN})({TIME_INFIX})(?P<second_num>{NUMBER_PATTERN})(?P<unit>{TIME_UNITS})(?P<approximate>{APPROXIMATE_WORDS})"
        )
        
        # 8. 简写范围+近似词组合表达式：如"一两年左右"（没有"到"字）
        self.short_range_with_approximate_pattern = re.compile(
            rf"(?P<first_num>[一二两三四五六七八九十])(?P<second_num>[一二两三四五六七八九十])(?P<unit>{TIME_UNITS})(?P<approximate>{APPROXIMATE_WORDS})"
        )
        
        # 带修饰词的持续时间
        self.duration_with_modifier_pattern = re.compile(
            rf"({upper_limit}|{lower_limit})?({NUMBER_PATTERN})({TIME_UNITS})({approximate})?"
        )
        
        # 初始化相对时间范围正则模式
        self.past_relative_pattern = self._build_past_relative_pattern()
        self.future_relative_pattern = self._build_future_relative_pattern()
        self.past_to_future_pattern = self._build_past_to_future_pattern()
        
        # 时间锚点模式
        self.year_anchor_pattern = re.compile(YEAR_ANCHORS)
        self.half_year_pattern = re.compile(HALF_YEAR_ANCHORS)  # 专门的半年模式
        self.month_anchor_pattern = re.compile(MONTH_ANCHORS)
        self.quarter_anchor_pattern = re.compile(QUARTER_ANCHORS)
        self.week_anchor_pattern = re.compile(WEEK_ANCHORS)
        self.day_anchor_pattern = re.compile(DAY_ANCHORS)
        self.fiscal_year_pattern = re.compile(FISCAL_YEAR_ANCHORS)
        self.special_time_pattern = re.compile(SPECIAL_TIME_POINTS)
        
        # 半年期模式 - 匹配"半年"
        self.half_year_duration_pattern = re.compile(r"半年")
        
        # 特殊的"上"字模式 - 仅匹配后面跟特定时间单位的"上"
        # 如"上个月"、"上周"、"上季度"等，避免匹配"上市"、"向上"等非时间表达
        self.special_up_pattern = re.compile(
            rf"{SPECIAL_PAST_PREFIX}(个|星期|周|礼拜|週|禮拜|季度|季|年|月|日|天|次)"
        )
        
        # 复合时间锚点模式 - 年份锚点+月份
        month_number = r"([0-9]{1,2}|[一二三四五六七八九十]{1,2})"
        self.year_month_pattern = re.compile(
            rf"({YEAR_ANCHORS})({month_number})(月份?|月)"
        )
        
        # 复合时间锚点模式 - 年份锚点+季度
        quarter_number = r"([1-4第一二三四])"
        self.year_quarter_pattern = re.compile(
            rf"({YEAR_ANCHORS})({quarter_number})(季度?|季)"
        )
        
        # 复合时间锚点模式 - 月份锚点+日期
        day_number = r"([0-9]{1,2}|[一二三四五六七八九十]{1,2})"
        self.month_day_pattern = re.compile(
            rf"({MONTH_ANCHORS})({day_number})(日|号)"
        )
        
        # 特殊时间表达式模式 - 确保这里使用的是函数引用而不是函数调用结果
        self.special_time_patterns = [
            (re.compile(r"(今年|本年)(以来|至今|为止|以前|之前)"), self._calculate_this_year_range),
            (re.compile(r"(本月|这个月|当月)(以来|至今|为止|以前|之前)"), self._calculate_this_month_range),
            (re.compile(r"(本季度|这个季度|当季)(以来|至今|为止|以前|之前)"), self._calculate_this_quarter_range),
            (re.compile(r"(本周|这周|这个星期|本星期|当周)(以来|至今|为止|以前|之前)"), self._calculate_this_week_range),
            (re.compile(r"(至今|迄今为止|截至目前|截止目前)"), lambda: (-float('inf'), 0)),  # 从无限久远到现在
        ]
        
        # 打印标记，指示今年以来模式已被修复
        print("DEBUG - 已修复今年以来的匹配模式")
    
    def _build_past_relative_pattern(self) -> Pattern:
        """构建过去相对时间范围的正则表达式"""
        pattern = rf"(({PAST_PREFIX})(的)?({NUMBER_PATTERN})?({TIME_UNITS})?(({TIME_INFIX}){NUMBER_PATTERN})?({TIME_UNITS})?({PAST_SUFFIX})?)"
        return re.compile(pattern)
    
    def _build_future_relative_pattern(self) -> Pattern:
        """构建未来相对时间范围的正则表达式"""
        # 修改未来相对时间模式，避免匹配单独的模糊词
        # 要求后面必须跟具体的时间单位或数字
        pattern = rf"(({FUTURE_PREFIX})(({NUMBER_PATTERN})({TIME_UNITS})?|({TIME_UNITS}))({FUTURE_SUFFIX})?)"
        return re.compile(pattern)
    
    def _build_past_to_future_pattern(self) -> Pattern:
        """构建从过去到未来的相对时间范围正则表达式"""
        pattern = rf"(({PAST_PREFIX})({NUMBER_PATTERN})?({TIME_UNITS})?({TIME_INFIX})({FUTURE_PREFIX})?({NUMBER_PATTERN})?({TIME_UNITS})?)"
        return re.compile(pattern)
    
    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取时间信息
        
        Args:
            text: 输入文本
            
        Returns:
            List[Dict[str, Any]]: 提取的时间信息列表
        """
        results = []
        
        # 0. 先提取半年特殊表达式（优先级最高）
        half_year_results = self._extract_half_year_expressions(text)
        results.extend(half_year_results)
        
        # 1. 提取相对时间范围
        relative_times = self._extract_relative_time_range(text)
        
        # 修复今年以来的计算
        now = datetime.datetime.now()
        start_of_year = datetime.datetime(now.year, 1, 1)
        days_since_start_of_year = (now - start_of_year).days
        
        # 检查并修正"今年以来"结果
        for result in relative_times:
            if result.get("match") == "今年以来":
                print(f"DEBUG - 检测到今年以来: 原值={result['minValue']}, 应为={-days_since_start_of_year}")
                result["minValue"] = -days_since_start_of_year
                result["maxValue"] = 0
                print(f"DEBUG - extract_time: 修正后 今年以来 minValue={result['minValue']}, maxValue={result['maxValue']}")
        
        results.extend(relative_times)
        
        # 2. 提取持续时间范围
        duration_times = self._extract_duration_time_range(text)
        results.extend(duration_times)
        
        # 3. 根据处理原则进行过滤和排序
        filtered_results = self._filter_and_sort_results(results)
        
        # 最终调试：排序后的结果
        for result in filtered_results:
            if result.get("match") == "今年以来":
                print(f"DEBUG - 最终结果: 今年以来 minValue={result['minValue']}, maxValue={result['maxValue']}")
        
        return filtered_results
    
    def _create_time_result(self, match_text: str, position: Tuple[int, int], 
                          slot_value: str, min_value: float, max_value: float) -> Dict[str, Any]:
        """创建统一的时间提取结果格式
        
        Args:
            match_text: 匹配到的原文文本
            position: 在原文中的位置 (start, end)
            slot_value: 时间类型，相对时间或持续时间
            min_value: 范围最小值（天数）
            max_value: 范围最大值（天数）
            
        Returns:
            Dict[str, Any]: 标准格式的结果字典
        """
        return {
            "match": match_text,
            "position": position,
            "slot": "time",
            "slot_value": slot_value,
            "minValue": min_value,
            "maxValue": max_value
        }
    
    def _extract_relative_time_range(self, text: str) -> List[Dict[str, Any]]:
        """提取相对时间范围"""
        results = []
        
        # 0. 先提取半年特殊表达式（优先级最高）
        half_year_results = self._extract_half_year_expressions(text)
        results.extend(half_year_results)
        
        # 1. 提取过去相对时间范围
        past_results = self._extract_time_with_pattern(
            text, self.past_relative_pattern, RELATIVE_TIME_RANGE, self._calculate_past_relative_days
        )
        results.extend(past_results)
        
        # 2. 提取未来相对时间范围
        future_results = self._extract_time_with_pattern(
            text, self.future_relative_pattern, RELATIVE_TIME_RANGE, self._calculate_future_relative_days
        )
        results.extend(future_results)
        
        # 3. 提取过去到未来的相对时间范围
        past_to_future_results = self._extract_past_to_future_time(text)
        results.extend(past_to_future_results)
        
        # 4. 提取特殊相对时间表达式（如"今年以来"等）
        special_results = self._extract_special_relative_time(text)
        results.extend(special_results)
        
        # 5. 提取复合时间锚点表达式（如"今年2月"）
        compound_anchor_results = self._extract_compound_time_anchors(text)
        results.extend(compound_anchor_results)
        
        # 6. 处理特殊的"上"字（最低优先级，且要确保不与其他表达重叠）
        special_up_results = self._extract_special_up_expressions(text, results)
        results.extend(special_up_results)
        
        return results

    def _extract_time_with_pattern(self, text: str, pattern: Pattern, slot_value: str, 
                                   calculation_func) -> List[Dict[str, Any]]:
        """通用的时间提取函数，根据给定模式提取时间信息"""
        results = []
        
        for match in pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 解析匹配文本中的数字和单位
            numbers, units = self._parse_time_expression(match_text)
            
            # 计算相对天数值
            min_value, max_value = calculation_func(numbers, units, match_text)
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=slot_value,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
            
        return results
        
    def _extract_past_to_future_time(self, text: str) -> List[Dict[str, Any]]:
        """提取从过去到未来的相对时间范围"""
        results = []
        
        for match in self.past_to_future_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 尝试分割成过去部分和未来部分
            parts = re.split(rf"({TIME_INFIX})", match_text)
            if len(parts) >= 3:
                past_part = parts[0]
                future_part = "".join(parts[2:])
                
                # 解析过去部分
                past_numbers, past_units = self._parse_time_expression(past_part)
                # 解析未来部分
                future_numbers, future_units = self._parse_time_expression(future_part)
                
                # 计算相对天数值
                min_value = self._calculate_past_days(past_numbers[0] if past_numbers else 0, 
                                                    past_units[0] if past_units else "天")
                max_value = self._calculate_future_days(future_numbers[0] if future_numbers else 0, 
                                                      future_units[0] if future_units else "天")
                
                # 使用通用方法创建结果
                result = self._create_time_result(
                    match_text=match_text,
                    position=(start_pos, end_pos),
                    slot_value=RELATIVE_TIME_RANGE,
                    min_value=min_value,
                    max_value=max_value
                )
                
                results.append(result)
            
        return results

    def _extract_special_relative_time(self, text: str) -> List[Dict[str, Any]]:
        """提取特殊相对时间表达式"""
        results = []
        
        # 使用预定义的特殊时间模式和对应的计算函数
        for pattern, calculation_func in self.special_time_patterns:
            for match in pattern.finditer(text):
                match_text = match.group(0)
                start_pos, end_pos = match.span()
                
                # 计算相对天数值
                min_value, max_value = calculation_func()
                
                # 调试输出
                if "今年以来" in match_text:
                    print(f"DEBUG - 今年以来: 实际天数={min_value}, 返回值={min_value}")
                
                # 使用通用方法创建结果
                result = self._create_time_result(
                    match_text=match_text,
                    position=(start_pos, end_pos),
                    slot_value=RELATIVE_TIME_RANGE,
                    min_value=min_value,
                    max_value=max_value
                )
                
                results.append(result)
                
        return results
        
    def _parse_time_expression(self, text: str) -> Tuple[List[float], List[str]]:
        """解析时间表达式中的数字和单位"""
        # 匹配所有数字
        numbers = []
        for match in re.finditer(NUMBER_PATTERN, text):
            number_text = match.group(1)
            numbers.append(chinese_to_num(number_text))
        
        # 匹配所有单位
        unit_pattern = f"({TIME_UNITS})"
        units = []
        for match in re.finditer(unit_pattern, text):
            units.append(match.group(1))
        
        # 如果没有找到数字，默认为1
        if not numbers:
            numbers = [1.0]
        
        # 如果没有找到单位，默认为"天"
        if not units:
            units = ["天"]
            
        return numbers, units
        
    def _calculate_past_relative_days(self, numbers: List[float], units: List[str], text: str) -> Tuple[float, float]:
        """计算过去相对时间的天数范围"""
        # 如果有多个数字，可能是范围表达（如"过去三到五天"）
        if len(numbers) >= 2 and len(units) >= 1:
            max_days = self._calculate_past_days(numbers[0], units[0])
            min_days = self._calculate_past_days(numbers[1], units[-1] if len(units) > 1 else units[0])
            # 确保最小值小于最大值
            if min_days > max_days:
                min_days, max_days = max_days, min_days
            return min_days, 0  # 过去时间的最大值通常是当前（0）
        
        # 单个时间点（如"过去三天"）
        if len(numbers) >= 1 and len(units) >= 1:
            days = self._calculate_past_days(numbers[0], units[0])
            return days, 0  # 过去时间的最大值通常是当前（0）
        
        # 默认情况，可能是模糊表达（如"过去一段时间"）
        return -30, 0  # 默认为过去30天到现在
        
    def _calculate_future_relative_days(self, numbers: List[float], units: List[str], text: str) -> Tuple[float, float]:
        """计算未来相对时间的天数范围"""
        # 如果有多个数字，可能是范围表达（如"未来三到五天"）
        if len(numbers) >= 2 and len(units) >= 1:
            min_days = 0  # 未来时间的最小值通常是当前（0）
            max_days = self._calculate_future_days(numbers[1] if len(numbers) > 1 else numbers[0], 
                                                units[-1] if len(units) > 1 else units[0])
            return min_days, max_days
        
        # 单个时间点（如"未来三天"）
        if len(numbers) >= 1 and len(units) >= 1:
            days = self._calculate_future_days(numbers[0], units[0])
            return 0, days  # 未来时间的最小值通常是当前（0）
        
        # 默认情况，可能是模糊表达（如"未来一段时间"）
        return 0, 30  # 默认为现在到未来30天
        
    def _calculate_past_days(self, number: float, unit: str) -> float:
        """计算过去的相对天数（返回负值）"""
        days = convert_to_days(number, unit)
        return -abs(days)  # 确保是负值
    
    def _calculate_future_days(self, number: float, unit: str) -> float:
        """计算未来的相对天数（返回正值）"""
        days = convert_to_days(number, unit)
        return abs(days)  # 确保是正值
    
    def _calculate_this_year_range(self) -> Tuple[float, float]:
        """计算今年以来的天数范围"""
        now = datetime.datetime.now()
        start_of_year = datetime.datetime(now.year, 1, 1)
        days_since_start_of_year = (now - start_of_year).days
        
        # 记录计算结果
        print(f"DEBUG - _calculate_this_year_range: 从年初到现在={days_since_start_of_year}天")
        
        # 确保返回实际的天数，而不是固定值
        return -days_since_start_of_year, 0
    
    def _calculate_this_month_range(self) -> Tuple[float, float]:
        """计算本月以来的天数范围"""
        now = datetime.datetime.now()
        start_of_month = datetime.datetime(now.year, now.month, 1)
        days_since_start_of_month = (now - start_of_month).days
        return -days_since_start_of_month, 0
    
    def _calculate_this_quarter_range(self) -> Tuple[float, float]:
        """计算本季度以来的天数范围"""
        now = datetime.datetime.now()
        current_quarter = (now.month - 1) // 3 + 1
        start_month = (current_quarter - 1) * 3 + 1
        start_of_quarter = datetime.datetime(now.year, start_month, 1)
        days_since_start_of_quarter = (now - start_of_quarter).days
        return -days_since_start_of_quarter, 0
    
    def _calculate_this_week_range(self) -> Tuple[float, float]:
        """计算本周以来的天数范围"""
        now = datetime.datetime.now()
        start_of_week = now - datetime.timedelta(days=now.weekday())
        days_since_start_of_week = (now - start_of_week).days
        return -days_since_start_of_week, 0
    
    def _extract_duration_time_range(self, text: str) -> List[Dict[str, Any]]:
        """提取持续时间范围"""
        results = []
        
        # 1. 提取期限表达式（"持有时间不超过X"等）
        holding_period_results = self._extract_holding_period(text)
        results.extend(holding_period_results)
        
        # 2. 提取简单期限表达式（"期限X"等）
        simple_period_results = self._extract_simple_period(text)
        results.extend(simple_period_results)
        
        # 3. 提取"X到Y期"类表达式
        range_period_results = self._extract_range_period(text)
        results.extend(range_period_results)
        
        # 4. 提取带范围限定词的时间表达式（"在X到Y之间"等）
        range_with_infix_results = self._extract_duration_range_with_infix(text)
        results.extend(range_with_infix_results)
        
        # 5. 提取范围+近似词组合表达式（"一到两年左右"等）
        range_with_approximate_results = self._extract_range_with_approximate(text)
        results.extend(range_with_approximate_results)
        
        # 5.1 提取简写范围+近似词组合表达式（"一两年左右"等）
        short_range_with_approximate_results = self._extract_short_range_with_approximate(text)
        results.extend(short_range_with_approximate_results)
        
        # 6. 提取前置修饰词的持续时间（"至少X"等）
        prefix_modifier_results = self._extract_prefix_modifier_duration(text)
        results.extend(prefix_modifier_results)
        
        # 7. 提取后置修饰词的持续时间（"X以上"等）
        suffix_modifier_results = self._extract_suffix_modifier_duration(text)
        results.extend(suffix_modifier_results)
        
        # 8. 提取复合持续时间（"一年零三个月"等）
        compound_duration_results = self._extract_compound_duration(text)
        results.extend(compound_duration_results)
        
        # 9. 提取时间范围表达式（"X到Y"等）
        range_results = self._extract_duration_range(text)
        results.extend(range_results)
        
        # 10. 提取具有修饰词的持续时间（原有逻辑，作为补充）
        modifier_results = self._extract_duration_with_modifier(text)
        results.extend(modifier_results)
        
        # 11. 提取简单持续时间
        simple_results = self._extract_simple_duration(text)
        results.extend(simple_results)
        
        return results
    
    def _extract_holding_period(self, text: str) -> List[Dict[str, Any]]:
        """提取期限表达式，如"持有时间不超过90天"、"投资期限至少一年"等"""
        results = []
        
        for match in self.holding_period_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            # 提取上下限修饰词
            has_upper_limit = re.search(rf"({UPPER_LIMIT_WORDS})", match_text) is not None
            has_lower_limit = re.search(rf"({LOWER_LIMIT_WORDS})", match_text) is not None
            has_approximate = re.search(rf"({APPROXIMATE_WORDS})", match_text) is not None
            
            # 解析数字和单位
            numbers, units = self._parse_time_expression(match_text)
            
            if not numbers or not units:
                continue
            
            # 计算基本天数
            base_days = convert_to_days(numbers[0], units[0])
            
            # 根据修饰词确定最小值和最大值
            min_value, max_value = self._apply_modifiers_to_duration(
                base_days, has_upper_limit, has_lower_limit, has_approximate
            )
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _extract_simple_period(self, text: str) -> List[Dict[str, Any]]:
        """提取简单期限表达式，如"期限5年"、"周期3个月"等"""
        results = []
        
        for match in self.simple_period_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            # 解析数字和单位
            numbers, units = self._parse_time_expression(match_text)
            
            if not numbers or not units:
                continue
            
            # 计算基本天数
            base_days = convert_to_days(numbers[0], units[0])
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=base_days,
                max_value=base_days
            )
            
            results.append(result)
        
        return results
    
    def _extract_range_period(self, text: str) -> List[Dict[str, Any]]:
        """提取范围时间期限表达式，如"三到五年期"等"""
        results = []
        
        for match in self.range_period_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            # 尝试分割成两部分
            parts = re.split(rf"({TIME_INFIX})", match_text)
            if len(parts) < 3:
                continue
                
            first_part = parts[0]
            second_part = "".join(parts[2:]).replace("期", "")  # 去掉"期"字
            
            # 从第一部分提取数字和单位
            first_numbers, first_units = self._parse_time_expression(first_part)
            if not first_numbers or not first_units:
                continue
                
            # 从第二部分提取数字和单位
            second_numbers, second_units = self._parse_time_expression(second_part)
            if not second_numbers:  # 如果没有提取到数字，可能是"三到五年期"中的"五年期"
                # 尝试提取单位前面的数字
                number_match = re.search(NUMBER_PATTERN, second_part)
                if number_match:
                    second_numbers = [chinese_to_num(number_match.group(1))]
                    second_units = first_units  # 假设单位与第一部分相同
            
            if not second_numbers or not second_units:
                continue
            
            # 计算天数
            first_days = convert_to_days(first_numbers[0], first_units[0])
            second_days = convert_to_days(second_numbers[0], second_units[0])
            
            # 确保最小值小于最大值
            min_value = min(first_days, second_days)
            max_value = max(first_days, second_days)
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _extract_duration_range_with_infix(self, text: str) -> List[Dict[str, Any]]:
        """提取带范围限定词的时间表达式，如"期限在一年到三年之间"等"""
        results = []
        
        for match in self.duration_range_with_infix_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            # 尝试分割成两部分
            parts = re.split(rf"({TIME_INFIX})", match_text)
            if len(parts) < 3:
                continue
                
            first_part = parts[0]
            second_part = "".join(parts[2:])
            
            # 从第一部分提取数字和单位
            first_numbers, first_units = self._parse_time_expression(first_part)
            if not first_numbers or not first_units:
                continue
                
            # 从第二部分提取数字和单位
            second_numbers, second_units = self._parse_time_expression(second_part)
            if not second_numbers or not second_units:
                continue
            
            # 计算天数
            first_days = convert_to_days(first_numbers[0], first_units[0])
            second_days = convert_to_days(second_numbers[0], second_units[0])
            
            # 确保最小值小于最大值
            min_value = min(first_days, second_days)
            max_value = max(first_days, second_days)
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _extract_prefix_modifier_duration(self, text: str) -> List[Dict[str, Any]]:
        """提取前置修饰词的持续时间，如"至少一年"、"不超过90天"等"""
        results = []
        
        for match in self.prefix_modifier_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            # 提取修饰词类型
            has_upper_limit = re.search(rf"({UPPER_LIMIT_WORDS})", match_text) is not None
            has_lower_limit = re.search(rf"({LOWER_LIMIT_WORDS})", match_text) is not None
            has_approximate = re.search(rf"({APPROXIMATE_WORDS})", match_text) is not None
            
            # 解析数字和单位
            numbers, units = self._parse_time_expression(match_text)
            
            if not numbers or not units:
                continue
            
            # 计算基本天数
            base_days = convert_to_days(numbers[0], units[0])
            
            # 应用修饰词
            min_value, max_value = self._apply_modifiers_to_duration(
                base_days, has_upper_limit, has_lower_limit, has_approximate
            )
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _extract_suffix_modifier_duration(self, text: str) -> List[Dict[str, Any]]:
        """提取后置修饰词的持续时间，如"一年以上"、"两年左右"等"""
        results = []
        
        for match in self.suffix_modifier_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            # 提取修饰词类型
            has_upper_limit = re.search(rf"({UPPER_LIMIT_WORDS})", match_text) is not None
            has_lower_limit = re.search(rf"({LOWER_LIMIT_WORDS})", match_text) is not None
            has_approximate = re.search(rf"({APPROXIMATE_WORDS})", match_text) is not None
            
            # 解析数字和单位
            numbers, units = self._parse_time_expression(match_text)
            
            if not numbers or not units:
                continue
            
            # 计算基本天数
            base_days = convert_to_days(numbers[0], units[0])
            
            # 应用修饰词
            min_value, max_value = self._apply_modifiers_to_duration(
                base_days, has_upper_limit, has_lower_limit, has_approximate
            )
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _extract_compound_duration(self, text: str) -> List[Dict[str, Any]]:
        """提取复合持续时间，如"一年零三个月"等"""
        results = []
        
        for match in self.compound_duration_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            # 查找所有的数字+单位组合
            pattern = rf"({NUMBER_PATTERN})({TIME_UNITS})"
            
            total_days = 0
            for unit_match in re.finditer(pattern, match_text):
                part = unit_match.group(0)
                number, unit = self._parse_number_with_unit(part)
                days = convert_to_days(number, unit)
                total_days += days
            
            if total_days <= 0:
                continue
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=total_days,
                max_value=total_days
            )
            
            results.append(result)
        
        return results
    
    def _apply_modifiers_to_duration(self, base_days: float, has_upper_limit: bool, 
                                   has_lower_limit: bool, has_approximate: bool) -> Tuple[float, float]:
        """应用修饰词到持续时间值，返回最小值和最大值"""
        if has_upper_limit:
            # 不超过X -> [0, X]
            min_value = 0
            max_value = base_days
            if has_approximate:
                # 考虑近似词的影响，例如"不超过约5天" -> [0, 6]
                max_value = base_days * 1.2
        elif has_lower_limit:
            # 至少X -> [X, inf]
            # 例如"至少半年以上": 基础值为半年(183天)，但包含"至少"和"以上"两个下限词，认为是[183, inf]
            min_value = base_days
            max_value = float('inf')
            if has_approximate:
                # 考虑近似词的影响，例如"至少约5天" -> [4, inf]
                min_value = base_days * 0.8
        elif has_approximate:
            # 只有近似词，如"约5天" -> [4, 6]
            min_value = base_days * 0.8
            max_value = base_days * 1.2
        else:
            # 没有修饰词，按简单持续时间处理
            min_value = base_days
            max_value = base_days
        
        return min_value, max_value
    
    def _extract_duration_with_modifier(self, text: str) -> List[Dict[str, Any]]:
        """提取带有修饰词的持续时间"""
        results = []
        
        for match in self.duration_with_modifier_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 解析匹配文本中的数字和单位，以及修饰词
            min_value, max_value = self._parse_duration_with_modifier(match_text)
            
            if min_value is None and max_value is None:
                continue
                
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
            
        return results
    
    def _parse_duration_with_modifier(self, text: str) -> Tuple[Optional[float], Optional[float]]:
        """解析带有修饰词的持续时间表达式，返回最小值和最大值"""
        # 检查是否包含上限、下限或近似修饰词
        has_upper_limit = re.search(rf"({UPPER_LIMIT_WORDS})", text) is not None
        has_lower_limit = re.search(rf"({LOWER_LIMIT_WORDS})", text) is not None
        has_approximate = re.search(rf"({APPROXIMATE_WORDS})", text) is not None
        
        # 解析匹配文本中的数字和单位
        numbers, units = self._parse_time_expression(text)
        
        if not numbers or not units:
            return None, None
            
        # 计算基本天数
        base_days = convert_to_days(numbers[0], units[0])
        
        # 根据修饰词确定最小值和最大值
        if has_upper_limit:
            # 不超过X -> [0, X]
            min_value = 0
            max_value = base_days
            if has_approximate:
                # 考虑近似词的影响，例如"不超过约5天" -> [0, 6]
                max_value = base_days * 1.2
        elif has_lower_limit:
            # 至少X -> [X, inf]
            min_value = base_days
            max_value = float('inf')
            if has_approximate:
                # 考虑近似词的影响，例如"至少约5天" -> [4, inf]
                min_value = base_days * 0.8
        elif has_approximate:
            # 只有近似词，如"约5天" -> [4, 6]
            min_value = base_days * 0.8
            max_value = base_days * 1.2
        else:
            # 没有修饰词，按简单持续时间处理
            min_value = base_days
            max_value = base_days
        
        return min_value, max_value
    
    def _extract_duration_range(self, text: str) -> List[Dict[str, Any]]:
        """提取时间范围表达式"""
        results = []
        
        for match in self.duration_range_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 解析时间范围表达式
            min_value, max_value = self._parse_duration_range(match_text)
            
            if min_value is None or max_value is None:
                continue
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
            
        return results
    
    def _parse_duration_range(self, text: str) -> Tuple[Optional[float], Optional[float]]:
        """解析时间范围表达式，返回最小值和最大值"""
        # 尝试分割成两部分
        parts = re.split(rf"({TIME_INFIX})", text)
        if len(parts) < 3:
            return None, None
            
        first_part = parts[0]
        second_part = "".join(parts[2:])
        
        # 解析第一部分
        first_numbers, first_units = self._parse_time_expression(first_part)
        # 解析第二部分
        second_numbers, second_units = self._parse_time_expression(second_part)
        
        if not first_numbers or not first_units or not second_numbers or not second_units:
            return None, None
        
        # 计算天数
        first_days = convert_to_days(first_numbers[0], first_units[0])
        second_days = convert_to_days(second_numbers[0], second_units[0])
        
        # 确保最小值小于最大值
        min_value = min(first_days, second_days)
        max_value = max(first_days, second_days)
        
        return min_value, max_value
    
    def _extract_simple_duration(self, text: str) -> List[Dict[str, Any]]:
        """提取简单持续时间"""
        results = []
        
        for match in self.duration_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查这个匹配是否已经被其他更复杂的模式匹配过
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            # 解析复合时间表达式（如"一年零三个月"）
            total_days = self._parse_compound_duration(match_text)
            
            if total_days <= 0:
                continue
                
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=total_days,
                max_value=total_days
            )
            
            results.append(result)
            
        return results
    
    def _parse_compound_duration(self, text: str) -> float:
        """解析复合时间表达式，返回总天数"""
        # 查找所有的数字+单位组合
        pattern = rf"({NUMBER_PATTERN})({TIME_UNITS})"
        
        total_days = 0
        for match in re.finditer(pattern, text):
            part = match.group(0)
            number, unit = self._parse_number_with_unit(part)
            days = convert_to_days(number, unit)
            total_days += days
            
        return total_days
    
    def _parse_number_with_unit(self, text: str) -> Tuple[float, str]:
        """从文本中解析单个数字和单位组合"""
        # 匹配数字
        number_match = re.search(NUMBER_PATTERN, text)
        
        # 匹配单位
        unit_pattern = f"({TIME_UNITS})"
        unit_match = re.search(unit_pattern, text)
        
        if number_match and unit_match:
            number_text = number_match.group(1)
            unit = unit_match.group(1)
            number = chinese_to_num(number_text)
            return number, unit
        
        return 0, "天"  # 默认值
    
    def _is_covered_by_other_matches(self, match_text: str, start_pos: int, end_pos: int, 
                                    existing_results: List[Dict[str, Any]]) -> bool:
        """检查当前匹配是否已经被其他更复杂的匹配覆盖"""
        for result in existing_results:
            other_start, other_end = result["position"]
            if other_start <= start_pos and other_end >= end_pos and match_text != result["match"]:
                return True
        return False
    
    def _filter_and_sort_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤和排序提取结果"""
        # 去除重叠的结果
        filtered_results = self._remove_overlapping_results(results)
        
        # 对结果按优先级排序
        sorted_results = self._sort_results_by_priority(filtered_results)
        
        # 调试：查看排序前后"今年以来"的变化
        for result in results:
            if result.get("match") == "今年以来":
                print(f"DEBUG - 排序前: 今年以来 minValue={result['minValue']}, maxValue={result['maxValue']}")
        
        for result in sorted_results:
            if result.get("match") == "今年以来":
                print(f"DEBUG - 排序后: 今年以来 minValue={result['minValue']}, maxValue={result['maxValue']}")
        
        return sorted_results
    
    def _remove_overlapping_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """移除重叠区域，保留优先级更高的结果"""
        if not results:
            return []
        
        # 按起始位置排序
        sorted_results = sorted(results, key=lambda x: x["position"][0])
        non_overlapping = [sorted_results[0]]
        
        for result in sorted_results[1:]:
            last = non_overlapping[-1]
            
            # 检查是否重叠
            if result["position"][0] >= last["position"][1]:
                # 不重叠，直接添加
                non_overlapping.append(result)
            else:
                # 重叠，根据优先级选择保留哪一个
                if self._has_higher_priority(result, last):
                    # 新结果优先级更高，替换
                    non_overlapping[-1] = result
        
        return non_overlapping
    
    def _has_higher_priority(self, result1: Dict[str, Any], result2: Dict[str, Any]) -> bool:
        """判断result1是否比result2有更高的优先级"""
        # 1. 相对时间 > 持续时间
        if result1["slot_value"] == RELATIVE_TIME_RANGE and result2["slot_value"] == DURATION_TIME_RANGE:
            return True
        elif result1["slot_value"] == DURATION_TIME_RANGE and result2["slot_value"] == RELATIVE_TIME_RANGE:
            return False
        
        # 2. 更长的匹配文本优先
        return len(result1["match"]) > len(result2["match"])
    
    def _sort_results_by_priority(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据处理原则排序结果"""
        def priority_key(result):
            # 优先级顺序：
            # 1. 相对时间 > 持续时间
            # 2. 更长的匹配文本优先
            slot_value_priority = 0 if result["slot_value"] == RELATIVE_TIME_RANGE else 1
            return (slot_value_priority, -len(result["match"]))
        
        return sorted(results, key=priority_key)

    def _extract_compound_time_anchors(self, text: str) -> List[Dict[str, Any]]:
        """提取复合时间锚点表达式，如'今年2月'、'去年第一季度'等"""
        results = []
        
        # 1. 提取年份+月份组合
        year_month_results = self._extract_year_month_combinations(text)
        results.extend(year_month_results)
        
        # 2. 提取年份+季度组合
        year_quarter_results = self._extract_year_quarter_combinations(text)
        results.extend(year_quarter_results)
        
        # 3. 提取月份+日期组合
        month_day_results = self._extract_month_day_combinations(text)
        results.extend(month_day_results)
        
        return results
    
    def _extract_year_month_combinations(self, text: str) -> List[Dict[str, Any]]:
        """提取年份+月份组合，如'今年2月'"""
        results = []
        
        for match in self.year_month_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 提取年份锚点和月份数值
            year_anchor = match.group(1)
            month_number_text = match.group(2)
            
            # 解析月份数值（可能是中文或阿拉伯数字）
            month_number = int(chinese_to_num(month_number_text))
            if month_number < 1 or month_number > 12:
                continue  # 无效月份
            
            # 计算年份相对天数（相对于当前时间）
            year_offset = self._calculate_year_anchor_offset(year_anchor)
            
            # 计算具体年月对应的日期
            min_value, max_value = self._calculate_year_month_range(year_offset, month_number)
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=RELATIVE_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _extract_year_quarter_combinations(self, text: str) -> List[Dict[str, Any]]:
        """提取年份+季度组合，如'今年第一季度'"""
        results = []
        
        for match in self.year_quarter_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 提取年份锚点和季度数值
            year_anchor = match.group(1)
            quarter_text = match.group(2)
            
            # 解析季度数值
            quarter_number = 0
            if quarter_text in ["1", "一", "第一"]:
                quarter_number = 1
            elif quarter_text in ["2", "二", "第二"]:
                quarter_number = 2
            elif quarter_text in ["3", "三", "第三"]:
                quarter_number = 3
            elif quarter_text in ["4", "四", "第四"]:
                quarter_number = 4
            
            if quarter_number == 0:
                continue  # 无效季度
            
            # 计算年份相对天数
            year_offset = self._calculate_year_anchor_offset(year_anchor)
            
            # 计算具体季度对应的日期范围
            min_value, max_value = self._calculate_year_quarter_range(year_offset, quarter_number)
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=RELATIVE_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _extract_month_day_combinations(self, text: str) -> List[Dict[str, Any]]:
        """提取月份+日期组合，如'本月15日'"""
        results = []
        
        for match in self.month_day_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 提取月份锚点和日期数值
            month_anchor = match.group(1)
            day_number_text = match.group(2)
            
            # 解析日期数值
            day_number = int(chinese_to_num(day_number_text))
            if day_number < 1 or day_number > 31:
                continue  # 无效日期
            
            # 计算月份相对天数
            month_offset, month_number = self._calculate_month_anchor_info(month_anchor)
            
            # 计算具体月日对应的日期范围
            min_value, max_value = self._calculate_month_day_range(month_offset, month_number, day_number)
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=RELATIVE_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _calculate_year_anchor_offset(self, year_anchor: str) -> int:
        """计算年份锚点相对于当前年的偏移量（以年为单位）"""
        now = datetime.datetime.now()
        current_year = now.year
        
        if year_anchor in ["今年", "本年"]:
            return 0  # 当前年
        elif year_anchor == "去年":
            return -1  # 上一年
        elif year_anchor == "前年":
            return -2  # 上上一年
        elif year_anchor == "明年":
            return 1   # 下一年
        elif year_anchor == "后年":
            return 2   # 下下一年
        elif re.match(r"\d{4}年", year_anchor):  # 具体年份，如"2022年"
            specific_year = int(year_anchor[:-1])
            return specific_year - current_year
        
        return 0  # 默认当前年
    
    def _calculate_month_anchor_info(self, month_anchor: str) -> Tuple[int, int]:
        """计算月份锚点的相对偏移量和具体月份
        
        Returns:
            Tuple[int, int]: (月份偏移量, 具体月份值)
        """
        now = datetime.datetime.now()
        current_year = now.year
        current_month = now.month
        
        if month_anchor in ["本月", "这个月", "当月"]:
            return 0, current_month  # 当前月
        elif month_anchor in ["上个月", "上月", "上一个月"]:
            if current_month == 1:  # 如果当前是1月，上个月是去年12月
                return -1, 12
            return 0, current_month - 1
        
        # 解析具体月份，如"3月"
        month_match = re.search(r"([0-9]{1,2}|[一二三四五六七八九十]{1,2})月", month_anchor)
        if month_match:
            month_number = int(chinese_to_num(month_match.group(1)))
            if 1 <= month_number <= 12:
                # 计算与当前月的相对偏移
                month_diff = month_number - current_month
                # 如果是过去的月份，默认是今年的；如果是未来的月份，也默认是今年的
                return 0, month_number
            
        return 0, current_month  # 默认当前月
    
    def _calculate_year_month_range(self, year_offset: int, month_number: int) -> Tuple[float, float]:
        """计算特定年月的时间范围（相对于当前时间）"""
        now = datetime.datetime.now()
        current_year = now.year
        target_year = current_year + year_offset
        
        # 目标月份的第一天
        target_month_start = datetime.datetime(target_year, month_number, 1)
        
        # 目标月份的最后一天
        if month_number == 12:
            next_month_start = datetime.datetime(target_year + 1, 1, 1)
        else:
            next_month_start = datetime.datetime(target_year, month_number + 1, 1)
        
        target_month_end = next_month_start - datetime.timedelta(days=1)
        
        # 计算相对天数
        days_to_start = (target_month_start - now).days
        days_to_end = (target_month_end - now).days
        
        # 确保开始日期小于结束日期
        min_value = min(days_to_start, days_to_end)
        max_value = max(days_to_start, days_to_end)
        
        return min_value, max_value
    
    def _calculate_year_quarter_range(self, year_offset: int, quarter_number: int) -> Tuple[float, float]:
        """计算特定年季度的时间范围（相对于当前时间）"""
        now = datetime.datetime.now()
        current_year = now.year
        target_year = current_year + year_offset
        
        # 计算季度开始月份
        start_month = (quarter_number - 1) * 3 + 1
        
        # 季度开始日期
        quarter_start = datetime.datetime(target_year, start_month, 1)
        
        # 季度结束日期（下一个季度的第一天减一天）
        if quarter_number == 4:  # 第四季度
            next_quarter_start = datetime.datetime(target_year + 1, 1, 1)
        else:
            next_quarter_start = datetime.datetime(target_year, start_month + 3, 1)
        
        quarter_end = next_quarter_start - datetime.timedelta(days=1)
        
        # 计算相对天数
        days_to_start = (quarter_start - now).days
        days_to_end = (quarter_end - now).days
        
        # 确保开始日期小于结束日期
        min_value = min(days_to_start, days_to_end)
        max_value = max(days_to_start, days_to_end)
        
        return min_value, max_value
    
    def _calculate_month_day_range(self, month_offset: int, month_number: int, day_number: int) -> Tuple[float, float]:
        """计算特定月日的时间范围（相对于当前时间）"""
        now = datetime.datetime.now()
        current_year = now.year
        current_month = now.month
        
        # 计算目标年份
        target_year = current_year
        target_month = month_number
        
        # 验证日期有效性
        try:
            # 目标日期（可能需要处理月底溢出）
            last_day = self._get_last_day_of_month(target_year, target_month)
            valid_day = min(day_number, last_day)
            target_date = datetime.datetime(target_year, target_month, valid_day)
            
            # 计算相对天数
            days_diff = (target_date - now).days
            
            # 精确到天的范围
            return days_diff, days_diff
            
        except ValueError:
            # 无效日期，返回默认值
            return 0, 0
    
    def _get_last_day_of_month(self, year: int, month: int) -> int:
        """获取指定年月的最后一天是几号"""
        if month == 12:
            next_month = datetime.datetime(year + 1, 1, 1)
        else:
            next_month = datetime.datetime(year, month + 1, 1)
        
        last_day = (next_month - datetime.timedelta(days=1)).day
        return last_day

    def _extract_half_year_expressions(self, text: str) -> List[Dict[str, Any]]:
        """提取半年特殊表达式，如"上半年"、"下半年"、"半年"等"""
        results = []
        
        # 处理"上半年"和"下半年"
        for match in self.half_year_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            if match_text == "上半年":
                # 上半年：当年1月1日到6月30日
                min_value, max_value = self._calculate_half_year_range(True)
            else:  # "下半年"
                # 下半年：当年7月1日到12月31日
                min_value, max_value = self._calculate_half_year_range(False)
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=RELATIVE_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        # 处理单独的"半年"表达式
        for match in self.half_year_duration_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否是"上半年"或"下半年"的一部分，如果是则跳过
            if self._is_part_of_longer_match(text, start_pos, "上半年") or \
               self._is_part_of_longer_match(text, start_pos, "下半年"):
                continue
                
            # 检查前面是否有"至少"、"以上"等下限修饰词
            context_start = max(0, start_pos - 10)
            preceding_text = text[context_start:start_pos]
            
            has_lower_limit = re.search(rf"({LOWER_LIMIT_WORDS})", preceding_text) is not None
            
            # 检查后面是否有"以上"等下限修饰词
            context_end = min(len(text), end_pos + 10)
            following_text = text[end_pos:context_end]
            
            has_following_lower_limit = re.search(rf"({LOWER_LIMIT_WORDS})", following_text) is not None
            
            # 半年固定为183天（半年的平均天数）
            days = 183
            
            if has_lower_limit or has_following_lower_limit:
                # 有下限修饰词，设置为183天到无穷大
                min_value = days
                max_value = float('inf')
            else:
                # 没有修饰词，就是固定的半年
                min_value = days
                max_value = days
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=DURATION_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results

    def _extract_special_up_expressions(self, text: str, existing_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """提取特殊的"上"字表达式，确保是时间相关的表达"""
        results = []
        
        for match in self.special_up_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_overlapping_with_existing(start_pos, end_pos, existing_results):
                continue
            
            # "上"后面必须跟时间单位，否则不识别
            time_unit = match.group(1)
            
            # 根据不同的时间单位设置不同的时间范围
            if time_unit in ["个", "月"]:
                # 上个月/上月
                min_value, max_value = self._calculate_last_month_range()
            elif time_unit in ["星期", "周", "礼拜", "週", "禮拜"]:
                # 上周/上星期
                min_value, max_value = self._calculate_last_week_range()
            elif time_unit in ["季度", "季"]:
                # 上季度
                min_value, max_value = self._calculate_last_quarter_range()
            elif time_unit in ["年"]:
                # 上年 (去年)
                min_value, max_value = self._calculate_last_year_range()
            elif time_unit in ["日", "天"]:
                # 上一天 (昨天)
                min_value, max_value = -1.0, -1.0
            elif time_unit in ["次"]:
                # 上次 - 相对模糊的过去时间点
                min_value, max_value = -30.0, -1.0
            else:
                # 默认处理为相对过去的时间
                min_value, max_value = -7.0, 0.0
            
            # 使用通用方法创建结果
            result = self._create_time_result(
                match_text=match_text,
                position=(start_pos, end_pos),
                slot_value=RELATIVE_TIME_RANGE,
                min_value=min_value,
                max_value=max_value
            )
            
            results.append(result)
        
        return results
    
    def _calculate_last_month_range(self) -> Tuple[float, float]:
        """计算上个月的相对天数范围"""
        now = datetime.datetime.now()
        current_year = now.year
        current_month = now.month
        
        # 计算上个月
        if current_month == 1:
            # 如果当前是1月，上个月是去年12月
            last_month_year = current_year - 1
            last_month = 12
        else:
            last_month_year = current_year
            last_month = current_month - 1
        
        # 上个月的第一天
        last_month_start = datetime.datetime(last_month_year, last_month, 1)
        
        # 上个月的最后一天
        if last_month == 12:
            next_month_start = datetime.datetime(last_month_year + 1, 1, 1)
        else:
            next_month_start = datetime.datetime(last_month_year, last_month + 1, 1)
        
        last_month_end = next_month_start - datetime.timedelta(days=1)
        
        # 计算相对天数
        days_to_start = (last_month_start - now).days
        days_to_end = (last_month_end - now).days
        
        return days_to_start, days_to_end
    
    def _calculate_last_week_range(self) -> Tuple[float, float]:
        """计算上周的相对天数范围"""
        now = datetime.datetime.now()
        
        # 当前周的开始（周一）
        this_week_start = now - datetime.timedelta(days=now.weekday())
        
        # 上周的开始和结束
        last_week_start = this_week_start - datetime.timedelta(days=7)
        last_week_end = this_week_start - datetime.timedelta(days=1)
        
        # 计算相对天数
        days_to_start = (last_week_start - now).days
        days_to_end = (last_week_end - now).days
        
        return days_to_start, days_to_end
    
    def _calculate_last_quarter_range(self) -> Tuple[float, float]:
        """计算上季度的相对天数范围"""
        now = datetime.datetime.now()
        current_year = now.year
        current_month = now.month
        
        # 计算当前季度
        current_quarter = (current_month - 1) // 3 + 1
        
        # 计算上季度
        if current_quarter == 1:
            # 如果当前是第一季度，上季度是去年第四季度
            last_quarter_year = current_year - 1
            last_quarter = 4
        else:
            last_quarter_year = current_year
            last_quarter = current_quarter - 1
        
        # 上季度的开始月份
        last_quarter_start_month = (last_quarter - 1) * 3 + 1
        
        # 上季度的开始和结束日期
        last_quarter_start = datetime.datetime(last_quarter_year, last_quarter_start_month, 1)
        
        if last_quarter == 4:
            # 如果是第四季度，结束日期是12月31日
            last_quarter_end = datetime.datetime(last_quarter_year, 12, 31)
        else:
            # 否则，结束日期是下一个季度的第一天减一天
            next_quarter_start_month = last_quarter_start_month + 3
            next_quarter_start = datetime.datetime(last_quarter_year, next_quarter_start_month, 1)
            last_quarter_end = next_quarter_start - datetime.timedelta(days=1)
        
        # 计算相对天数
        days_to_start = (last_quarter_start - now).days
        days_to_end = (last_quarter_end - now).days
        
        return days_to_start, days_to_end
    
    def _calculate_last_year_range(self) -> Tuple[float, float]:
        """计算去年的相对天数范围"""
        now = datetime.datetime.now()
        current_year = now.year
        
        # 去年的开始和结束
        last_year_start = datetime.datetime(current_year - 1, 1, 1)
        last_year_end = datetime.datetime(current_year - 1, 12, 31)
        
        # 计算相对天数
        days_to_start = (last_year_start - now).days
        days_to_end = (last_year_end - now).days
        
        return days_to_start, days_to_end

    def _is_overlapping_with_existing(self, start_pos: int, end_pos: int, existing_results: List[Dict[str, Any]]) -> bool:
        """检查当前位置是否与已有结果重叠"""
        for result in existing_results:
            other_start, other_end = result["position"]
            # 检查是否有重叠
            if (start_pos <= other_end and end_pos >= other_start):
                return True
        return False
    
    def _is_part_of_longer_match(self, text: str, start_pos: int, long_expr: str) -> bool:
        """检查当前位置是否是更长表达式的一部分"""
        # 计算可能的长表达式的起始位置
        possible_start = start_pos - (text[start_pos:].startswith(long_expr[1:]) and 1 or 0)
        if possible_start < 0:
            return False
            
        # 检查从该位置开始是否匹配长表达式
        return text[possible_start:possible_start+len(long_expr)] == long_expr
    
    def _calculate_half_year_range(self, is_first_half: bool) -> Tuple[float, float]:
        """计算半年范围的相对天数
        
        Args:
            is_first_half: 是否是上半年（True为上半年，False为下半年）
            
        Returns:
            Tuple[float, float]: (最小相对天数, 最大相对天数)
        """
        now = datetime.datetime.now()
        current_year = now.year
        
        if is_first_half:
            # 上半年：1月1日到6月30日
            start_date = datetime.datetime(current_year, 1, 1)
            end_date = datetime.datetime(current_year, 6, 30)
        else:
            # 下半年：7月1日到12月31日
            start_date = datetime.datetime(current_year, 7, 1)
            end_date = datetime.datetime(current_year, 12, 31)
        
        # 计算相对天数
        days_to_start = (start_date - now).days
        days_to_end = (end_date - now).days
        
        # 修正逻辑：
        # 如果当前时间在所指时间段内，则最大值应该是0（当前时间），而不是未来的时间点
        # 如果当前时间已经超过了时间段的结束时间，则使用时间段的实际范围（都是过去时间）
        # 如果当前时间还未到时间段的开始时间，则使用时间段的实际范围（都是未来时间）
        
        if days_to_start <= 0 and days_to_end >= 0:
            # 当前时间在时间段内，最大值应该是0（当前）
            min_value = days_to_start
            max_value = 0
        elif days_to_end < 0:
            # 时间段已经过去，使用实际范围
            min_value = days_to_start
            max_value = days_to_end
        else:
            # 时间段还未开始，使用实际范围
            min_value = days_to_start
            max_value = days_to_end
        
        return min_value, max_value

    def _extract_range_with_approximate(self, text: str) -> List[Dict[str, Any]]:
        """提取范围+近似词组合表达式，如"一到两年左右"等"""
        results = []
        
        for match in self.range_with_approximate_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            try:
                # 提取范围的两个数字和单位
                first_number = chinese_to_num(match.group('first_num'))
                second_number = chinese_to_num(match.group('second_num'))
                unit = match.group('unit')
                
                # 转换为天数
                first_days = convert_to_days(first_number, unit)
                second_days = convert_to_days(second_number, unit)
                
                # 由于有近似词，添加一定的浮动范围（这里使用20%的浮动）
                min_value = min(first_days, second_days)
                max_value = max(first_days, second_days) * 1.2  # 上限增加20%的浮动
                
                # 使用通用方法创建结果
                result = self._create_time_result(
                    match_text=match_text,
                    position=(start_pos, end_pos),
                    slot_value=DURATION_TIME_RANGE,
                    min_value=min_value,
                    max_value=max_value
                )
                
                results.append(result)
            except Exception as e:
                print(f"Error in _extract_range_with_approximate: {e}, match: {match_text}")
                continue
        
        return results

    def _extract_short_range_with_approximate(self, text: str) -> List[Dict[str, Any]]:
        """提取简写范围+近似词组合表达式，如"一两年左右"等"""
        results = []
        
        for match in self.short_range_with_approximate_pattern.finditer(text):
            match_text = match.group(0)
            start_pos, end_pos = match.span()
            
            # 检查是否与已有结果重叠
            if self._is_covered_by_other_matches(match_text, start_pos, end_pos, results):
                continue
            
            try:
                # 提取范围的两个数字和单位
                first_number = chinese_to_num(match.group('first_num'))
                second_number = chinese_to_num(match.group('second_num'))
                unit = match.group('unit')
                
                # 转换为天数
                first_days = convert_to_days(first_number, unit)
                second_days = convert_to_days(second_number, unit)
                
                # 由于有近似词，添加一定的浮动范围（这里使用20%的浮动）
                min_value = min(first_days, second_days)
                max_value = max(first_days, second_days) * 1.2  # 上限增加20%的浮动
                
                # 使用通用方法创建结果
                result = self._create_time_result(
                    match_text=match_text,
                    position=(start_pos, end_pos),
                    slot_value=DURATION_TIME_RANGE,
                    min_value=min_value,
                    max_value=max_value
                )
                
                results.append(result)
            except Exception as e:
                print(f"Error in _extract_short_range_with_approximate: {e}, match: {match_text}")
                continue
        
        return results


# 添加示例和使用说明
def example_usage():
    """时间抽取器使用示例"""
    extractor = TimeExtractor()
    
    # 示例文本
    texts = [
        "过去三天的数据",
        "最近一个月的销售情况",
        "未来两周的预测",
        "接下来半年的计划",
        "今年以来的增长率",
        "本月至今的交易量",
        "过去三个月到未来六个月的趋势",
        "投资期限在一年到三年之间",
        "持有时间不超过90天",
        "至少持有半年以上",
        "大约两年左右的项目",
        "一年零三个月的合同期",
    ]
    
    # 对每个示例文本进行时间提取
    for text in texts:
        results = extractor.extract_time(text)
        print(f"\n文本: {text}")
        for result in results:
            print(f"  匹配: {result['match']}")
            print(f"  位置: {result['position']}")
            print(f"  类型: {result['slot_value']}")
            print(f"  最小值: {result['minValue']} 天")
            print(f"  最大值: {result['maxValue']} 天")


if __name__ == "__main__":
    example_usage()