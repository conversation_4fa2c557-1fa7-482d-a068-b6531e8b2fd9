#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关键词扩充脚本
用于读取JSON格式的关键词列表，使用大模型进行扩充，并处理返回结果
"""

import json
import os
import re
from typing import Dict, List, Tuple, Any, Set
import argparse

# 导入并发处理引擎
from synthesis_engine import run_synthesis

class KeywordExpander:
    def __init__(self, input_file: str, output_file: str, temp_dir: str):
        """初始化关键词扩充器
        
        Args:
            input_file: 输入的JSON文件路径
            output_file: 输出的JSON文件路径
            temp_dir: 临时文件目录
        """
        self.input_file = input_file
        self.output_file = output_file
        self.temp_dir = temp_dir
        
        # 存储原始关键词数据
        self.keyword_data = {}
        
        # 全局关键词映射: 关键词 -> {槽位: 槽位值}
        self.keyword_mapping = {}
        
        # 确保目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir:  # 只在输出路径不为空时创建目录
            os.makedirs(output_dir, exist_ok=True)
        
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "input"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "output"), exist_ok=True) 
        
    def load_keywords(self) -> None:
        """加载关键词数据并构建映射"""
        print(f"读取关键词文件: {self.input_file}")
        
        with open(self.input_file, "r", encoding="utf-8") as f:
            self.keyword_data = json.load(f)
        
        # 构建关键词到槽位-槽位值的映射
        for slot, slot_values in self.keyword_data.items():
            for slot_value, keywords in slot_values.items():
                for keyword in keywords:
                    self._add_to_mapping(keyword, slot, slot_value)
    
    def _add_to_mapping(self, keyword: str, slot: str, slot_value: str) -> None:
        """添加关键词到映射字典中
        
        Args:
            keyword: 关键词
            slot: 槽位名称
            slot_value: 槽位值
        """
        if keyword not in self.keyword_mapping:
            self.keyword_mapping[keyword] = {}
        
        if slot not in self.keyword_mapping[keyword]:
            self.keyword_mapping[keyword][slot] = []
        
        if slot_value not in self.keyword_mapping[keyword][slot]:
            self.keyword_mapping[keyword][slot].append(slot_value)
    
    def prepare_tasks(self) -> None:
        """准备大模型扩充任务"""
        print("准备关键词扩充任务...")
        
        # 确保输入目录存在
        os.makedirs(os.path.join(self.temp_dir, "input"), exist_ok=True)
        
        tasks = []
        task_id = 0
        
        # 生成任务列表
        for slot, slot_values in self.keyword_data.items():
            for slot_value, keywords in slot_values.items():
                # 创建符合synthesis_engine要求的任务格式
                task = {
                    "id": f"task_{task_id}",  # 确保每个任务有唯一ID
                    "slot": slot,
                    "slot_value": slot_value,
                    "keywords": keywords
                }
                tasks.append(task)
                task_id += 1
        
        # 写入JSONL格式的任务文件
        input_file = os.path.join(self.temp_dir, "input", "keyword_tasks.jsonl")
        with open(input_file, "w", encoding="utf-8") as f:
            for task in tasks:
                f.write(json.dumps(task, ensure_ascii=False) + "\n")
        
        print(f"已创建 {len(tasks)} 个关键词扩充任务")
    
    def construct_prompt(self, task: Dict[str, Any]) -> Tuple[str, str]:
        """构造大模型提示词
        
        Args:
            task: 任务数据，包含槽位、槽位值和现有关键词列表
            
        Returns:
            指令和提示词文本的元组
        """
        # 直接从task中获取数据（因为prepare_tasks已经构造好了格式）
        slot = task["slot"]
        slot_value = task["slot_value"]
        keywords = task["keywords"]
        
        instruction = "扩充关键词列表"
        
        # 获取同一个slot下的其他槽位值及其示例
        same_slot_values = {}
        if slot in self.keyword_data:
            for other_slot_value, other_keywords in self.keyword_data[slot].items():
                if other_slot_value != slot_value and other_keywords:
                    # 获取1-2个示例关键词
                    examples = other_keywords[:min(2, len(other_keywords))]
                    same_slot_values[other_slot_value] = examples
        
        # 构建其他槽位值示例的描述
        other_values_desc = ""
        if same_slot_values:
            other_values_desc = f"\n\n注意：在同一个'{slot}'类别下，还有以下其他槽位值，请确保你生成的关键词与这些槽位值明确区分开：\n"
            for other_value, examples in same_slot_values.items():
                other_values_desc += f"- {other_value}：示例关键词 {', '.join(examples)}\n"
        
        prompt = f"""请帮我扩充以下关键词列表。这些关键词属于"{slot}"类别中的"{slot_value}"概念。
        
现有关键词: {', '.join(keywords)}{other_values_desc}

请基于这些现有关键词，生成更多可能的同义表达、相关表述、口语化表达或不同编码字体形式。

新增的关键词应该明确指向"{slot_value}"概念，避免模糊或通用的表达，并且要与同类别下的其他槽位值明确区分。

请以JSON数组格式返回所有新增关键词（不包括已有关键词）。格式如下:
```json
["新关键词1", "新关键词2", "新关键词3", ...]
```

请按照关键词长度从短到长返回，从两个字开始，不要重复，多考虑不同的表达词或可能的近义字词的替换顺序反转组合等，发挥想象力，并结合实际的理财咨询场景，不要返回空字符串。

更长的表达应该是通过更多样复杂的描述来表达这个槽位值，而不是基于前面短的表达的扩充，因为短的表达已经足够表达这个槽位值了。

只返回JSON数组，不要有其他解释。确保每个关键词都有实际意义且与"{slot}: {slot_value}"直接相关。"""
        
        return instruction, prompt
    
    def process_response(self, task: Dict[str, Any], response: str) -> Dict[str, Any]:
        """处理大模型返回的结果
        
        Args:
            task: 原始任务数据
            response: 大模型的响应
            
        Returns:
            处理后的结果
        """
        # 从raw_data中获取任务相关信息
        raw_data = task["raw_data"]
        slot = raw_data["slot"]
        slot_value = raw_data["slot_value"]
        original_keywords = set(raw_data["keywords"])
        
        # 尝试从响应中提取JSON数组
        try:
            # 使用正则表达式匹配JSON数组部分
            json_match = re.search(r'\[\s*"[^"]*"(?:\s*,\s*"[^"]*")*\s*\]', response)
            if json_match:
                json_str = json_match.group(0)
                new_keywords = json.loads(json_str)
            else:
                # 尝试直接解析整个响应
                new_keywords = json.loads(response)
            
            # 确保结果是列表
            if not isinstance(new_keywords, list):
                new_keywords = []
        except Exception as e:
            print(f"解析响应时出错 ({task['id']}): {str(e)}")
            print(f"原始响应: {response}")
            new_keywords = []
        
        # 对新关键词进行去重
        unique_keywords = []
        seen = set()
        for kw in new_keywords:
            if kw not in seen and kw.strip():
                seen.add(kw)
                unique_keywords.append(kw)
        
        # 过滤掉已有的关键词
        filtered_keywords = [kw for kw in unique_keywords if kw not in original_keywords]
        
        # 检查关键词是否已存在于其他槽位-槽位值中
        conflict_keywords = {}
        valid_keywords = []
        
        for keyword in filtered_keywords:
            conflicts = self._check_keyword_conflicts(keyword, slot, slot_value)
            if conflicts:
                conflict_keywords[keyword] = conflicts
            else:
                valid_keywords.append(keyword)
                # 将有效关键词添加到映射中
                self._add_to_mapping(keyword, slot, slot_value)
        
        # 返回处理结果
        return {
            "id": task["id"],
            "slot": slot,
            "slot_value": slot_value,
            "original_count": len(original_keywords),
            "new_count": len(valid_keywords),
            "conflict_count": len(conflict_keywords),
            "valid_keywords": valid_keywords,
            "conflict_keywords": conflict_keywords
        }
    
    def _check_keyword_conflicts(self, keyword: str, current_slot: str, current_slot_value: str) -> Dict[str, List[str]]:
        """检查关键词是否与其他槽位-槽位值冲突
        
        Args:
            keyword: 待检查的关键词
            current_slot: 当前的槽位
            current_slot_value: 当前的槽位值
            
        Returns:
            冲突的槽位和槽位值列表，如果没有冲突则返回空字典
        """
        conflicts = {}
        
        if keyword in self.keyword_mapping:
            for slot, slot_values in self.keyword_mapping[keyword].items():
                # 如果是相同槽位下的不同槽位值，则为冲突
                if slot == current_slot and current_slot_value not in slot_values:
                    if slot not in conflicts:
                        conflicts[slot] = []
                    conflicts[slot].extend(slot_values)
                # 如果是不同槽位，也视为冲突
                elif slot != current_slot:
                    if slot not in conflicts:
                        conflicts[slot] = []
                    conflicts[slot].extend(slot_values)
        
        return conflicts
    
    def merge_results(self) -> None:
        """合并处理结果并更新关键词数据"""
        print("合并处理结果...")
        
        output_file = os.path.join(self.temp_dir, "output", "keyword_tasks.jsonl")
        results = []
        
        # 读取结果文件
        try:
            with open(output_file, "r", encoding="utf-8") as f:
                for line in f:
                    results.append(json.loads(line.strip()))
        except FileNotFoundError:
            print(f"警告: 未找到结果文件 {output_file}")
            return
        
        # 创建新的关键词数据结构，只包含扩充的关键词
        expanded_keywords = {}
        
        # 更新关键词数据
        total_new = 0
        total_conflicts = 0
        
        for result in results:
            slot = result["slot"]
            slot_value = result["slot_value"]
            valid_keywords = result["valid_keywords"]
            
            # 将有效的新关键词添加到扩充数据中
            if valid_keywords:
                # 确保数据结构已初始化
                if slot not in expanded_keywords:
                    expanded_keywords[slot] = {}
                if slot_value not in expanded_keywords[slot]:
                    expanded_keywords[slot][slot_value] = []
                
                # 添加有效关键词
                expanded_keywords[slot][slot_value].extend(valid_keywords)
                total_new += len(valid_keywords)
            
            total_conflicts += result["conflict_count"]
        
        # 对每个槽位-槽位值的关键词进行去重和排序
        for slot, slot_values in expanded_keywords.items():
            for slot_value, keywords in slot_values.items():
                # 去重
                unique_keywords = list(set(keywords))
                # 按关键词长度从长到短排序
                expanded_keywords[slot][slot_value] = sorted(unique_keywords, key=len, reverse=True)
        
        # 保存扩充后的关键词数据
        with open(self.output_file, "w", encoding="utf-8") as f:
            json.dump(expanded_keywords, f, ensure_ascii=False, indent=4)
        
        print(f"关键词扩充完成:")
        print(f"- 新增有效关键词: {total_new}")
        print(f"- 发现冲突关键词: {total_conflicts}")
        print(f"- 扩充后的关键词文件已保存至: {self.output_file}")
    
    def run(self, api_config: Dict[str, Any]) -> None:
        """运行关键词扩充流程
        
        Args:
            api_config: API配置信息
        """
        # 加载关键词并构建映射
        self.load_keywords()
        
        # 准备任务
        self.prepare_tasks()
        
        # 运行并发处理引擎
        print("启动关键词扩充处理...")
        
        run_synthesis(
            input_dir=os.path.join(self.temp_dir, "input"),
            output_dir=os.path.join(self.temp_dir, "output"),
            construct_prompt_func=self.construct_prompt,
            process_response_func=self.process_response,
            config=api_config
        )
        
        # 合并结果
        self.merge_results()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="关键词扩充工具")
    parser.add_argument("--input", "-i", default="keywords_chinese.json", help="输入的关键词JSON文件")
    parser.add_argument("--output", "-o", default="keywords_chinese_expanded.json", help="输出的关键词JSON文件")
    parser.add_argument("--temp-dir", "-t", default="temp", help="临时文件目录")
    parser.add_argument("--api-key", "-k", default="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", help="API密钥")
    parser.add_argument("--api-url", default="https://openrouter.ai/api/v1/chat/completions", help="API URL")
    parser.add_argument("--model", default="deepseek/deepseek-v3-base:free", help="模型名称")
    parser.add_argument("--workers", "-w", type=int, default=1, help="并发工作线程数")
    
    args = parser.parse_args()
    
    # API配置
    api_config = {
        "API_KEY": args.api_key,
        "API_URL": args.api_url,
        "MODEL": args.model,
        "NUM_PROCESSORS": args.workers,
        "API_PARAMS": {
            "max_tokens": 8196,
            "temperature": 0.2
        }
    }
    
    # 创建并运行关键词扩充器
    expander = KeywordExpander(args.input, args.output, args.temp_dir)
    expander.run(api_config)

if __name__ == "__main__":
    main()