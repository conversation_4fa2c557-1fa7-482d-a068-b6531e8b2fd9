"""
收益率抽取器

专注于提取金融文本中的收益率信息

输入格式:
    text: str - 包含收益率信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的收益率信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'rate_range' 或 'specific_rate'
        "minValue": float,       # 范围最小值（小数形式，如0.05表示5%）
        "maxValue": float,       # 范围最大值（小数形式），可能为 None
    }
"""

import re   
from typing import Dict, List, Any, Tuple, Optional
from app.utils.extractor.shared.shared_utils import create_result, logger
from app.utils.extractor.num.number_convert import text_to_num
from app.utils.extractor.num.num_utils import (
    RateUnitHandler, RateModifierHandler,
)
from app.services.cache_service import cache_service

class RateExtractor:
    """
    收益率抽取器
    专注于从文本中提取收益率信息（通常为年化收益率、月化收益率等带有百分比的值）
    """
    # 槽位类型
    SPECIFIC_RATE = "specific_rate"
    RATE_RANGE = "rate_range"
    
    def extract_rate(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取收益率信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的收益率信息列表
        """
        try:
            results = []
            covered_positions = set()  # 记录已覆盖的位置
            
            # 处理统一收益率超级模式
            RATE_SUPER_PATTERN = cache_service.get_tool_regexs(slot_name="rate", tool_name="super_pattern")
            
            if not RATE_SUPER_PATTERN:
                logger.warning("[RATE_EXTRACTOR] Warning: 未找到收益率超级模式")
                return []
            
            for match in RATE_SUPER_PATTERN.finditer(text):
                result = self.process_unified_rate(match)
                if result and not self._is_position_covered(result["position"][0], result["position"][1], covered_positions):
                    self._mark_covered_positions(result["position"][0], result["position"][1], covered_positions)
                    results.append(result)
            
            # 过滤和标准化提取的值
            return self._filter_values(results)
            
        except Exception as e:
            logger.warning(f"[RATE_EXTRACTOR] Warning: {e.__class__.__name__}: {e}, 原始文本: '{text}'")
            return []
    
    def _process_rate_range_units(self, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 数字+单位+连接词+数字+单位"""
        try:
            value1 = RateUnitHandler.normalize_rate(components['rate_unit1'])
            value2 = RateUnitHandler.normalize_rate(components['rate_unit2'])
            min_value = min(value1, value2)
            max_value = max(value1, value2)
            return min_value, max_value, self.RATE_RANGE
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_rate_range_units Error: {e}")
            return None

    def _process_rate_range_numbers_unit(self, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 数字+连接词+数字+单位"""
        try:
            rate_context_prefix = components.get('rate_context_prefix', None)
            rate_context_suffix = components.get('rate_context_suffix', None)
            num1 = text_to_num(components['complex_num1'])
            num2 = text_to_num(components['complex_num2'])
            unit = components.get('unit_pattern', None)
            # 如果没有任何收益率上下文词和单位，则返回 None
            if not rate_context_prefix and not rate_context_suffix and not unit:
                return None
            value1 = RateUnitHandler.to_decimal(num1, unit)
            value2 = RateUnitHandler.to_decimal(num2, unit)
            min_value = min(value1, value2)
            max_value = max(value1, value2)
            return min_value, max_value, self.RATE_RANGE
        except (ValueError, KeyError) as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_rate_range_numbers_unit Error: {e}")
            return None

    def _process_single_rate_unit(self, match_text: str, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 单个收益率单元"""
        try:
            value = RateUnitHandler.normalize_rate(components['rate_unit'])
            # 如果没有修饰词，直接返回 SPECIFIC_RATE
            if not RateModifierHandler.has_modifier(match_text):
                return value, value, self.SPECIFIC_RATE
            else:
                # 有修饰词，返回 RATE_RANGE，等待后续 apply_modifiers 处理
                return value, value, self.RATE_RANGE
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_single_rate_unit Error: {e}")
            return None

    def _process_context_rate(self, match_text: str, components: Dict) -> Optional[Tuple[float, float, str]]:
        """处理 纯数字或百分比表达式(在收益率上下文中)"""
        try:
            # 只有在有收益率上下文时才处理纯数字
            has_context = 'rate_context_prefix' in components or 'rate_context_suffix' in components
                
            number_text = components['rate_context_num']
            # 检查是否为百分比表达式
            is_percent = "百分之" in number_text or "分之" in number_text
            
            # 如果不是百分比表达式且没有上下文，返回None
            if not is_percent and not has_context:
                return None
            
            number_value = text_to_num(number_text)
            
            # 如果是百分比表达式，text_to_num已经处理了转换
            # 如果不是百分比且数字大于1，则假设是百分比，转换为小数
            decimal_value = number_value if (is_percent or number_value < 1) else number_value / 100.0
            
            # 默认是 SPECIFIC_RATE，但如果有修饰词则为 RATE_RANGE
            slot_value = self.RATE_RANGE if RateModifierHandler.has_modifier(match_text) else self.SPECIFIC_RATE
            return decimal_value, decimal_value, slot_value
        except ValueError as e:
            logger.warning(f"[RATE_EXTRACTOR] _process_context_rate Error: {e}")
            return None

    def _dispatch_and_extract_initial(self, match_text: str, components: Dict) -> Optional[Tuple[float, float, str]]:
        """根据匹配的组件分发到相应的处理函数并提取初始值"""
        # 按优先级尝试匹配和处理
        if 'rate_unit1' in components and 'rate_unit2' in components:
            return self._process_rate_range_units(components)
        elif 'complex_num1' in components and 'complex_num2' in components:
            return self._process_rate_range_numbers_unit(components)
        elif 'rate_unit' in components:
            return self._process_single_rate_unit(match_text, components)
        elif 'rate_context_num' in components:
            return self._process_context_rate(match_text, components)
        else:
            # 没有匹配任何已知格式
            logger.debug(f"[RATE_EXTRACTOR] No dispatch rule matched for components: {components.keys()} in '{match_text}'")
            return None

    def process_unified_rate(self, match) -> Optional[Dict[str, Any]]:
        """处理统一收益率模式的匹配结果"""
        full_match_text = match.group(0)
        components = {name: value for name, value in match.groupdict().items() if value is not None}
        try:
            # 检查是否匹配到非收益需求表达，如果是则跳过
            if 'non_rate_expression' in components and components['non_rate_expression']:
                logger.debug(f"[RATE_EXTRACTOR] Skipping non-rate expression: '{full_match_text}'")
                return None
                
            # 获取实际的收益率表达式部分（不包含前缀）
            actual_expression = components.get('actual_rate_expression', '')
            if not actual_expression:
                logger.debug(f"[RATE_EXTRACTOR] No actual rate expression found in: '{full_match_text}'")
                return None
                
            # 直接获取actual_rate_expression分组的位置
            actual_start, actual_end = match.span('actual_rate_expression')
            
            # 第一阶段：分发并提取初始值
            initial_result = self._dispatch_and_extract_initial(full_match_text, components)

            # 如果没有提取到初始值，则返回None
            if initial_result is None:
                logger.debug(f"[RATE_EXTRACTOR] Initial extraction failed for: '{full_match_text}'")
                return None

            min_value, max_value, slot_value = initial_result

            # 第二阶段：应用修饰词并创建最终结果
            return self._apply_modifiers_and_create_result(
                min_value, max_value, slot_value, 
                full_match_text, actual_expression, 
                actual_start, actual_end
            )

        except Exception as e: # 捕获未预料的错误
            logger.warning(f"[RATE_EXTRACTOR] Unexpected Error in process_unified_rate for '{full_match_text}': {e.__class__.__name__}: {e}")
            return None
            
    def _apply_modifiers_and_create_result(self, min_value, max_value, slot_value, 
                                          full_match_text, actual_expression, 
                                          actual_start, actual_end) -> Optional[Dict[str, Any]]:
        """应用修饰词并创建最终结果"""
        try:
            apply_mod = RateModifierHandler.has_modifier(full_match_text)
            if apply_mod and slot_value == self.RATE_RANGE:
                # 确保传入 apply_modifiers 的值非 None
                current_min = min_value if min_value is not None else (max_value if max_value is not None else 0)
                current_max = max_value if max_value is not None else (min_value if min_value is not None else 0)
                min_value, max_value = RateModifierHandler.apply_modifiers(
                    current_min,
                    current_max,
                    full_match_text
                )
            elif slot_value == self.SPECIFIC_RATE:
                 # 确保 SPECIFIC_RATE 的 min/max 一致
                 final_value = min_value if min_value is not None else max_value
                 min_value = final_value
                 max_value = final_value

            # 创建最终结果 (检查最终值是否有效)
            if min_value is not None or max_value is not None:
                return create_result(
                    match=actual_expression,
                    position=(actual_start, actual_end),
                    slot="number",
                    slot_value=slot_value,
                    min_value=min_value,
                    max_value=max_value
                )
            else:
                logger.debug(f"[RATE_EXTRACTOR] Final min/max are None after processing modifiers for: '{full_match_text}'")
                return None
        except Exception as e:
            logger.warning(f"[RATE_EXTRACTOR] Error in _apply_modifiers_and_create_result: {e.__class__.__name__}: {e}")
            return None

    def _filter_values(self, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤无效的收益率值"""
        filtered_values = []
        
        for value in values:
            # 获取最小值和最大值
            min_value = value.get("minValue")
            max_value = value.get("maxValue")
            
            # 跳过无效数据情况
            if min_value is None and max_value is None:
                continue
                
            # 检查有效值是否在合理范围内（不超过1.0，即100%）
            if min_value is not None and min_value > 1.0:
                continue
                
            if max_value is not None and max_value > 1.0:
                continue
                
            # 检查最小值是否非负
            if min_value is not None and min_value < 0:
                continue
                
            # 如果最小值和最大值都存在，确保最大值大于等于最小值
            if min_value is not None and max_value is not None and max_value < min_value:
                continue
                
            # 通过所有检查，添加到结果列表
            filtered_values.append(value)
                
        return filtered_values
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos) 