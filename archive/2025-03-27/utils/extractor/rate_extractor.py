"""
收益率抽取器

专注于提取金融文本中的收益率信息

输入格式:
    text: str - 包含收益率信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的收益率信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'rate_range' 或 'specific_rate'
        "minValue": float,       # 范围最小值（小数形式，如0.05表示5%）
        "maxValue": float,       # 范围最大值（小数形式），可能为 None
    }
"""

import re   
from typing import Dict, List, Any, Tuple, Optional, Pattern, ClassVar
from app.utils.extractor.shared.shared_utils import create_rate_result

class RateExtractor:
    """
    收益率抽取器
    专注于从文本中提取收益率信息（通常为年化收益率、月化收益率等带有百分比的值）
    """
    
    # 基础数值模式
    NUMBER_PATTERN: ClassVar[str] = r"(?:\d+(?:\.\d+)?)"
    
    # 收益率单位模式
    RATE_UNITS: ClassVar[Dict[str, List[str]]] = {
        "PERCENT": ["%", "％", "个百分点", "百分点", "个点"],
        "PERMILLE": ["‰", "千分点"],
        "BASIC_POINT": ["BP", "bp", "个基点", "基点"]
    }
    
    # 收益率周期
    RATE_PERIODS: ClassVar[Dict[str, List[str]]] = {
        "YEAR": ["年化", "年收益率", "年息", "年利率", "年回报率", "/年", "每年"],
        "MONTH": ["月化", "月收益率", "月息", "月利率", "月回报率", "/月", "每月"],
        "DAY": ["日化", "日收益率", "日息", "日利率", "日回报率", "/日", "每日"],
        "WEEK": ["周化", "周收益率", "周息", "周利率", "周回报率", "/周", "每周"]
    }
    
    # 数值修饰词
    RATE_MODIFIERS: ClassVar[Dict[str, List[str]]] = {
        "PREFIX": ["约", "大约", "大概", "差不多", "接近", "将近", "预期", "预计", "目标", "期望"],
        "SUFFIX": ["左右", "上下", "出头", "不到", "多一点", "多点", "多些", "多"]
    }
    
    # 范围连接词
    RANGE_CONNECTORS: ClassVar[List[str]] = ["到", "至", "-", "~", "—", "－", "→"]
    
    # 范围描述词
    RANGE_DESCRIPTORS: ClassVar[Dict[str, List[str]]] = {
        "ABOVE": ["以上", "起", "开外", "多", "往上", "起步", "打底", "为主", "及以上", "或以上", "或更多", "最低", "至少"],
        "BELOW": ["以下", "之内", "以内", "内", "之下", "及以下", "或以下", "不超过", "最高", "封顶", "顶多", "至多"],
        "BETWEEN": ["之间", "上下", "左右", "前后", "范围内", "区间"]
    }
    
    # 编译好的正则表达式模式
    _PATTERNS: ClassVar[Dict[str, List[Tuple[Pattern, str]]]] = {}
    
    @classmethod
    def _ensure_patterns_compiled(cls) -> None:
        """确保正则表达式模式已编译"""
        if not cls._PATTERNS:
            cls._PATTERNS = cls._compile_patterns()
    
    @classmethod
    def _compile_patterns(cls) -> Dict[str, List[Tuple[Pattern, str]]]:
        """编译所有正则表达式模式"""
        patterns = {
            "range": [],
            "specific": []
        }
        
        # 1. 具体收益率范围模式
        range_pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*(?:{cls._build_range_connector_pattern()})\\s*{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}"
        patterns["range"].append((re.compile(range_pattern), "explicit_range"))
        
        # 2. 以上/以下/之间范围模式
        for direction, terms in cls.RANGE_DESCRIPTORS.items():
            for term in terms:
                if direction == "ABOVE":
                    pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "above"))
                elif direction == "BELOW":
                    pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "below"))
                elif direction == "BETWEEN":
                    pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "between"))
        
        # 3. 具体收益率模式
        specific_pattern = f"{cls.NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}"
        patterns["specific"].append((re.compile(specific_pattern), "specific"))
        
        return patterns
    
    @classmethod
    def _build_unit_pattern(cls) -> str:
        """构建单位模式"""
        all_units = []
        for units in cls.RATE_UNITS.values():
            all_units.extend(units)
        return f"(?:{'|'.join(re.escape(unit) for unit in all_units)})"
    
    @classmethod
    def _build_range_connector_pattern(cls) -> str:
        """构建范围连接词模式"""
        return '|'.join(re.escape(connector) for connector in cls.RANGE_CONNECTORS)
    
    @classmethod
    def extract_rate(cls, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取收益率信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的收益率信息列表
        """
        cls._ensure_patterns_compiled()
        
        extracted_values = []
        covered_positions = set()  # 记录已覆盖的位置
        
        # 1. 处理范围收益率
        range_values = cls._extract_range_rates(text, covered_positions)
        extracted_values.extend(range_values)
        
        # 2. 处理具体收益率
        specific_values = cls._extract_specific_rates(text, covered_positions)
        extracted_values.extend(specific_values)
        
        return cls._filter_and_normalize_values(extracted_values)
    
    @classmethod
    def _extract_range_rates(cls, text: str, covered_positions: set) -> List[Dict[str, Any]]:
        """提取范围收益率"""
        extracted = []
        
        for pattern, pattern_type in cls._PATTERNS["range"]:
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                if cls._is_position_covered(start, end, covered_positions):
                    continue
                    
                try:
                    numbers = cls._extract_numbers_from_text(match_text)
                    if len(numbers) >= 2:
                        min_val, max_val = cls._normalize_range_values(numbers[0], numbers[1], match_text)
                        match_info = create_rate_result(
                            match_text=match_text,
                            position=(start, end),
                            slot_value="rate_range",
                            min_value=min_val,
                            max_value=max_val
                        )
                        
                        cls._mark_covered_positions(start, end, covered_positions)
                        extracted.append(match_info)
                except ValueError as e:
                    print(f"收益率范围值处理错误: {e}，匹配文本: '{match_text}'")
                    continue
                except Exception as e:
                    print(f"收益率范围处理未预期错误: {e.__class__.__name__}: {e}，匹配文本: '{match_text}'")
                    continue
                    
        return extracted
    
    @classmethod
    def _extract_specific_rates(cls, text: str, covered_positions: set) -> List[Dict[str, Any]]:
        """提取具体收益率"""
        extracted = []
        
        for pattern, pattern_type in cls._PATTERNS["specific"]:
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                if cls._is_position_covered(start, end, covered_positions):
                    continue
                    
                try:
                    normalized_value = cls._normalize_rate(match_text, match_text)
                    
                    # 检查是否有"以上"等限定词
                    has_above_descriptor = any(above_term in text[max(0, start-5):end+5] for above_term in cls.RANGE_DESCRIPTORS["ABOVE"])
                    
                    # 检查是否有"以下"等限定词
                    has_below_descriptor = any(below_term in text[max(0, start-5):end+5] for below_term in cls.RANGE_DESCRIPTORS["BELOW"])
                    
                    if has_above_descriptor:
                        # 若有"以上"等限定词，设置minValue，maxValue为None
                        min_val = normalized_value
                        max_val = None
                    elif has_below_descriptor:
                        # 若有"以下"等限定词，设置maxValue，minValue为0
                        min_val = 0
                        max_val = normalized_value
                    else:
                        # 若无限定词，minValue和maxValue都设为该值
                        min_val = normalized_value
                        max_val = normalized_value
                    
                    match_info = create_rate_result(
                        match_text=match_text,
                        position=(start, end),
                        slot_value="specific_rate",
                        min_value=min_val,
                        max_value=max_val
                    )
                    
                    cls._mark_covered_positions(start, end, covered_positions)
                    extracted.append(match_info)
                except ValueError as e:
                    print(f"收益率值处理错误: {e}，匹配文本: '{match_text}'")
                    continue
                except Exception as e:
                    print(f"收益率处理未预期错误: {e.__class__.__name__}: {e}，匹配文本: '{match_text}'")
                    continue
                    
        return extracted
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos)
    
    @classmethod
    def _extract_numbers_from_text(cls, text: str) -> List[str]:
        """从文本中提取数字"""
        return re.findall(cls.NUMBER_PATTERN, text)
    
    @classmethod
    def _normalize_rate(cls, rate_text: str, context: str) -> float:
        """标准化收益率值（转换为小数形式）"""
        # 提取数字
        number_text = re.sub(r'[^\d.]', '', rate_text)
        if not number_text:
            raise ValueError(f"无法从'{rate_text}'中提取有效数字")
            
        try:
            rate = float(number_text)
        except ValueError:
            raise ValueError(f"无法将'{number_text}'转换为浮点数")
        
        # 根据上下文判断单位
        if any(unit in context for unit in cls.RATE_UNITS["PERMILLE"]):
            rate /= 1000  # 千分位转小数
        elif any(unit in context for unit in cls.RATE_UNITS["BASIC_POINT"]):
            rate /= 10000  # 基点转小数
        else:
            rate /= 100  # 百分比转小数
            
        return rate
    
    @classmethod
    def _normalize_range_values(cls, min_text: str, max_text: str, context: str) -> Tuple[float, float]:
        """标准化范围值"""
        min_val = cls._normalize_rate(min_text, context)
        max_val = cls._normalize_rate(max_text, context)
        return min_val, max_val
    
    @classmethod
    def _filter_and_normalize_values(cls, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤和归一化抽取的值"""
        filtered_values = []
        
        for value in values:
            # 检查值的合理性
            is_valid = False
            if "minValue" in value and value["minValue"] >= 0:
                if "maxValue" in value and (value["maxValue"] is None or (value["maxValue"] >= value["minValue"] and value["maxValue"] <= 1)):
                    is_valid = True
                elif "maxValue" not in value or value.get("maxValue") is None:  # 对于只有下限的情况或maxValue不存在的情况
                    is_valid = True
                    
            if is_valid:
                filtered_values.append(value)
                
        return filtered_values 