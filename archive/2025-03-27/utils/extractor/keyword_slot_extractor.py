"""
基于关键词的槽位抽取器
负责从文本中提取基于关键词匹配的槽位信息

输入格式:
    text: str - 待提取的文本
    
输出格式:
    List[Dict[str, Any]] - 提取的槽位信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "value": str,            # 条件值名称
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值
        "position": Tuple[int, int]  # 在原文中的位置 (start, end)
    }
"""

from typing import Dict, List, Tuple, Any, Set
import logging

from resource.slot_definitions import SLOTS
from resource.keywords_mapping import COMPILED_KEYWORD_PATTERNS
from app.utils.extractor.shared.shared_utils import create_keyword_result

logger = logging.getLogger(__name__)

class KeywordSlotExtractor:
    """
    基于关键词的槽位抽取器
    负责从文本中提取基于关键词匹配的槽位信息
    """
    
    def __init__(self):
        """
        初始化关键词槽位抽取器
        加载槽位定义和关键词映射
        """
        self.slots = SLOTS
        self.keyword_patterns = COMPILED_KEYWORD_PATTERNS
    
    def extract(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取关键词槽位信息
        
        Args:
            text (str): 待提取的文本
            
        Returns:
            List[Dict[str, Any]]: 槽位匹配信息列表，每个字典包含以下字段：
            {
                "match": str,            # 原文匹配的文本
                "value": str,            # 条件值名称
                "slot_value": str,       # 槽位值
                "slot": str,             # 槽位名称
                "position": Tuple[int, int]  # 在原文中的位置 (start, end)
            }
        """
        all_matches = []  # 存储所有匹配结果
        
        # 1. 首先匹配所有可能的槽位值
        for slot, values in self.keyword_patterns.items():
            # 获取槽位名称
            slot_name = self.slots[slot]["name"]
            
            # 遍历该槽位下的所有条件值
            for value_key, patterns in values.items():
                # 获取条件值的name属性
                slot_value = value_key
                value_name = self.slots[slot]["values"][value_key]["name"]
                is_bs_value = self.slots[slot]["values"][value_key].get("is_bs_value", True)
                
                # 遍历该条件值下的所有模式
                for pattern_tuple in patterns:
                    pattern, keyword = pattern_tuple
                    
                    # 使用正则表达式匹配
                    for match in pattern.finditer(text):
                        match_text = match.group()
                        start, end = match.start(), match.end()
                        
                        if is_bs_value:
                            value = value_name
                        else:
                            value = ""

                        # 记录匹配信息
                        match_info = {
                            "match": match_text,
                            "value": value,  # 使用条件值的name而不是键
                            "slot_value": slot_value,  # 添加槽位值字段
                            "slot": slot,
                            "position": (start, end),
                            "length": len(match_text)
                        }
                        all_matches.append(match_info)
        
        # 2. 按匹配长度降序排序
        all_matches.sort(key=lambda x: x["length"], reverse=True)
        
        # 3. 使用贪心算法选择不重叠的最长匹配结果
        covered_positions = set()  # 记录已覆盖的位置
        results = []
        
        for match in all_matches:
            start, end = match["position"]
            
            # 检查是否与已选择的结果重叠
            overlap = False
            for pos in range(start, end):
                if pos in covered_positions:
                    overlap = True
                    break
            
            if overlap:
                continue
            
            # 标记已覆盖的位置
            for pos in range(start, end):
                covered_positions.add(pos)
            
            # 添加到结果中
            results.append(create_keyword_result(
                match_text=match["match"],
                position=match["position"],
                slot_value=match["slot_value"],
                additional_info={"value": match["value"], "slot": match["slot"]}
            ))
        
        return results 