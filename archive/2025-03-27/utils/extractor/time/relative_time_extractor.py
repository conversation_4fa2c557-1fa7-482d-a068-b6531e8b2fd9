"""
金融领域相对时间抽取器

输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,                # 原文匹配的文本
        "position": Tuple[int, int], # 在原文中的位置 (start, end)
        "slot": str,                 # 槽位名称
        "slot_value": str,           # 槽位值类型：'relative_time_range'
        "minValue": float,           # 范围最小值（天数），负数表示过去，0表示当前，正数表示未来
        "maxValue": float,           # 范围最大值（天数），负数表示过去，0表示当前，正数表示未来
    }
"""
import re
import time
from typing import List, Dict, Any, Tuple, Set, Optional, Union
from datetime import datetime

from .parser.regex_pattern import TIME_CHAR_STRING, \
    FAKE_POSITIVE_START_STRING, FAKE_POSITIVE_END_STRING
from .parser.chinese_parser import extract_parentheses, remove_parentheses
from .parser.time_parser import TimeParser
from .time_utils import create_time_result

class RelativeTimeExtractor:
    """ 相对时间抽取器 """
    
    def __init__(self):
        self.parse_time = None
        self._prepare()
    
    def _prepare(self):
        """初始化解析器和正则表达式"""
        self.parse_time = TimeParser()
        self.time_string_pattern = re.compile(TIME_CHAR_STRING)

        self.fake_positive_start_pattern = re.compile(FAKE_POSITIVE_START_STRING)
        self.fake_positive_end_pattern = re.compile(FAKE_POSITIVE_END_STRING)

        # 与常用短语容易混淆的词
        self.non_time_string_list = [
            '一点', '0时', '一日', '黎明', '十分', '百分', '万分']

        # 数字正则
        self.num_pattern = re.compile(r'[０-９0-9]')

        # 四位数字正则
        self.four_num_year_pattern = re.compile(r'^[\d]{4}$')

        # 四数字后接单位，说明非年份
        self.unit_pattern = re.compile(r'(多)?[万亿元]') 

        # 单字符时间
        self.single_char_time = set(['春', '夏', '秋', '冬'])
    
    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """
        提取文本中的相对时间信息
        
        Args:
            text: 输入文本
            
        Returns:
            List[Dict[str, Any]]: 相对时间信息列表
        """
        # 使用内部方法提取时间
        time_spans = self._extract_time_spans(text)
        
        results = []
        now = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        for span in time_spans:
            if span.get('type') != 'time_span':
                continue
                
            match_text = span.get('text', '')
            start, end = span.get('offset', [0, 0])
            position = (start, end)
            
            detail = span.get('detail', {})
            time_range = detail.get('time', [])
            
            if len(time_range) == 2:
                # 将时间字符串转换为datetime对象，并去除时分秒
                start_time = datetime.strptime(time_range[0], '%Y-%m-%d %H:%M:%S').replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = datetime.strptime(time_range[1], '%Y-%m-%d %H:%M:%S').replace(hour=0, minute=0, second=0, microsecond=0)
                
                # 计算与当前时间的天数差，转为整数
                min_value = int((start_time - now).days)
                max_value = int((end_time - now).days)
                
                # 统一结果格式
                result = create_time_result(
                    match_text=match_text,
                    position=position,
                    slot_value='relative_time_range',
                    min_value=min_value,
                    max_value=max_value
                )
                
                results.append(result)
        
        return results
    
    def _extract_time_spans(self, text: str, time_base=time.time(), with_parsing=True, ret_all=False,
                           ret_type='str', ret_future=False, period_results_num=None) -> List[Dict[str, Any]]:
        """
        提取文本中的时间实体
        
        Args:
            text: 输入文本
            time_base: 基准时间
            with_parsing: 是否返回解析详情
            ret_all: 是否返回所有可能的时间实体，包括非时间语义的
            ret_type: 返回类型
            ret_future: 是否返回未来时间
            period_results_num: 周期结果数量限制
            
        Returns:
            List[Dict[str, Any]]: 时间实体列表
        """
        candidates_list = self.extract_time_candidates(text)

        time_entity_list = list()
        for candidate in candidates_list:
            offset = [0, 0]
            bias = 0
            while candidate['offset'][0] + offset[1] < candidate['offset'][1]:

                true_string, result, offset = self.grid_search(
                    candidate['time_candidate'][bias:], time_base=time_base,
                    ret_type=ret_type, ret_future=ret_future,
                    period_results_num=period_results_num)

                if true_string is not None:

                    # 判断字符串是否为大概率非时间语义
                    if (true_string in self.non_time_string_list) and (not ret_all):
                        bias += offset[1]
                        continue

                    # 判断四数字 "2033" 是否后接货币、非年份量词
                    if self.four_num_year_pattern.search(true_string):
                        back_start = candidate['offset'][0] + bias + offset[1]
                        if self.unit_pattern.search(text[back_start: back_start + 2]):
                            bias += offset[1]
                            continue

                    if with_parsing:
                        time_entity_list.append(
                            {'text': true_string,
                             'offset': [candidate['offset'][0] + bias + offset[0],
                                        candidate['offset'][0] + bias + offset[1]],
                             'type': result['type'],
                             'detail': result})
                    else:
                        time_entity_list.append(
                            {'text': true_string,
                             'offset': [candidate['offset'][0] + bias + offset[0],
                                        candidate['offset'][0] + bias + offset[1]],
                             'type': result['type']})
                    bias += offset[1]
                else:
                    break

        return time_entity_list
    
    def _filter(self, sub_string: str) -> bool:
        """
        对某些易造成实体错误的字符进行过滤。
        此问题产生的原因在于，parse_time 工具对某些不符合要求的字符串也能成功解析，造成假阳性。
        
        Args:
            sub_string: 待过滤的子字符串
            
        Returns:
            bool: 是否通过过滤
        """
        if self.fake_positive_start_pattern.search(sub_string[0]):
            return False

        if self.fake_positive_end_pattern.search(sub_string[-1]):
            return False

        if len(sub_string) != len(sub_string.strip()):
            return False

        if '的' in sub_string[0] or '的' in sub_string[-1]:
            return False

        if sub_string[0] in ')）' or sub_string[-1] in '(（':
            return False

        return True
    
    def grid_search(self, time_candidate: str, time_base=time.time(),
                    ret_type='str', ret_future=False, period_results_num=None) -> Tuple[Optional[str], Optional[Dict], Optional[List]]:
        """
        全面搜索候选时间字符串，从长至短，较优
        
        Args:
            time_candidate: 候选时间字符串
            time_base: 基准时间
            ret_type: 返回类型
            ret_future: 是否返回未来时间
            period_results_num: 周期结果数量限制
            
        Returns:
            Tuple[str, Dict, List]: 匹配的子字符串、解析结果、偏移量
        """
        length = len(time_candidate)
        for i in range(length):  
            for j in range(i):
                try:
                    offset = [j, length - i + j + 1]
                    sub_string = time_candidate[j: offset[1]]

                    # 处理可能的非时间字符串
                    if not self._filter(sub_string):
                        continue

                    sub_string_for_parse = sub_string.replace('的', '')
                    sub_string_for_parse = sub_string_for_parse.replace(' ', '')
                    sub_parentheses = extract_parentheses(sub_string_for_parse, parentheses='()（）')
                    if '周' in ''.join(sub_parentheses) or '星期' in ''.join(sub_parentheses):
                        sub_string_for_parse = remove_parentheses(sub_string_for_parse, parentheses='()（）')
                    if self.num_pattern.search(sub_string_for_parse[0]):
                        if j - 1 >= 0:
                            if self.num_pattern.search(time_candidate[j - 1]):
                                continue
                    if self.num_pattern.search(sub_string_for_parse[-1]):
                        if offset[1] < length:
                            if self.num_pattern.search(time_candidate[offset[1]]):
                                continue

                    result = self.parse_time(
                        sub_string_for_parse, time_base=time_base, strict=True,
                        ret_type=ret_type, ret_future=ret_future,
                        period_results_num=period_results_num)

                    return sub_string, result, offset
                except (ValueError, Exception):
                    continue

        return None, None, None
    
    def extract_time_candidates(self, text: str) -> List[Dict[str, Any]]:
        """
        获取所有的候选时间字符串，其中包含了时间实体
        
        Args:
            text: 输入文本
            
        Returns:
            List[Dict[str, Any]]: 候选时间字符串列表
        """
        text_length = len(text)
        idx_count = 0
        time_candidates_list = list()
        while idx_count < text_length:
            matched_res = self.time_string_pattern.search(text[idx_count:])
            if matched_res is not None:
                if len(matched_res.group()) > 1:
                    time_candidates_list.append(
                        {'time_candidate': matched_res.group(),
                         'offset': [idx_count + matched_res.span()[0],
                                    idx_count + matched_res.span()[1]]})
                elif matched_res.group() in self.single_char_time:
                    time_candidates_list.append(
                        {'time_candidate': matched_res.group(),
                         'offset': [idx_count + matched_res.span()[0],
                                    idx_count + matched_res.span()[1]]})
                idx_count += matched_res.span()[1]
            else:
                break

        return time_candidates_list
