"""
时间抽取相关常量和通用工具类
"""
from typing import Dict, Tuple, List, Any, Optional
import re
import calendar
from app.utils.extractor.num.num_utils import chinese_to_num, COMPLEX_NUMBER_PATTERN
from app.utils.extractor.shared.shared_utils import create_time_result as shared_create_time_result

# =========== 基础常量 ===========
# 中文数字映射
CHINESE_DIGITS = {
    '零': 0, '〇': 0, '0': 0,
    '一': 1, '壹': 1, '1': 1,
    '二': 2, '贰': 2, '兩': 2, '两': 2, '2': 2,
    '三': 3, '叁': 3, '3': 3,
    '四': 4, '肆': 4, '4': 4,
    '五': 5, '伍': 5, '5': 5,
    '六': 6, '陆': 6, '陸': 6, '6': 6,
    '七': 7, '柒': 7, '7': 7,
    '八': 8, '捌': 8, '8': 8,
    '九': 9, '玖': 9, '9': 9,
    '十': 10, '拾': 10,
    '百': 100, '佰': 100,
    '千': 1000, '仟': 1000,
    '万': 10000, '萬': 10000,
    '亿': 100000000, '億': 100000000,
    '半': 0.5  # 添加"半"对应的数值
}

# 时间单位定义
class TimeUnits:
    """时间单位定义类"""
    
    # 时间单位映射（原始单位 -> 标准单位）
    UNIT_MAPPING = {
        # 年相关
        "年": "年", "年份": "年",
        # 月相关
        "月": "月", "个月": "月", "個月": "月", "月份": "月", "个月份": "月",
        # 周相关
        "周": "周", "星期": "周", "礼拜": "周", "週": "周", "个周": "周", "个星期": "周", "個周": "周",
        # 天相关
        "天": "天", "日": "天", "号": "天", "號": "天", "个工作日": "天", "工作日": "天",
        # 季度相关
        "季度": "季度", "季": "季度", "个季度": "季度", "个季": "季度",
    }
    
    # 时间单位转换为天数的比例
    DAYS_CONVERSION = {
        "年": 366,
        "月": 30, 
        "季度": 90,
        "周": 7,
        "天": 1
    }
    
    @classmethod
    def get_all_units(cls) -> List[str]:
        """获取所有时间单位"""
        return list(cls.UNIT_MAPPING.keys())
    
    @classmethod
    def get_standard_unit(cls, unit: str) -> str:
        """获取标准化的时间单位"""
        return cls.UNIT_MAPPING.get(unit, unit)
    
    @classmethod
    def get_pattern(cls) -> str:
        """获取时间单位的正则表达式模式"""
        return f"({'|'.join(cls.get_all_units())})"

# 时间单位模式（使用TimeUnits类中的定义）
TIME_UNIT_PATTERN = TimeUnits.get_pattern()

# 槽位类型
RELATIVE_TIME_RANGE = "relative_time_range"
DURATION_TIME_RANGE = "duration_time_range"

# 时间范围连接词
TIME_RANGE_CONNECTOR = r'(到|至|~|-|、|和|与|或|及)'

# 复合时间连接词：用于连接多个时间单位形成复合时间
TIME_COMPOUND_CONNECTOR = r'(零|又|和|加|以及|再)'

# 半单位模式（"半年"、"半个月"等）
HALF_UNIT_PATTERN = fr'半(?:个|個)?({TIME_UNIT_PATTERN})'

# 单位加半模式（"一年半"、"两月半"等）
UNIT_HALF_PATTERN = fr'({COMPLEX_NUMBER_PATTERN})({TIME_UNIT_PATTERN})半'

# 定义基本时间单元模式
TIME_UNIT_EXPR = fr'''
(?:
  # 复合时间（如"1年3个月"）
  (?:{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN}
    (?:{TIME_COMPOUND_CONNECTOR}?{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN})+)
  |
  # 单位加半表达式（如"一年半"）
  (?:{UNIT_HALF_PATTERN})
  |
  # 半单位表达式（如"半年"、"半个月"）
  (?:{HALF_UNIT_PATTERN})
  |
  # 简单时间单位（数字+单位）
  (?:{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN})
  
)
'''
# =========== 持续时间提取器常量 ===========

# 近似修饰词：表示时间的近似范围
DURATION_APPROXIMATE_MODIFIER = r'(大约|大概|约|约为|接近|差不多|估计|粗略|近|基本|大致|左右|上下|内外|多)'

# 上限修饰词：表示时间的最大限制
DURATION_UPPER_LIMIT_MODIFIER = r'(不超过|不超過|少于|少於|不到|不足|最多|顶多|頂多|以下|以内|之内|内|未满|低于|低於|短于|短於)'

# 下限修饰词：表示时间的最小限制
DURATION_LOWER_LIMIT_MODIFIER = r'(以上|之上|开外|起|最少|至少|不少于|不少於|多于|多於|起码|高于|高於|长于|长於|不低于|不低於)'

# 后缀修饰词：位于时间单位后的修饰词
DURATION_SUFFIX_MODIFIER = r'(左右|上下|以上|以下|之内|以内|内外|之外|之久|出头|来)'

# 中缀模式特殊前缀
DURATION_SHORT_PREFIX = r'(短则|至少|最少|最短|短期内|短期来看)'

# 中缀模式特殊后缀
DURATION_LONG_PREFIX = r'(长则|最多|最长|长期来看)'

# 定义中缀分隔符
DURATION_INFIX_SEPARATOR = r'(?:[,，;；、:：]|\s+|而|但|但是|\n|\r\n)?\s*'

# 新增短期
SHORT_TERM_PATTERN = r'(短期不用|中短期|最近一段时间不用|可以放几个月|最近几个月不用|短期|暂时不需要用)'

# 长期关键词
LONG_TERM_PATTERN = r'(长期不用|可以放久一点|很久不用|期限久一点|长一些|长期|最近几年都不用)'

# =========== 公共数据结构定义 ===========
from dataclasses import dataclass

@dataclass
class TimeMatch:
    """时间匹配结果"""
    text: str
    start_pos: int
    end_pos: int
    components: Dict[str, Any]  # 解析出的组件
    is_valid: bool = True  # 添加一个字段来表示匹配是否有效
    
    @property
    def position(self) -> Tuple[int, int]:
        """获取匹配位置"""
        return (self.start_pos, self.end_pos)

@dataclass
class TimeRange:
    """时间范围结果"""
    min_value: float
    max_value: float
    slot_type: str  # 可以是"relative_time_range"或"duration_time_range"

class TimeProcessor:
    """时间处理器基类"""
    
    def __init__(self, pattern: str, processor_name: str, use_verbose: bool = True):
        self.pattern = re.compile(pattern, re.VERBOSE) if use_verbose else re.compile(pattern)
        self.processor_name = processor_name
    
    def match(self, text: str) -> List[TimeMatch]:
        """在文本中查找匹配"""
        matches = []
        for match in self.pattern.finditer(text):
            components = self.extract_components(match)
            if components:
                # 创建匹配结果
                time_match = TimeMatch(
                    text=match.group(0),
                    start_pos=match.start(),
                    end_pos=match.end(),
                    components=components
                )
                
                # 检查匹配是否有效（例如，避免年份被误识别为持续时间）
                time_match.is_valid = self.validate_match(time_match)
                
                matches.append(time_match)
        return matches
    
    def validate_match(self, match: TimeMatch) -> bool:
        """验证匹配是否有效，子类可以重写该方法进行特定验证"""
        return True
    
    def extract_components(self, match) -> Optional[Dict[str, Any]]:
        """提取匹配的组件，由子类实现"""
        raise NotImplementedError
    
    def process(self, match: TimeMatch) -> Optional[TimeRange]:
        """处理匹配，由子类实现"""
        raise NotImplementedError

class TimeParserPipeline:
    """时间解析管道，包含多个处理器"""
    
    def __init__(self, processors: List[TimeProcessor]):
        self.processors = processors
    
    def process(self, text: str) -> List[Dict[str, Any]]:
        """处理文本，提取所有时间表达式"""
        result = []
        
        # 为避免重复匹配，我们使用一个掩码来标记已经匹配过的文本部分
        mask = [False] * len(text)
        
        # 收集所有处理器的匹配
        all_matches = []
        for processor in self.processors:
            matches = processor.match(text)
            for match in matches:
                # 如果匹配被标记为无效，跳过
                if not match.is_valid:
                    continue
                    
                # 检查是否与之前的匹配重叠
                start, end = match.position
                if any(mask[start:end]):
                    continue  # 跳过重叠的匹配
                match.processor = processor
                all_matches.append(match)
        
        # 按照起始位置排序匹配
        all_matches.sort(key=lambda m: m.start_pos)
        
        # 处理排序后的匹配
        for match in all_matches:
            start, end = match.position
            # 如果当前匹配的任何部分已被标记，则跳过
            if any(mask[start:end]):
                continue
                
            # 处理当前匹配
            time_range = match.processor.process(match)
            if time_range:
                # 将结果添加到结果列表
                result.append(create_time_result(
                    match_text=match.text,
                    position=match.position,
                    slot_value=time_range.slot_type,
                    min_value=time_range.min_value,
                    max_value=time_range.max_value
                ))
                
                # 标记已处理的文本部分
                for i in range(start, end):
                    mask[i] = True
        return result

class TimeUnitHandler:
    """时间单位处理器"""
    
    @classmethod
    def standardize(cls, unit: str) -> str:
        """标准化时间单位"""
        return TimeUnits.get_standard_unit(unit)
    
    @classmethod
    def to_days(cls, value: float, unit: str) -> float:
        """将时间单位转换为天数"""
        standard_unit = cls.standardize(unit)
        return value * TimeUnits.DAYS_CONVERSION.get(standard_unit, 1)
    
    @classmethod
    def parse_number_and_unit(cls, text: str) -> Tuple[float, str]:
        """从文本中解析数字和单位"""
        # 提取数字
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        number_text = number_match.group(0) if number_match else "1"
        number = chinese_to_num(number_text)
        
        # 提取单位
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        unit = unit_match.group(0) if unit_match else "天"
        
        # 处理"半"字
        if "半" in text:
            if text.startswith("半"):
                number = 0.5
            elif "半" in text and number > 0:
                number += 0.5
                
        return number, unit
        
    @classmethod
    def is_year_identifier(cls, text: str) -> bool:
        """检查文本是否为年份标识符而不是持续时间
        
        例如："2024年"是年份，而不是2024个年的持续时间
        
        Args:
            text: 要检查的文本
            
        Returns:
            bool: 如果是年份标识符返回True，否则返回False
        """
        # 匹配四位数字后跟"年"的模式
        year_pattern = r'^(19|20)\d{2}年$'
        if re.match(year_pattern, text):
            return True
            
        # 也可以添加其他年份模式，如"二零二四年"等
        chinese_year_pattern = r'^[零一二三四五六七八九〇]{4}年$'
        if re.match(chinese_year_pattern, text):
            return True
            
        return False

class TimeModifierHandler:
    """时间修饰词处理器"""
    
    @staticmethod
    def has_approximate_modifier(text: str) -> bool:
        """检查是否有近似修饰词"""
        return bool(re.search(DURATION_APPROXIMATE_MODIFIER, text))
    
    @staticmethod
    def has_upper_limit_modifier(text: str) -> bool:
        """检查是否有上限修饰词"""
        return bool(re.search(DURATION_UPPER_LIMIT_MODIFIER, text))
    
    @staticmethod
    def has_lower_limit_modifier(text: str) -> bool:
        """检查是否有下限修饰词"""
        return bool(re.search(DURATION_LOWER_LIMIT_MODIFIER, text))
    
    @classmethod
    def apply_modifiers(cls, base_days: float, text: str) -> Tuple[float, float]:
        """应用修饰词调整基础天数范围"""
        # 根据方向调整范围
        if cls.has_past_modifier(text):
            min_value, max_value = -base_days, 0
        elif cls.has_future_modifier(text):
            min_value, max_value = 0, base_days
        elif cls.has_current_modifier(text):
            min_value, max_value = -base_days / 2, base_days / 2
        else:
            min_value, max_value = -base_days / 2, base_days / 2
        
        # 如果有近似修饰词，扩大范围
        if cls.has_approximate_modifier(text):
            range_span = max_value - min_value
            min_value -= range_span * 0.2
            max_value += range_span * 0.2
            
        return min_value, max_value

def create_time_result(match_text: str, position: Tuple[int, int], 
                       slot_value: str, min_value: float, max_value: float) -> Dict[str, Any]:
    """创建时间结果字典"""
    return shared_create_time_result(
        match_text=match_text,
        position=position,
        slot_value=slot_value,
        min_value=min_value,
        max_value=max_value
    )
