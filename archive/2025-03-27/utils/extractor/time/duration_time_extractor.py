"""
金融领域持续时间抽取器

专注于提取金融文本中的时间信息，抽取持续时间范围：

如"一年半"、"90天到180天"、"三年左右"等
    
特别说明：
    1. 一年按366天计算，一个月按30天计算
    2. 持续时间是指具体的时间跨度，如果有最小最大时间跨度描述，则最小最大值为对应的描述转天数，
       如果只有单个时间跨度描述，则最小最大值为该描述转天数。

输出格式:
    List[Dict[str, Any]] - 提取的时间信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,             # 槽位名称
        "slot_value": str,       # 槽位值类型：'duration_time_range'
        "minValue": float,       # 范围最小值（天数），可能为 None，但不与maxValue同时为None
        "maxValue": float,       # 范围最大值（天数），可能为 None，但不与minValue同时为None
    }
"""
from typing import Dict, List, Any, Optional, Tuple
import re
from app.utils.extractor.time.time_utils import (
    DURATION_TIME_RANGE, TIME_UNIT_PATTERN,
    DURATION_UPPER_LIMIT_MODIFIER, DURATION_LOWER_LIMIT_MODIFIER,
    DURATION_APPROXIMATE_MODIFIER, TIME_RANGE_CONNECTOR, DURATION_INFIX_SEPARATOR,
    TIME_UNIT_EXPR, DURATION_SHORT_PREFIX, DURATION_LONG_PREFIX, DURATION_SUFFIX_MODIFIER,
    SHORT_TERM_PATTERN, LONG_TERM_PATTERN,
    TimeUnitHandler, TimeModifierHandler,
    TimeMatch, TimeRange, TimeProcessor, TimeParserPipeline
)
from app.utils.extractor.num.num_utils import chinese_to_num, COMPLEX_NUMBER_PATTERN


# 时间范围模式
DURATION_SUPER_PATTERN_STR = fr'''
(?:
  # 上限修饰词（可选）
  (?P<upper_limit>{DURATION_UPPER_LIMIT_MODIFIER})?
  
  # 下限修饰词（可选）
  (?P<lower_limit>{DURATION_LOWER_LIMIT_MODIFIER})?
  
  # 近似修饰词（可选）
  (?P<approximate>{DURATION_APPROXIMATE_MODIFIER})?
  
  (?:
    # 格式1：时间单元+连接词+时间单元
    (?P<time_unit1>{TIME_UNIT_EXPR})
    (?P<connector>{TIME_RANGE_CONNECTOR})
    (?P<time_unit2>{TIME_UNIT_EXPR})
    |
    # 格式2：单个时间单元
    (?P<time_unit>{TIME_UNIT_EXPR})
    |
    # 格式3：数字+连接词+数字+时间单元
    (?P<number1>{COMPLEX_NUMBER_PATTERN})
    (?P<connector2>{TIME_RANGE_CONNECTOR})
    (?P<number2>{COMPLEX_NUMBER_PATTERN})
    (?P<shared_unit>{TIME_UNIT_PATTERN})
  )
  
  # 后缀修饰（可选）
  (?P<suffix_modifier>{DURATION_SUFFIX_MODIFIER})?
)
'''

# 修改中缀持续时间模式
DURATION_RANGE_WITH_INFIX_PATTERN_STR = fr'{DURATION_SHORT_PREFIX}的?{TIME_UNIT_EXPR}{DURATION_INFIX_SEPARATOR}{DURATION_LONG_PREFIX}的?{TIME_UNIT_EXPR}'


class UnifiedDurationProcessor(TimeProcessor):
    """统一持续时间处理器，处理所有持续时间表达式"""
    
    def extract_components(self, match) -> Optional[Dict[str, Any]]:
        """从匹配中提取组件"""
        components = {}
        
        # 收集所有非None的命名组
        for group_name, value in match.groupdict().items():
            if value is not None:
                components[group_name] = value
                
        return components
    
    def validate_match(self, match: TimeMatch) -> bool:
        """验证匹配是否有效，主要检测是否为年份标识符"""
        components = match.components
        
        # 检查是否为年份标识符
        if 'time_unit1' in components and 'time_unit2' in components:
            if TimeUnitHandler.is_year_identifier(components['time_unit1']) or \
               TimeUnitHandler.is_year_identifier(components['time_unit2']):
                return False
        
        elif 'time_unit' in components:
            if TimeUnitHandler.is_year_identifier(components['time_unit']):
                return False
                
        elif 'number1' in components and 'number2' in components and 'shared_unit' in components:
            number1 = chinese_to_num(components['number1'])
            number2 = chinese_to_num(components['number2'])
            unit = TimeUnitHandler.standardize(components['shared_unit'])
            
            if TimeUnitHandler.is_year_identifier(f"{number1}{unit}") or \
               TimeUnitHandler.is_year_identifier(f"{number2}{unit}"):
                return False
                
        return True
    
    def process(self, match: TimeMatch) -> Optional[TimeRange]:
        """处理匹配结果，转换为时间范围"""
        components = match.components
        min_value = None
        max_value = None
        
        # 处理连接词形式的时间范围
        if 'time_unit1' in components and 'time_unit2' in components:
            days1 = self._process_time_unit(components['time_unit1'])
            days2 = self._process_time_unit(components['time_unit2'])
            min_value = min(days1, days2)
            max_value = max(days1, days2)
        
        # 处理单个时间单元
        elif 'time_unit' in components:
            days = self._process_time_unit(components['time_unit'])
            min_value = max_value = days
            
        # 处理数字+连接词+数字+时间单元模式
        elif 'number1' in components and 'number2' in components and 'shared_unit' in components:
            number1 = chinese_to_num(components['number1'])
            number2 = chinese_to_num(components['number2'])
            unit = TimeUnitHandler.standardize(components['shared_unit'])
            
            days1 = self._convert_to_days(number1, unit)
            days2 = self._convert_to_days(number2, unit)
            
            min_value = min(days1, days2)
            max_value = max(days1, days2)
     
        if min_value is None or max_value is None:
            return None
        
        # 应用修饰词的影响
        min_value, max_value = self._apply_modifiers(min_value, max_value, match.text)
        
        return TimeRange(min_value, max_value, DURATION_TIME_RANGE)
    
    def _process_time_unit(self, text: str) -> float:
        """处理通用时间单元表达式，返回对应的天数"""
        # 检查是否是复合时间表达式
        if re.search(fr'{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN}.*{COMPLEX_NUMBER_PATTERN}{TIME_UNIT_PATTERN}', text):
            return self._process_compound_expression(text)
        
        # 检查是否是单位加半表达式
        unit_half_match = re.search(fr'({COMPLEX_NUMBER_PATTERN})({TIME_UNIT_PATTERN})半', text)
        if unit_half_match:
            return self._process_unit_half_expression(text)
        
        # 检查是否是半单位表达式
        half_unit_match = re.search(fr'半(?:个|個)?({TIME_UNIT_PATTERN})', text)
        if half_unit_match:
            return self._process_half_unit_expression(text)
        
        # 默认处理为简单时间单位
        number, unit = TimeUnitHandler.parse_number_and_unit(text)
        return self._convert_to_days(number, unit)
    
    def _process_compound_expression(self, text: str) -> float:
        """处理复合时间表达式（如"1年3个月"或"1年零3个月"）"""
        total_days = 0
        number_matches = list(re.finditer(COMPLEX_NUMBER_PATTERN, text))
        unit_matches = list(re.finditer(TIME_UNIT_PATTERN, text))
        
        if len(number_matches) == len(unit_matches):
            for i in range(len(number_matches)):
                num = chinese_to_num(number_matches[i].group(1))
                unit = TimeUnitHandler.standardize(unit_matches[i].group(1))
                total_days += self._convert_to_days(num, unit)
                
        return total_days
    
    def _process_unit_half_expression(self, text: str) -> float:
        """处理单位加半表达式（如"一年半"、"两月半"）"""
        # 提取数字和单位
        number_match = re.search(COMPLEX_NUMBER_PATTERN, text)
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        
        if not number_match or not unit_match:
            return 0
        
        number = chinese_to_num(number_match.group(1))
        unit = TimeUnitHandler.standardize(unit_match.group(1))
        
        # 计算基础天数
        base_days = self._convert_to_days(number, unit)
        
        # 计算"半"的天数（单位的一半）
        half_days = self._convert_to_days(0.5, unit)
        
        # 返回总天数
        return base_days + half_days
        
    def _process_half_unit_expression(self, text: str) -> float:
        """处理半单位表达式（如"半年"、"半个月"）"""
        # 提取单位
        unit_match = re.search(TIME_UNIT_PATTERN, text)
        
        if not unit_match:
            return 0
        
        unit = TimeUnitHandler.standardize(unit_match.group(1))
        
        # 计算半个单位的天数
        return self._convert_to_days(0.5, unit)
    
    def _convert_to_days(self, number: float, unit: str) -> float:
        """将数字和单位转换为天数"""
        return TimeUnitHandler.to_days(number, unit)
        
    def _apply_modifiers(self, min_value: float, max_value: float, match_text: str) -> Tuple[float, float]:
        """应用修饰词对时间范围的影响"""
        
        # 检查是否有各类修饰词
        has_lower_limit = TimeModifierHandler.has_lower_limit_modifier(match_text)
        has_upper_limit = TimeModifierHandler.has_upper_limit_modifier(match_text)
        # has_approximate = TimeModifierHandler.has_approximate_modifier(match_text)
            
        # 如果是单一值，使用TimeModifierHandler来应用修饰词
        if min_value == max_value:
            if has_lower_limit:
                min_value = max_value
                max_value = None
            elif has_upper_limit:
                min_value = 0
                max_value = max_value
                
            return min_value, max_value
        else:
            # 是范围值情况
            
            # 处理下限修饰词：例如"至少3-6个月"，表示最小值是6个月，最大值无限
            if has_lower_limit:
                min_value = max_value
                max_value = None
            
            # 处理上限修饰词：例如"不超过3-6个月"，表示最大值是6个月，最小值为0
            elif has_upper_limit:
                # 最大值保持不变，最小值设为0
                min_value = 0
            
            return min_value, max_value

class DurationRangeWithInfixProcessor(TimeProcessor):
    """带中缀的持续时间范围处理器（如"短则3天，长则7天"）"""
    def __init__(self, pattern_str: str, processor_name: str):
        super().__init__(pattern_str, processor_name)
    
    def extract_components(self, match) -> Optional[Dict[str, Any]]:
        """从匹配中提取组件"""
        # 解析文本中的两个数字和单位
        number_matches = list(re.finditer(COMPLEX_NUMBER_PATTERN, match.group()))
        number1 = chinese_to_num(number_matches[0].group(1))
        number2 = chinese_to_num(number_matches[1].group(1))
        
        # 查找单位
        unit_matches = list(re.finditer(TIME_UNIT_PATTERN, match.group()))
        if len(unit_matches) < 2:
            return None
            
        unit1 = TimeUnitHandler.standardize(unit_matches[0].group(1))
        unit2 = TimeUnitHandler.standardize(unit_matches[1].group(1))
        
        return {
            "number1": number1, 
            "unit1": unit1,
            "number2": number2, 
            "unit2": unit2
        }
    
    def validate_match(self, match: TimeMatch) -> bool:
        """验证匹配是否有效，检测是否为年份标识符"""
        components = match.components
        
        number1 = components.get("number1")
        unit1 = components.get("unit1")
        number2 = components.get("number2")
        unit2 = components.get("unit2")
        
        # 检查是否为年份标识符
        if TimeUnitHandler.is_year_identifier(f"{number1}{unit1}") or \
           TimeUnitHandler.is_year_identifier(f"{number2}{unit2}"):
            return False
            
        return True
    
    def process(self, match: TimeMatch) -> Optional[TimeRange]:
        """处理匹配结果，转换为时间范围"""
        number1 = match.components.get("number1")
        unit1 = match.components.get("unit1")
        number2 = match.components.get("number2")
        unit2 = match.components.get("unit2")
        
        # 转换为天数
        days1 = self._convert_to_days(number1, unit1)
        days2 = self._convert_to_days(number2, unit2)
        
        # 确保最小值在前，最大值在后
        min_value = min(days1, days2)
        max_value = max(days1, days2)
        
        return TimeRange(min_value, max_value, DURATION_TIME_RANGE)
    
    def _convert_to_days(self, number: float, unit: str) -> float:
        """将数字和单位转换为天数"""
        return TimeUnitHandler.to_days(number, unit)

class TermProcessor(TimeProcessor):
    """短期/长期处理器，用于处理'短期'和'长期'等表达式"""
    
    def __init__(self):
        """初始化处理器"""
        # 创建短期模式和长期模式的正则表达式
        short_term_pattern = SHORT_TERM_PATTERN
        long_term_pattern = LONG_TERM_PATTERN
        
        # 合并模式，使用非捕获组
        pattern = fr'(?:{short_term_pattern})|(?:{long_term_pattern})'
        
        super().__init__(pattern, "term_processor", use_verbose=False)
    
    def extract_components(self, match) -> Optional[Dict[str, Any]]:
        """从匹配中提取组件"""
        text = match.group(0)
        
        # 检查是否为短期或长期
        is_short_term = bool(re.search(SHORT_TERM_PATTERN, text))
        is_long_term = bool(re.search(LONG_TERM_PATTERN, text))
        
        return {
            "is_short_term": is_short_term,
            "is_long_term": is_long_term,
            "text": text
        }
    
    def process(self, match: TimeMatch) -> Optional[TimeRange]:
        """处理匹配结果，转换为时间范围"""
        components = match.components
        
        if components.get("is_short_term", False):
            # 短期: 30到180天
            min_value = 30.0
            max_value = 180.0
        elif components.get("is_long_term", False):
            # 长期: 180天以上，最大值无限
            min_value = 180.0
            max_value = None  # None 表示无上限
        else:
            return None
        
        return TimeRange(min_value, max_value, DURATION_TIME_RANGE)

class DurationTimeExtractor:
    """持续时间抽取器，用于提取文本中的持续时间表达（如"3个月"、"1-2年"等）"""
    
    def __init__(self):
        """初始化持续时间抽取器"""
        # 创建处理器列表，包括统一处理器和特殊处理器
        processors = [
            # 统一持续时间处理器
            UnifiedDurationProcessor(DURATION_SUPER_PATTERN_STR, "unified_duration"),
            
            # 特殊中缀模式处理器
            DurationRangeWithInfixProcessor(DURATION_RANGE_WITH_INFIX_PATTERN_STR, "infix_range"),
            
            # 短期/长期处理器
            TermProcessor(),
        ]
        
        self.pipeline = TimeParserPipeline(processors)
    
    def extract_time(self, text: str) -> List[Dict[str, Any]]:
        """从文本中提取持续时间表达式"""
        return self.pipeline.process(text) 
