"""
金额抽取器

专注于提取金融文本中的金额值

输入格式:
    text: str - 包含金额信息的文本字符串
    
输出格式:
    List[Dict[str, Any]] - 提取的金额信息列表
    每个字典包含以下字段：
    {
        "match": str,            # 原文匹配的文本
        "position": Tuple[int, int],  # 在原文中的位置 (start, end)
        "slot": str,                 # 槽位名称
        "slot_value": str,       # 槽位值类型：'amount_range' 或 'specific_amount'
        # 对于 amount_range:
        "minValue": float,       # 范围最小值（元），可能为 None
        "maxValue": float,       # 范围最大值（元），可能为 None
        # 对于 specific_amount:
        "value": float           # 具体金额值（元）
    }
"""

import re
from typing import Dict, List, Any, Tuple, Pattern, ClassVar
from app.utils.extractor.num.num_utils import (
    chinese_to_num, NUMBER_PATTERN, COMPLEX_NUMBER_PATTERN, 
    extract_numbers, CHINESE_NUMBER_PATTERN, ARABIC_NUMBER_PATTERN,
    MIXED_NUMBER_PATTERN
)
from app.utils.extractor.shared.shared_utils import create_amount_result

class AmountExtractor:
    """
    金额抽取器
    专注于从文本中提取金额值信息（通常为元、万元等带有货币单位的值）
    """
    
    # 货币单位模式
    CURRENCY_UNITS: ClassVar[Dict[str, List[str]]] = {
        "CNY": ["元", "块", "人民币", "rmb", "RMB", "￥"],
        "TEN_THOUSAND": ["万", "w", "W"],
        "HUNDRED_THOUSAND": ["十万"],
        "MILLION": ["百万", "佰万"],
        "TEN_MILLION": ["千万"],
        "HUNDRED_MILLION": ["亿"],
        "THOUSAND": ["千", "仟"],
        "HUNDRED": ["百", "佰"]
    }
    
    # 数值修饰词
    AMOUNT_MODIFIERS: ClassVar[Dict[str, List[str]]] = {
        "PREFIX": ["人民币", "约", "大约", "大概", "差不多", "接近", "将近", "快", "差", "至少", "最少", "最多", "最高"],
        "SUFFIX": ["左右", "上下", "出头", "不到", "多一点", "多点", "多些", "多", "几"]
    }
    
    # 范围连接词
    RANGE_CONNECTORS: ClassVar[List[str]] = ["到", "至", "-", "~", "—", "－", "→"]
    
    # 范围描述词
    RANGE_DESCRIPTORS: ClassVar[Dict[str, List[str]]] = {
        "ABOVE": ["以上", "起", "开外", "多", "往上", "起步", "打底", "为主", "及以上", "或以上", "或更多", "最低", "至少"],
        "BELOW": ["以下", "之内", "以内", "内", "之下", "及以下", "或以下", "不超过", "最高", "封顶", "顶多", "至多"],
        "BETWEEN": ["之间", "上下", "左右", "前后", "范围内", "区间"]
    }
    
    # 编译好的正则表达式模式
    _PATTERNS: ClassVar[Dict[str, List[Tuple[Pattern, str]]]] = {}
    
    @classmethod
    def _ensure_patterns_compiled(cls) -> None:
        """确保正则表达式模式已编译"""
        if not cls._PATTERNS:
            cls._PATTERNS = cls._compile_patterns()
    
    @classmethod
    def _compile_patterns(cls) -> Dict[str, List[Tuple[Pattern, str]]]:
        """编译所有正则表达式模式"""
        patterns = {
            "range": [],
            "specific": []
        }
        
        # 1. 具体金额范围模式
        range_pattern = f"{NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*(?:{cls._build_range_connector_pattern()})\\s*{NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}"
        patterns["range"].append((re.compile(range_pattern), "explicit_range"))
        
        # 2. 以上/以下范围模式
        for direction, terms in cls.RANGE_DESCRIPTORS.items():
            for term in terms:
                if direction == "ABOVE":
                    # 数字+单位+以上
                    pattern = f"{NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "above"))
                    
                    # 前缀词+单位 (如"最少要万元")
                    for prefix in cls.AMOUNT_MODIFIERS["PREFIX"]:
                        if prefix in ["至少", "最少", "最低"]:
                            for big_unit in ["万", "亿", "千万", "百万", "十万"]:
                                for currency_unit in cls.CURRENCY_UNITS["CNY"]:
                                    prefix_pattern = f"{prefix}\\s*{big_unit}\\s*{currency_unit}?"
                                    patterns["range"].append((re.compile(prefix_pattern), "unit_above"))
                elif direction == "BELOW":
                    # 数字+单位+以下
                    pattern = f"{NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "below"))
                    
                    # 前缀词+单位 (如"最多万元")
                    for prefix in cls.AMOUNT_MODIFIERS["PREFIX"]:
                        if prefix in ["最多", "最高", "顶多"]:
                            for big_unit in ["万", "亿", "千万", "百万", "十万"]:
                                for currency_unit in cls.CURRENCY_UNITS["CNY"]:
                                    prefix_pattern = f"{prefix}\\s*{big_unit}\\s*{currency_unit}?"
                                    patterns["range"].append((re.compile(prefix_pattern), "unit_below"))
                elif direction == "BETWEEN":
                    pattern = f"{NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}?\\s*{term}"
                    patterns["range"].append((re.compile(pattern), "between"))
        
        # 3. 具体金额模式 - 确保提取完整的金额表达式
        specific_pattern = f"{COMPLEX_NUMBER_PATTERN}\\s*{cls._build_unit_pattern()}"
        patterns["specific"].append((re.compile(specific_pattern), "specific"))
        
        # 4. 纯单位金额模式 (例如："万元以下") - 直接使用明确的单位
        for big_unit in ["万", "亿", "千万", "百万", "十万"]:
            # 为每个大单位创建明确的模式
            for currency_unit in cls.CURRENCY_UNITS["CNY"]:  # 只使用基本货币单位（元、块等）
                # 以下模式
                for term in cls.RANGE_DESCRIPTORS["BELOW"]:
                    below_pattern = f"{big_unit}\\s*{currency_unit}?\\s*{term}"
                    patterns["range"].append((re.compile(below_pattern), "unit_below"))
                # 以上模式
                for term in cls.RANGE_DESCRIPTORS["ABOVE"]:
                    above_pattern = f"{big_unit}\\s*{currency_unit}?\\s*{term}"
                    patterns["range"].append((re.compile(above_pattern), "unit_above"))
                
                # 直接单位作为金额 (例如："万元"等同于"一万元")
                unit_only_pattern = f"(?<![0-9一二三四五六七八九十百千万亿兩两壹贰叁肆伍陆柒捌玖拾佰仟萬億]){big_unit}\\s*{currency_unit}"
                patterns["specific"].append((re.compile(unit_only_pattern), "unit_as_amount"))
        
        # 5. 添加前缀词+单位作为金额
        for prefix in cls.AMOUNT_MODIFIERS["PREFIX"]:
            if prefix in ["约", "大约", "大概", "差不多"]:
                for big_unit in ["万", "亿", "千万", "百万", "十万"]:
                    for currency_unit in cls.CURRENCY_UNITS["CNY"]:
                        prefix_pattern = f"{prefix}\\s*{big_unit}\\s*{currency_unit}"
                        patterns["specific"].append((re.compile(prefix_pattern), "unit_as_amount"))
        
        return patterns
    
    @classmethod
    def _build_unit_pattern(cls) -> str:
        """构建单位模式"""
        all_units = []
        for units in cls.CURRENCY_UNITS.values():
            all_units.extend(units)
        return f"(?:{'|'.join(re.escape(unit) for unit in all_units)})"
    
    @classmethod
    def _build_range_connector_pattern(cls) -> str:
        """构建范围连接词模式"""
        return '|'.join(re.escape(connector) for connector in cls.RANGE_CONNECTORS)
    
    @classmethod
    def extract_amount(cls, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取金额信息
        
        Args:
            text (str): 用户输入的文本
            
        Returns:
            List[Dict[str, Any]]: 提取的金额信息列表
        """
        cls._ensure_patterns_compiled()
        
        extracted_values = []
        covered_positions = set()  # 记录已覆盖的位置
        
        # 1. 处理范围金额
        range_values = cls._extract_range_amounts(text, covered_positions)
        extracted_values.extend(range_values)
        
        # 2. 处理具体金额
        specific_values = cls._extract_specific_amounts(text, covered_positions)
        extracted_values.extend(specific_values)
        
        # 3. 处理单独的货币单位表达方式
        # 使用直接定义的模式（而非按单位拼凑）
        direct_patterns = [
            ("万元", 10000),
            ("万块", 10000),
            ("万人民币", 10000),
            ("亿元", 100000000),
            ("亿块", 100000000),
            ("亿人民币", 100000000),
            ("十万元", 100000),
            ("百万元", 1000000),
            ("千万元", 10000000)
        ]
        
        for pattern, value in direct_patterns:
            for match in re.finditer(pattern, text):
                match_text = match.group()
                start, end = match.span()
                
                if not cls._is_position_covered(start, end, covered_positions):
                    # 检查是否为独立单位，而非数字+单位
                    is_independent = True
                    if start > 0:
                        prev_char = text[start-1]
                        if prev_char.isdigit() or prev_char in "一二三四五六七八九十百千万亿兩两壹贰叁肆伍陆柒捌玖拾佰仟萬億":
                            is_independent = False
                    
                    if is_independent:
                        match_info = {
                            "match": match_text,
                            "position": (start, end),
                            "value": value,  # 直接使用预定义值
                            "slot": "number",
                            "slot_value": "specific_amount"
                        }
                        cls._mark_covered_positions(start, end, covered_positions)
                        extracted_values.append(match_info)
        
        return cls._filter_and_normalize_values(extracted_values)
    
    @classmethod
    def _extract_range_amounts(cls, text: str, covered_positions: set) -> List[Dict[str, Any]]:
        """提取范围金额"""
        extracted = []
        
        for pattern, pattern_type in cls._PATTERNS["range"]:
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                if cls._is_position_covered(start, end, covered_positions):
                    continue
                    
                try:
                    if pattern_type == "explicit_range":
                        # 从文本中提取所有数字表达
                        numbers = extract_numbers(match_text)
                        if len(numbers) >= 2:
                            min_val, max_val = cls._normalize_range_values(numbers[0][0], numbers[1][0], match_text)
                            match_info = create_amount_result(
                                match_text=match_text,
                                position=(start, end),
                                slot_value="amount_range",
                                min_value=min_val,
                                max_value=max_val
                            )
                    elif pattern_type == "above":
                        # 提取数字并转换
                        numbers = extract_numbers(match_text)
                        if numbers:
                            number = numbers[0][0]
                            value = cls._normalize_amount(number, match_text)
                            match_info = create_amount_result(
                                match_text=match_text,
                                position=(start, end),
                                slot_value="amount_range",
                                min_value=value,
                                max_value=None
                            )
                    elif pattern_type == "below":
                        # 提取数字并转换
                        numbers = extract_numbers(match_text)
                        if numbers:
                            number = numbers[0][0]
                            value = cls._normalize_amount(number, match_text)
                            match_info = create_amount_result(
                                match_text=match_text,
                                position=(start, end),
                                slot_value="amount_range",
                                min_value=0,
                                max_value=value
                            )
                    elif pattern_type == "unit_above":
                        # 处理纯单位的"以上"表达式，例如"万元以上"
                        unit_value = cls._get_unit_value(match_text)
                        match_info = create_amount_result(
                            match_text=match_text,
                            position=(start, end),
                            slot_value="amount_range",
                            min_value=unit_value,
                            max_value=None
                        )
                    elif pattern_type == "unit_below":
                        # 处理纯单位的"以下"表达式，例如"万元以下"
                        unit_value = cls._get_unit_value(match_text)
                        match_info = create_amount_result(
                            match_text=match_text,
                            position=(start, end),
                            slot_value="amount_range",
                            min_value=0,
                            max_value=unit_value
                        )
                    else:
                        continue
                    
                    cls._mark_covered_positions(start, end, covered_positions)
                    extracted.append(match_info)
                except ValueError as e:
                    print(f"金额范围值处理错误: {e}，匹配文本: '{match_text}'")
                    continue
                except Exception as e:
                    print(f"金额范围处理未预期错误: {e.__class__.__name__}: {e}，匹配文本: '{match_text}'")
                    continue
                    
        return extracted
    
    @classmethod
    def _extract_specific_amounts(cls, text: str, covered_positions: set) -> List[Dict[str, Any]]:
        """提取具体金额"""
        extracted = []
        
        for pattern, pattern_type in cls._PATTERNS["specific"]:
            for match in pattern.finditer(text):
                match_text = match.group()
                start, end = match.span()
                
                if cls._is_position_covered(start, end, covered_positions):
                    continue
                    
                try:
                    # 根据模式类型处理金额
                    if pattern_type == "unit_as_amount":
                        # 处理单位作为金额的情况 (如"万元"等同于"一万元")
                        value = cls._get_unit_value(match_text)
                    else:
                        # 使用统一的转换方法
                        value = cls._normalize_amount(match_text, match_text)
                    
                    # 对纯单位金额再进行一次特殊处理
                    if value == 0 and any(big_unit in match_text for big_unit in ["万", "亿", "十万", "百万", "千万"]):
                        if "万" in match_text and "千万" not in match_text:
                            value = 10000
                        elif "十万" in match_text:
                            value = 100000
                        elif "百万" in match_text:
                            value = 1000000
                        elif "千万" in match_text:
                            value = 10000000
                        elif "亿" in match_text:
                            value = 100000000
                    
                    match_info = create_amount_result(
                        match_text=match_text,
                        position=(start, end),
                        slot_value="specific_amount",
                        value=value
                    )
                    
                    cls._mark_covered_positions(start, end, covered_positions)
                    extracted.append(match_info)
                except ValueError as e:
                    print(f"具体金额值处理错误: {e}，匹配文本: '{match_text}'")
                    continue
                except Exception as e:
                    print(f"具体金额处理未预期错误: {e.__class__.__name__}: {e}，匹配文本: '{match_text}'")
                    continue
                    
        return extracted
    
    @staticmethod
    def _is_position_covered(start: int, end: int, covered_positions: set) -> bool:
        """检查位置是否已被覆盖"""
        return any(pos in covered_positions for pos in range(start, end))
    
    @staticmethod
    def _mark_covered_positions(start: int, end: int, covered_positions: set) -> None:
        """标记已覆盖的位置"""
        for pos in range(start, end):
            covered_positions.add(pos)
    
    @classmethod
    def _extract_numbers_from_text(cls, text: str) -> List[str]:
        """从文本中提取数字"""
        # 使用新的extract_numbers函数
        extracted = extract_numbers(text)
        return [num[0] for num in extracted]
    
    @classmethod
    def _normalize_amount(cls, amount_text: str, context: str) -> float:
        """标准化金额值（转换为元）"""
        if not amount_text or not isinstance(amount_text, str):
            raise ValueError(f"金额文本无效: '{amount_text}'")
            
        try:
            # 使用通用工具处理各类数字形式
            amount = chinese_to_num(amount_text)
            
            if amount is None or amount < 0:
                raise ValueError(f"无法从'{amount_text}'提取有效金额")
                
            # 如果已经是包含单位的表达式，则不再重复应用单位转换
            # 检查原始文本中是否包含"万"、"亿"等大单位
            has_unit = False
            for big_unit in ["万", "萬", "亿", "億"]:
                if big_unit in amount_text:
                    has_unit = True
                    break
            
            # 只有在原始表达式中不包含大单位时，才根据上下文应用单位转换
            if not has_unit:
                # 应用单位转换（元、万元、亿元等）
                for unit_type, units in cls.CURRENCY_UNITS.items():
                    for unit in units:
                        if unit in context:
                            # 根据单位进行转换
                            if unit_type == "TEN_THOUSAND":
                                amount *= 10000
                            elif unit_type == "HUNDRED_THOUSAND":
                                amount *= 100000
                            elif unit_type == "MILLION":
                                amount *= 1000000
                            elif unit_type == "TEN_MILLION":
                                amount *= 10000000
                            elif unit_type == "HUNDRED_MILLION":
                                amount *= 100000000
                            elif unit_type == "THOUSAND":
                                amount *= 1000
                            elif unit_type == "HUNDRED":
                                amount *= 100
                            break  # 找到第一个单位就退出
                        
            return amount
        except Exception as e:
            raise ValueError(f"金额转换错误: {e}, 原始文本: '{amount_text}'")
    
    @classmethod
    def _normalize_range_values(cls, min_text: str, max_text: str, context: str) -> Tuple[float, float]:
        """标准化范围值"""
        if not min_text or not max_text:
            raise ValueError(f"范围值文本不完整: 最小值='{min_text}', 最大值='{max_text}'")
            
        min_val = cls._normalize_amount(min_text, context)
        max_val = cls._normalize_amount(max_text, context)
        
        # 检查最小值不大于最大值
        if min_val > max_val:
            raise ValueError(f"范围值无效: 最小值({min_val})大于最大值({max_val})")
            
        return min_val, max_val
    
    @classmethod
    def _filter_and_normalize_values(cls, values: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤和标准化提取的值"""
        filtered_values = []
        
        for value in values:
            # 检查值的合理性
            is_valid = False
            if "value" in value and value["value"] > 0:  # 金额需要大于0
                is_valid = True
            elif "minValue" in value and "maxValue" in value:
                if value["minValue"] >= 0 and (value["maxValue"] is None or value["maxValue"] >= value["minValue"]):
                    is_valid = True
                    
            if is_valid:
                filtered_values.append(value)
                
        return filtered_values
    
    @classmethod
    def _get_unit_value(cls, text: str) -> float:
        """获取单位的数值"""
        if not text or not isinstance(text, str):
            raise ValueError(f"单位文本无效: '{text}'")
            
        # 对于明确的单位表达式，给出确定的值
        if "万" in text and "千万" not in text:
            return 10000
        elif "十万" in text:
            return 100000
        elif "百万" in text or "佰万" in text:
            return 1000000
        elif "千万" in text:
            return 10000000
        elif "亿" in text:
            return 100000000
        elif "千" in text or "仟" in text:
            return 1000
        elif "百" in text or "佰" in text:
            return 100
        elif "十" in text or "拾" in text:
            return 10
        
        # 默认返回1
        return 1.0 