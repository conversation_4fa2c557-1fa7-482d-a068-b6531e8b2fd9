"""
通用工具类和函数，用于所有抽取器共享
"""
from typing import Dict, Tuple, List, Any, Optional, Union, Literal


def create_slot_result(
    match_text: str, 
    position: Tuple[int, int], 
    slot_type: str, 
    slot_value: str, 
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    value: Optional[float] = None,
    additional_info: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """创建标准化的槽位提取结果

    Args:
        match_text: 匹配到的原文文本
        position: 在原文中的位置 (start, end)
        slot_type: 槽位类型，如'time', 'number', 'keyword'等
        slot_value: 槽位值类型，如'time_range', 'amount_range', 'specific_amount'等
        min_value: 范围最小值，对于范围类型结果
        max_value: 范围最大值，对于范围类型结果
        value: 具体数值，对于具体值类型结果
        additional_info: 额外的附加信息，如单位、置信度等

    Returns:
        Dict[str, Any]: 标准格式的结果字典
    """
    result = {
        "match": match_text,
        "startPos": position[0],
        "endPos": position[1],
        "slot": slot_type,
        "slot_value": slot_value
    }

    # 添加范围值（如果有）
    if min_value is not None:
        result["minValue"] = min_value
    if max_value is not None:
        result["maxValue"] = max_value

    if min_value and not max_value:
        result["maxValue"] = None
    if max_value and not min_value:
        result["minValue"] = None

    # 添加具体值（如果有）
    if value is not None:
        result["value"] = value

    # 添加额外信息（如果有）
    if additional_info:
        result.update(additional_info)

    return result


def create_time_result(
    match_text: str, 
    position: Tuple[int, int], 
    slot_value: str, 
    min_value: float, 
    max_value: float
) -> Dict[str, Any]:
    """创建时间结果对象（向后兼容）

    Args:
        match_text: 匹配到的原文文本
        position: 在原文中的位置 (start, end)
        slot_value: 时间类型，如'relative_time_range', 'duration_time_range'
        min_value: 范围最小值（天数）
        max_value: 范围最大值（天数）

    Returns:
        Dict[str, Any]: 标准格式的结果字典
    """
    return create_slot_result(
        match_text=match_text,
        position=position,
        slot_type="time_range",
        slot_value=slot_value,
        min_value=min_value,
        max_value=max_value
    )


def create_amount_result(
    match_text: str, 
    position: Tuple[int, int], 
    slot_value: Literal["amount_range", "specific_amount"], 
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    value: Optional[float] = None
) -> Dict[str, Any]:
    """创建金额结果对象

    Args:
        match_text: 匹配到的原文文本
        position: 在原文中的位置 (start, end)
        slot_value: 金额类型，'amount_range'或'specific_amount'
        min_value: 范围最小值（元），对于amount_range类型
        max_value: 范围最大值（元），对于amount_range类型
        value: 具体金额值（元），对于specific_amount类型

    Returns:
        Dict[str, Any]: 标准格式的结果字典
    """
    return create_slot_result(
        match_text=match_text,
        position=position,
        slot_type="number",
        slot_value=slot_value,
        min_value=min_value,
        max_value=max_value,
        value=value
    )


def create_rate_result(
    match_text: str, 
    position: Tuple[int, int], 
    slot_value: Literal["rate_range", "specific_rate"], 
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    value: Optional[float] = None
) -> Dict[str, Any]:
    """创建收益率结果对象

    Args:
        match_text: 匹配到的原文文本
        position: 在原文中的位置 (start, end)
        slot_value: 收益率类型，'rate_range'或'specific_rate'
        min_value: 范围最小值，对于rate_range类型
        max_value: 范围最大值，对于rate_range类型
        value: 具体收益率值，对于specific_rate类型

    Returns:
        Dict[str, Any]: 标准格式的结果字典
    """
    return create_slot_result(
        match_text=match_text,
        position=position,
        slot_type="number",
        slot_value=slot_value,
        min_value=min_value,
        max_value=max_value,
        value=value
    )


def create_keyword_result(
    match_text: str, 
    position: Tuple[int, int], 
    slot_value: str,
    additional_info: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """创建关键词结果对象

    Args:
        match_text: 匹配到的原文文本
        position: 在原文中的位置 (start, end)
        slot_value: 关键词类型
        additional_info: 额外的附加信息，如置信度等

    Returns:
        Dict[str, Any]: 标准格式的结果字典
    """
    return create_slot_result(
        match_text=match_text,
        position=position,
        slot_type="keyword",
        slot_value=slot_value,
        additional_info=additional_info
    )


def filter_overlapping_results(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """过滤重叠的结果，保留覆盖范围更大的结果

    Args:
        results: 原始结果列表

    Returns:
        List[Dict[str, Any]]: 过滤后的结果列表
    """
    if not results:
        return []
        
    # 按照匹配文本长度降序排序，长度相同时按起始位置升序排序
    sorted_results = sorted(
        results, 
        key=lambda x: (-len(x.get('match', '')), x.get('startPos', 0))
    )
    
    filtered_results = []
    covered_positions = set()
    
    for result in sorted_results:
        start_pos = result.get('startPos', 0)
        end_pos = result.get('endPos', 0)
        
        # 检查是否与已有的覆盖位置重叠
        if any(pos in covered_positions for pos in range(start_pos, end_pos)):
            continue
            
        # 添加当前结果，并标记已覆盖的位置
        filtered_results.append(result)
        for pos in range(start_pos, end_pos):
            covered_positions.add(pos)
            
    # 按照原始位置排序返回结果
    return sorted(filtered_results, key=lambda x: (x.get('startPos', 0), x.get('endPos', 0)))
