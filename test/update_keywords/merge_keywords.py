#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
关键词合并脚本
用于将扩充后的关键词合并到原始关键词文件中
"""

import json
import os
import argparse
from typing import Dict, List, Any


class KeywordMerger:
    def __init__(self, original_file: str, expanded_file: str, output_file: str):
        """初始化关键词合并器
        
        Args:
            original_file: 原始关键词JSON文件路径
            expanded_file: 扩充关键词JSON文件路径
            output_file: 输出的合并后JSON文件路径
        """
        self.original_file = original_file
        self.expanded_file = expanded_file
        self.output_file = output_file
        
        # 存储原始关键词数据
        self.original_data = {}
        
        # 存储扩充关键词数据
        self.expanded_data = {}
        
        # 存储合并后的关键词数据
        self.merged_data = {}
        
        # 存储原始槽位和槽位值的顺序
        self.slot_order = []
        self.slot_value_order = {}
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir:  # 只在输出路径不为空时创建目录
            os.makedirs(output_dir, exist_ok=True)
    
    def load_data(self) -> None:
        """加载原始和扩充的关键词数据"""
        print(f"读取原始关键词文件: {self.original_file}")
        with open(self.original_file, "r", encoding="utf-8") as f:
            # 读取JSON保持键顺序
            self.original_data = json.load(f)
            # 记录槽位顺序
            self.slot_order = list(self.original_data.keys())
            # 记录每个槽位中槽位值的顺序
            for slot, slot_values in self.original_data.items():
                self.slot_value_order[slot] = list(slot_values.keys())
        
        print(f"读取扩充关键词文件: {self.expanded_file}")
        with open(self.expanded_file, "r", encoding="utf-8") as f:
            self.expanded_data = json.load(f)
            
            # 对于扩充数据中存在但原始数据中不存在的槽位，添加到顺序列表末尾
            for slot in self.expanded_data:
                if slot not in self.slot_order:
                    self.slot_order.append(slot)
                    
            # 对于扩充数据中存在但原始数据中不存在的槽位值，添加到相应槽位的顺序列表末尾
            for slot, slot_values in self.expanded_data.items():
                if slot not in self.slot_value_order:
                    self.slot_value_order[slot] = []
                    
                for slot_value in slot_values:
                    if slot in self.slot_value_order and slot_value not in self.slot_value_order[slot]:
                        self.slot_value_order[slot].append(slot_value)
    
    def merge_keywords(self) -> None:
        """合并关键词数据"""
        print("合并关键词数据...")
        
        # 首先复制原始数据到合并结果
        self.merged_data = self.original_data.copy()
        
        # 记录新增槽位和槽位值的数量
        new_slots = 0
        new_slot_values = 0
        added_keywords = 0
        
        # 遍历扩充数据，合并到原始数据中
        for slot, slot_values in self.expanded_data.items():
            # 如果是新的槽位，直接添加整个槽位
            if slot not in self.merged_data:
                self.merged_data[slot] = slot_values
                new_slots += 1
                new_slot_values += len(slot_values)
                
                # 计算添加的关键词数量
                for keywords in slot_values.values():
                    added_keywords += len(keywords)
                
                continue
            
            # 处理现有槽位的情况
            for slot_value, keywords in slot_values.items():
                # 如果是新的槽位值，直接添加整个槽位值
                if slot_value not in self.merged_data[slot]:
                    self.merged_data[slot][slot_value] = keywords
                    new_slot_values += 1
                    added_keywords += len(keywords)
                    continue
                
                # 合并关键词，确保不重复
                original_keywords_set = set(self.merged_data[slot][slot_value])
                new_keywords = [kw for kw in keywords if kw not in original_keywords_set]
                
                if new_keywords:
                    self.merged_data[slot][slot_value].extend(new_keywords)
                    added_keywords += len(new_keywords)
        
        # 对所有合并后的关键词按长度从长到短排序
        for slot, slot_values in self.merged_data.items():
            for slot_value, keywords in slot_values.items():
                # 去重（以防万一）
                unique_keywords = list(set(keywords))
                # 按关键词长度从长到短排序
                self.merged_data[slot][slot_value] = sorted(unique_keywords, key=len, reverse=True)
        
        print(f"合并完成:")
        print(f"- 新增槽位: {new_slots}")
        print(f"- 新增槽位值: {new_slot_values}")
        print(f"- 新增关键词: {added_keywords}")
    
    def save_merged_data(self) -> None:
        """保存合并后的关键词数据，保持原始文件中的顺序"""
        # 创建有序字典用于保存结果
        ordered_data = {}
        
        # 按照原始顺序重建数据结构
        for slot in self.slot_order:
            if slot in self.merged_data:
                ordered_data[slot] = {}
                
                # 按照原始槽位值顺序添加
                for slot_value in self.slot_value_order.get(slot, []):
                    if slot_value in self.merged_data[slot]:
                        ordered_data[slot][slot_value] = self.merged_data[slot][slot_value]
                
                # 添加任何可能漏掉的槽位值
                for slot_value in self.merged_data[slot]:
                    if slot_value not in ordered_data[slot]:
                        ordered_data[slot][slot_value] = self.merged_data[slot][slot_value]
        
        # 保存有序结果
        with open(self.output_file, "w", encoding="utf-8") as f:
            json.dump(ordered_data, f, ensure_ascii=False, indent=4)
        
        print(f"合并后的关键词文件已保存至: {self.output_file}")
    
    def run(self) -> None:
        """运行关键词合并流程"""
        self.load_data()
        self.merge_keywords()
        self.save_merged_data()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="关键词合并工具")
    parser.add_argument("--original", "-o", default="keywords_chinese.json", help="原始关键词JSON文件")
    parser.add_argument("--expanded", "-e", default="keywords_chinese_expanded.json", help="扩充关键词JSON文件")
    parser.add_argument("--output", "-m", default="keywords_chinese_merged.json", help="输出的合并后JSON文件")
    
    args = parser.parse_args()
    
    # 创建并运行关键词合并器
    merger = KeywordMerger(args.original, args.expanded, args.output)
    merger.run()


if __name__ == "__main__":
    main() 