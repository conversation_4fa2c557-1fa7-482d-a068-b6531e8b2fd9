#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
将keywords.json中的槽位名和槽位值变量名替换为对应的中文name
"""

import json
import copy
import csv
import os

# 读取slot_definitions.py中的槽位定义
def get_slot_definitions():
    # 这里直接使用硬编码方式获取槽位定义
    # 实际应用中可以通过import导入SLOTS变量
    import sys
    import os
    
    # 获取项目根目录路径
    root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
    
    # 将根目录添加到系统路径
    sys.path.insert(0, root_dir)
    
    # 导入SLOTS
    from resource.slot_definitions import SLOTS
    return SLOTS

# 读取keywords.json文件
def read_keywords_json(file_path="resource/keywords.json"):
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

# 创建槽位和槽位值的映射字典
def create_mapping_dict(slots):
    slot_mapping = {}
    slot_value_mapping = {}
    
    for slot_key, slot_info in slots.items():
        slot_mapping[slot_key] = slot_info["name"]
        slot_value_mapping[slot_key] = {}
        
        for value_key, value_info in slot_info["values"].items():
            slot_value_mapping[slot_key][value_key] = value_info["name"]
    
    return slot_mapping, slot_value_mapping

# 创建反向映射字典，用于从中文名称转回英文变量名
def create_reverse_mapping_dict(slots):
    reverse_slot_mapping = {}
    reverse_slot_value_mapping = {}
    
    for slot_key, slot_info in slots.items():
        chinese_slot_name = slot_info["name"]
        reverse_slot_mapping[chinese_slot_name] = slot_key
        reverse_slot_value_mapping[chinese_slot_name] = {}
        
        for value_key, value_info in slot_info["values"].items():
            chinese_value_name = value_info["name"]
            reverse_slot_value_mapping[chinese_slot_name][chinese_value_name] = value_key
    
    return reverse_slot_mapping, reverse_slot_value_mapping

# 转换keywords.json为中文名称版本
def convert_to_chinese_names(keywords, slot_mapping, slot_value_mapping):
    result = {}
    
    for slot_key, slot_values in keywords.items():
        if slot_key in slot_mapping:
            chinese_slot_name = slot_mapping[slot_key]
            result[chinese_slot_name] = {}
            
            for value_key, keywords_list in slot_values.items():
                if value_key in slot_value_mapping.get(slot_key, {}):
                    chinese_value_name = slot_value_mapping[slot_key][value_key]
                    result[chinese_slot_name][chinese_value_name] = keywords_list
                else:
                    # 如果找不到对应的中文名，则保留原键名
                    result[chinese_slot_name][value_key] = keywords_list
        else:
            # 如果找不到对应的中文名，则保留原键名
            result[slot_key] = copy.deepcopy(slot_values)
    
    return result

# 转换中文名称版本的keywords.json回英文变量名版本
def convert_to_english_names(chinese_keywords, reverse_slot_mapping, reverse_slot_value_mapping):
    result = {}
    
    for chinese_slot_name, slot_values in chinese_keywords.items():
        if chinese_slot_name in reverse_slot_mapping:
            slot_key = reverse_slot_mapping[chinese_slot_name]
            result[slot_key] = {}
            
            for chinese_value_name, keywords_list in slot_values.items():
                if chinese_value_name in reverse_slot_value_mapping.get(chinese_slot_name, {}):
                    value_key = reverse_slot_value_mapping[chinese_slot_name][chinese_value_name]
                    result[slot_key][value_key] = keywords_list
                else:
                    # 如果找不到对应的英文变量名，则保留原中文名
                    result[slot_key][chinese_value_name] = keywords_list
        else:
            # 如果找不到对应的英文变量名，则保留原中文名
            result[chinese_slot_name] = copy.deepcopy(slot_values)
    
    return result

# 保存转换后的JSON文件
def save_chinese_keywords(data, output_file="resource/keywords_chinese.json"):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)
    
    print(f"中文版keywords已保存至: {output_file}")

# 保存转换后的英文JSON文件
def save_english_keywords(data, output_file="resource/keywords_english.json"):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)
    
    print(f"英文版keywords已保存至: {output_file}")

# 导出为CSV格式文件
def export_to_csv(data, output_file="resource/keywords_chinese.csv"):
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        csv_writer = csv.writer(f)
        # 写入CSV头部
        csv_writer.writerow(["槽位", "槽位值", "关键词"])
        
        # 写入数据
        for slot_name, values in data.items():
            for value_name, keywords_list in values.items():
                for keyword in keywords_list:
                    csv_writer.writerow([slot_name, value_name, keyword])
    
    print(f"CSV格式数据已保存至: {output_file}")

# 从CSV文件转回英文JSON格式
def convert_csv_to_english_json(csv_file="resource/keywords_chinese.csv", output_file="resource/keywords_english.json", slots=None):
    if slots is None:
        slots = get_slot_definitions()
    
    # 创建反向映射字典
    reverse_slot_mapping, reverse_slot_value_mapping = create_reverse_mapping_dict(slots)
    
    # 读取CSV文件
    result = {}
    
    with open(csv_file, 'r', encoding='utf-8', newline='') as f:
        csv_reader = csv.reader(f)
        next(csv_reader)  # 跳过头部
        
        for row in csv_reader:
            if len(row) < 3:
                continue
                
            chinese_slot_name = row[0]
            chinese_value_name = row[1]
            keyword = row[2]
            
            # 转换回英文变量名
            if chinese_slot_name in reverse_slot_mapping:
                slot_key = reverse_slot_mapping[chinese_slot_name]
                
                if chinese_value_name in reverse_slot_value_mapping.get(chinese_slot_name, {}):
                    value_key = reverse_slot_value_mapping[chinese_slot_name][chinese_value_name]
                    
                    # 构建结果
                    if slot_key not in result:
                        result[slot_key] = {}
                    
                    if value_key not in result[slot_key]:
                        result[slot_key][value_key] = []
                    
                    if keyword not in result[slot_key][value_key]:
                        result[slot_key][value_key].append(keyword)
                else:
                    # 如果找不到对应的英文变量名，保留中文名
                    if slot_key not in result:
                        result[slot_key] = {}
                    
                    if chinese_value_name not in result[slot_key]:
                        result[slot_key][chinese_value_name] = []
                    
                    if keyword not in result[slot_key][chinese_value_name]:
                        result[slot_key][chinese_value_name].append(keyword)
            else:
                # 如果找不到对应的英文变量名，保留中文名
                if chinese_slot_name not in result:
                    result[chinese_slot_name] = {}
                
                if chinese_value_name not in result[chinese_slot_name]:
                    result[chinese_slot_name][chinese_value_name] = []
                
                if keyword not in result[chinese_slot_name][chinese_value_name]:
                    result[chinese_slot_name][chinese_value_name].append(keyword)
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=4)
    
    print(f"英文版keywords已保存至: {output_file}")
    
    return result

def main():
    # 获取槽位定义
    slots = get_slot_definitions()
    
    # 创建命令行参数解析
    if len(os.sys.argv) > 1:
        if os.sys.argv[1] == "csv2json":
            # 如果有csv2json参数，则执行CSV转JSON功能
            convert_csv_to_english_json()
            return
        elif os.sys.argv[1] == "chinese2english":
            # 如果有chinese2english参数，则执行中文JSON转英文JSON功能
            chinese_file = os.sys.argv[2] if len(os.sys.argv) > 2 else "resource/keywords_chinese.json"
            output_file = os.sys.argv[3] if len(os.sys.argv) > 3 else "resource/keywords_english.json"
            
            # 读取中文JSON文件
            with open(chinese_file, 'r', encoding='utf-8') as f:
                chinese_keywords = json.load(f)
            
            # 创建反向映射字典
            reverse_slot_mapping, reverse_slot_value_mapping = create_reverse_mapping_dict(slots)
            
            # 转换为英文变量名
            english_keywords = convert_to_english_names(chinese_keywords, reverse_slot_mapping, reverse_slot_value_mapping)
            
            # 保存结果
            save_english_keywords(english_keywords, output_file)
            return
    
    # 读取原始keywords.json
    keywords = read_keywords_json()
    
    # 创建映射字典
    slot_mapping, slot_value_mapping = create_mapping_dict(slots)
    
    # 转换为中文名称
    chinese_keywords = convert_to_chinese_names(keywords, slot_mapping, slot_value_mapping)
    
    # 保存JSON结果
    save_chinese_keywords(chinese_keywords)
    
    # 导出CSV格式
    export_to_csv(chinese_keywords)
    
    # 打印部分结果示例
    print("转换示例:")
    for i, (slot, values) in enumerate(chinese_keywords.items()):
        print(f"槽位: {slot}")
        for j, (value_name, keywords) in enumerate(values.items()):
            print(f"  值: {value_name}")
            print(f"  关键词: {keywords[:2]}{'...' if len(keywords) > 2 else ''}")
        if i >= 2:  # 只打印前3个槽位示例
            print("...")
            break
    
    # 如果需要将CSV转回英文JSON格式，可以取消下面的注释
    # convert_csv_to_english_json()

if __name__ == "__main__":
    # 主程序入口
    main() 