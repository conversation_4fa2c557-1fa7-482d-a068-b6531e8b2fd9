2025-04-01 04:40:15,005 - INFO - 系统启动
2025-04-01 04:40:15,005 - INFO - 任务生产者启动
2025-04-01 04:40:15,005 - INFO - 处理者 #0 启动
2025-04-01 04:40:15,005 - INFO - 结果写入者启动
2025-04-01 04:40:15,006 - INFO - 监控线程启动
2025-04-01 04:40:15,006 - INFO - 所有任务已添加到队列
2025-04-01 04:40:15,006 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:40:15,006 - INFO - 任务生产者已完成
2025-04-01 04:40:15,007 - INFO - 任务生产者已完成
2025-04-01 04:40:20,012 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:40:25,015 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:40:30,020 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:40:35,026 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:40:40,031 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:40:45,037 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:40:50,042 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:40:50,814 - ERROR - 任务 task_0 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    slot = task["slot"]
KeyError: 'slot'

2025-04-01 04:40:50,814 - INFO - 任务 task_0 将在 5 秒后重试 (第 1 次)
2025-04-01 04:40:55,048 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    slot = task["slot"]
KeyError: 'slot'

2025-04-01 04:40:55,815 - INFO - 任务 task_0 已重新入队
2025-04-01 04:41:00,053 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1
2025-04-01 04:41:05,059 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1
2025-04-01 04:41:10,064 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1
2025-04-01 04:41:15,070 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1
2025-04-01 04:41:20,071 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1
2025-04-01 04:41:25,076 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1
2025-04-01 04:41:30,082 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1
2025-04-01 04:41:35,087 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1
2025-04-01 04:41:39,561 - ERROR - 任务 task_1 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    slot = task["slot"]
KeyError: 'slot'

2025-04-01 04:41:39,561 - INFO - 任务 task_1 将在 5 秒后重试 (第 1 次)
2025-04-01 04:41:40,093 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：2 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    slot = task["slot"]
KeyError: 'slot'

2025-04-01 04:41:44,562 - INFO - 任务 task_1 已重新入队
2025-04-01 04:41:45,098 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：2
2025-04-01 04:41:50,104 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：2
2025-04-01 04:41:55,109 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：2
2025-04-01 04:42:00,115 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：2
2025-04-01 04:42:05,120 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：2
2025-04-01 04:42:10,126 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：2
2025-04-01 04:42:15,131 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：2
2025-04-01 04:42:18,674 - ERROR - 任务 task_2 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    slot = task["slot"]
KeyError: 'slot'

2025-04-01 04:42:18,674 - INFO - 任务 task_2 将在 5 秒后重试 (第 1 次)
2025-04-01 04:42:20,136 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：3 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    slot = task["slot"]
KeyError: 'slot'

2025-04-01 04:42:23,675 - INFO - 任务 task_2 已重新入队
2025-04-01 04:42:25,142 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：3
2025-04-01 04:42:30,147 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：3
2025-04-01 04:42:35,153 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：3
2025-04-01 04:42:40,155 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：3
2025-04-01 04:42:45,160 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：3
2025-04-01 04:42:50,166 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：3
2025-04-01 04:42:55,171 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：3
2025-04-01 04:42:58,187 - ERROR - 任务 task_3 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    raw_data = task["raw_data"]
KeyError: 'slot'

2025-04-01 04:42:58,187 - INFO - 任务 task_3 将在 5 秒后重试 (第 1 次)
2025-04-01 04:43:00,177 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    raw_data = task["raw_data"]
KeyError: 'slot'

2025-04-01 04:43:03,188 - INFO - 任务 task_3 已重新入队
2025-04-01 04:43:05,182 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4
2025-04-01 04:43:10,187 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4
2025-04-01 04:43:15,193 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4
2025-04-01 04:43:20,199 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4
2025-04-01 04:43:25,204 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4
2025-04-01 04:43:30,210 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4
2025-04-01 04:43:35,215 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4
2025-04-01 04:43:40,220 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：4
2025-04-01 04:43:44,426 - ERROR - 任务 task_4 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    response: 大模型的响应
KeyError: 'slot'

2025-04-01 04:43:44,426 - INFO - 任务 task_4 将在 5 秒后重试 (第 1 次)
2025-04-01 04:43:45,226 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：5 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    response: 大模型的响应
KeyError: 'slot'

2025-04-01 04:43:49,427 - INFO - 任务 task_4 已重新入队
2025-04-01 04:43:50,231 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：5
2025-04-01 04:43:55,237 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：5
2025-04-01 04:44:00,242 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：5
2025-04-01 04:44:05,247 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：5
2025-04-01 04:44:10,252 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：5
2025-04-01 04:44:15,258 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：5
2025-04-01 04:44:20,263 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：5
2025-04-01 04:44:24,530 - ERROR - 任务 task_5 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    response: 大模型的响应
KeyError: 'slot'

2025-04-01 04:44:24,530 - INFO - 任务 task_5 将在 5 秒后重试 (第 1 次)
2025-04-01 04:44:25,269 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    response: 大模型的响应
KeyError: 'slot'

2025-04-01 04:44:29,531 - INFO - 任务 task_5 已重新入队
2025-04-01 04:44:30,274 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6
2025-04-01 04:44:35,280 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6
2025-04-01 04:44:40,285 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6
2025-04-01 04:44:45,291 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6
2025-04-01 04:44:50,296 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6
2025-04-01 04:44:55,302 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6
2025-04-01 04:45:00,307 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6
2025-04-01 04:45:05,312 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：6
2025-04-01 04:45:08,684 - ERROR - 任务 task_6 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    response: 大模型的响应
KeyError: 'slot'

2025-04-01 04:45:08,684 - INFO - 任务 task_6 将在 5 秒后重试 (第 1 次)
2025-04-01 04:45:10,318 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    response: 大模型的响应
KeyError: 'slot'

2025-04-01 04:45:13,684 - INFO - 任务 task_6 已重新入队
2025-04-01 04:45:15,323 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7
2025-04-01 04:45:20,329 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7
2025-04-01 04:45:25,334 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7
2025-04-01 04:45:30,340 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7
2025-04-01 04:45:35,345 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7
2025-04-01 04:45:40,351 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7
2025-04-01 04:45:45,356 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7
2025-04-01 04:45:50,360 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：7
2025-04-01 04:45:54,619 - ERROR - 任务 task_7 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    response: 大模型的响应
KeyError: 'slot'

2025-04-01 04:45:54,619 - INFO - 任务 task_7 将在 5 秒后重试 (第 1 次)
2025-04-01 04:45:55,365 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：8 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 143, in process_response
    response: 大模型的响应
KeyError: 'slot'

2025-04-01 04:45:58,487 - INFO - 收到中断信号，准备安全退出
2025-04-01 04:45:58,488 - INFO - 系统已安全退出
