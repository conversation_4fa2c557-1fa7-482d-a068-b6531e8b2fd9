2025-04-01 04:39:20,077 - INFO - 系统启动
2025-04-01 04:39:20,077 - INFO - 任务生产者启动
2025-04-01 04:39:20,077 - INFO - 处理者 #0 启动
2025-04-01 04:39:20,077 - INFO - 结果写入者启动
2025-04-01 04:39:20,078 - INFO - 所有任务已添加到队列
2025-04-01 04:39:20,078 - INFO - 监控线程启动
2025-04-01 04:39:20,078 - INFO - 任务生产者已完成
2025-04-01 04:39:20,078 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:39:20,079 - INFO - 任务生产者已完成
2025-04-01 04:39:25,084 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:39:30,090 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:39:35,095 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:39:40,100 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:39:45,106 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:39:50,111 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:39:55,117 - INFO - 状态：运行中 | 任务队列：88 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：0
2025-04-01 04:39:56,619 - ERROR - 任务 task_0 处理失败: 'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 142, in process_response
    slot = task["slot"]
KeyError: 'slot'

2025-04-01 04:39:56,619 - INFO - 任务 task_0 将在 5 秒后重试 (第 1 次)
2025-04-01 04:40:00,122 - INFO - 状态：运行中 | 任务队列：87 | 结果队列：0 | 已处理：0 | 成功：0 | 失败：0 | 重试：1 | 最新错误：'slot'Traceback (most recent call last):
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 211, in run
    result = self.process_task(task)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/synthesis_engine.py", line 279, in process_task
    processed_result = engine.process_response(task, llm_response)
  File "/matrix/0-Work/0_dev/slots_extra/test/update_keywords/keywords_expander.py", line 142, in process_response
    slot = task["slot"]
KeyError: 'slot'

2025-04-01 04:40:01,620 - INFO - 任务 task_0 已重新入队
2025-04-01 04:40:03,137 - INFO - 收到中断信号，准备安全退出
2025-04-01 04:40:03,138 - INFO - 系统已安全退出
