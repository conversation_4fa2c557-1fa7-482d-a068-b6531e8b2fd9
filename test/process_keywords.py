import json
import os
from tqdm import tqdm

def load_json_file(file_path):
    """加载JSON文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_json_file(data, file_path):
    """保存JSON文件"""
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)

def build_keyword_mapping(data):
    """构建关键词到槽位-槽位值的映射"""
    keyword_mapping = {}
    
    # 遍历每个槽位类型
    for slot_type, slot_values in data.items():
        # 遍历每个槽位值
        for slot_value, keywords in slot_values.items():
            # 遍历每个关键词
            for keyword in keywords:
                if keyword not in keyword_mapping:
                    keyword_mapping[keyword] = []
                
                # 添加槽位-槽位值对
                keyword_mapping[keyword].append({
                    "slot_type": slot_type,
                    "slot_value": slot_value
                })
    
    return keyword_mapping

def process_keywords(input_file, output_file):
    """处理关键词文件，让用户选择关键词应该属于哪个槽位值"""
    # 加载原始数据
    data = load_json_file(input_file)
    
    # 构建关键词映射
    keyword_mapping = build_keyword_mapping(data)
    
    # 找出需要用户选择的关键词（对应多个槽位的关键词）
    ambiguous_keywords = {k: v for k, v in keyword_mapping.items() if len(v) > 1}
    
    print(f"发现 {len(keyword_mapping)} 个关键词，其中 {len(ambiguous_keywords)} 个关键词属于多个槽位，需要用户选择。")
    
    # 创建新的数据结构保存结果
    new_data = {slot_type: {slot_value: [] for slot_value in slot_values} for slot_type, slot_values in data.items()}
    
    # 先处理没有歧义的关键词
    for keyword, mappings in tqdm(keyword_mapping.items(), desc="处理无歧义关键词"):
        if len(mappings) == 1:
            slot_type = mappings[0]["slot_type"]
            slot_value = mappings[0]["slot_value"]
            new_data[slot_type][slot_value].append(keyword)
    
    # 处理有歧义的关键词
    for i, (keyword, mappings) in enumerate(ambiguous_keywords.items(), 1):
        print(f"\n处理进度: {i}/{len(ambiguous_keywords)}")
        print(f"关键词：'{keyword}' 出现在以下槽位中:")
        
        for idx, mapping in enumerate(mappings, 1):
            print(f"{idx}. {mapping['slot_type']} -> {mapping['slot_value']}")
        
        # 请用户选择
        while True:
            try:
                choice = input(f"请选择 '{keyword}' 应该属于哪个槽位 (1-{len(mappings)})，或输入'跳过'：")
                if choice.lower() == '跳过':
                    print(f"已跳过关键词 '{keyword}'")
                    break
                
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(mappings):
                    selected = mappings[choice_idx]
                    new_data[selected["slot_type"]][selected["slot_value"]].append(keyword)
                    print(f"已将 '{keyword}' 添加到 {selected['slot_type']} -> {selected['slot_value']}")
                    break
                else:
                    print(f"请输入1-{len(mappings)}之间的数字")
            except ValueError:
                print("请输入有效的数字或'跳过'")
    
    # 保存结果
    save_json_file(new_data, output_file)
    print(f"\n处理完成! 结果已保存到 {output_file}")

if __name__ == "__main__":
    input_file = "data/keywords_chinese.json"
    output_file = "data/keywords_processed.json"
    
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
    else:
        process_keywords(input_file, output_file)