import pandas as pd
import requests
import json
import os
import ast
import argparse
from tqdm import tqdm
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Alignment

def normalize_dict(item, service_type='all'):
    """标准化字典，处理单引号形式的字典，并清理特殊字符

    处理以下问题：
    1. 非打印字符如U+00A0（不间断空格）
    2. 格式不正确的节点或字符串

    Args:
        item: 要标准化的字典或字符串
        service_type: 服务类型，'all'(总控)或'slots'(槽位)
    """
    if not isinstance(item, str):
        return item

    # 清理非打印字符
    # 替换不间断空格(U+00A0)为普通空格
    cleaned_str = item.replace('\xa0', ' ')

    # 替换其他可能的不可见字符
    for i in range(32):
        if i not in [9, 10, 13]:  # 保留制表符、换行和回车
            cleaned_str = cleaned_str.replace(chr(i), '')

    # 处理引号不匹配的问题
    # 计算单引号和双引号的数量
    single_quotes = cleaned_str.count("'")
    double_quotes = cleaned_str.count('"')

    # 如果引号数量不是偶数，尝试修复
    if single_quotes % 2 != 0:
        # 尝试将最后一个单引号替换为双引号
        last_quote_pos = cleaned_str.rfind("'")
        if last_quote_pos != -1:
            cleaned_str = cleaned_str[:last_quote_pos] + '"' + cleaned_str[last_quote_pos+1:]

    if double_quotes % 2 != 0:
        # 尝试将最后一个双引号替换为单引号
        last_quote_pos = cleaned_str.rfind('"')
        if last_quote_pos != -1:
            cleaned_str = cleaned_str[:last_quote_pos] + "'" + cleaned_str[last_quote_pos+1:]

    # 尝试解析字符串为Python对象
    try:
        result = ast.literal_eval(cleaned_str)

        # 如果是槽位服务，调整预期结果格式
        if service_type == 'slots' and isinstance(result, list) and len(result) > 0:
            # 检查是否是双层嵌套的list结构
            if all(isinstance(item, list) for item in result):
                # 将内层list中的所有condition拿出来，用单层list包裹
                flattened = []
                for inner_list in result:
                    flattened.extend(inner_list)
                return [flattened]  # 返回单层list包裹的结果

        return result
    except (SyntaxError, ValueError) as e:
        # 如果解析失败，尝试使用json解析
        try:
            # 将单引号替换为双引号以符合JSON格式
            json_str = cleaned_str.replace("'", '"')
            result = json.loads(json_str)

            # 如果是槽位服务，调整预期结果格式
            if service_type == 'slots' and isinstance(result, list) and len(result) > 0:
                # 检查是否是双层嵌套的list结构
                if all(isinstance(item, list) for item in result):
                    # 将内层list中的所有condition拿出来，用单层list包裹
                    flattened = []
                    for inner_list in result:
                        flattened.extend(inner_list)
                    return [flattened]  # 返回单层list包裹的结果

            return result
        except json.JSONDecodeError:
            # 如果仍然失败，尝试更激进的修复方法
            try:
                # 移除所有空格并重试
                no_space_str = cleaned_str.replace(" ", "")
                result = ast.literal_eval(no_space_str)

                # 如果是槽位服务，调整预期结果格式
                if service_type == 'slots' and isinstance(result, list) and len(result) > 0:
                    # 检查是否是双层嵌套的list结构
                    if all(isinstance(item, list) for item in result):
                        # 将内层list中的所有condition拿出来，用单层list包裹
                        flattened = []
                        for inner_list in result:
                            flattened.extend(inner_list)
                        return [flattened]  # 返回单层list包裹的结果

                return result
            except (SyntaxError, ValueError):
                # 如果所有尝试都失败，抛出原始异常
                raise ValueError(f"无法解析字符串为有效的Python对象: {e}")

def clean_conditions(conditions):
    """清洗conditions，移除lower_bound字段"""
    for outter_conditions in conditions:
        for i, inner_condition in enumerate(outter_conditions):
            # 补充删除字段
            if "lower_bound" in inner_condition:
                del inner_condition["lower_bound"]
            if 'value' not in inner_condition:
                inner_condition.update({"value": None})
            if 'minValue' not in inner_condition:
                inner_condition.update({"minValue": None})
            if 'maxValue' not in inner_condition:
                inner_condition.update({"maxValue": None})

            # 调整字段顺序：key、operator、value、minValue、maxValue
            ordered_condition = {}
            # 按照指定顺序添加字段
            if 'key' in inner_condition:
                ordered_condition['key'] = inner_condition['key']
            if 'operator' in inner_condition:
                ordered_condition['operator'] = inner_condition['operator']
            if 'value' in inner_condition:
                ordered_condition['value'] = inner_condition['value']
            if 'minValue' in inner_condition:
                ordered_condition['minValue'] = inner_condition['minValue']
            if 'maxValue' in inner_condition:
                ordered_condition['maxValue'] = inner_condition['maxValue']

            # 添加其他可能存在的字段
            for key, value in inner_condition.items():
                if key not in ordered_condition:
                    ordered_condition[key] = value

            # 用排序后的字典替换原字典
            outter_conditions[i] = ordered_condition

    return conditions

def compare_items(actual, expected):
    """比对两个字典是否一致"""
    if not isinstance(actual, dict) or not isinstance(expected, dict):
        return actual == expected

    # 检查字段名称、类型和值是否一致
    for key in set(actual.keys()) | set(expected.keys()):
        if key not in actual or key not in expected:
            return False

        if type(actual[key]) != type(expected[key]):
            return False

        if isinstance(actual[key], dict):
            if not compare_items(actual[key], expected[key]):
                return False
        elif isinstance(actual[key], list):
            if len(actual[key]) != len(expected[key]):
                return False

            for a_item, e_item in zip(sorted(actual[key]), sorted(expected[key])):
                if isinstance(a_item, dict):
                    if not compare_items(a_item, e_item):
                        return False
                elif a_item != e_item:
                    return False
        elif actual[key] != expected[key]:
            return False

    return True

def get_structure_info(conditions):
    """获取条件结构的信息，用于调试"""
    if not isinstance(conditions, list):
        return f"非列表类型: {type(conditions)}"

    result = f"列表长度: {len(conditions)}"

    if len(conditions) > 0:
        result += f", 第一层元素类型: {type(conditions[0])}"

        if isinstance(conditions[0], list):
            result += f", 第一个内层列表长度: {len(conditions[0])}"

            if len(conditions[0]) > 0:
                result += f", 内层元素类型: {type(conditions[0][0])}"

                if isinstance(conditions[0][0], dict) and len(conditions[0][0]) > 0:
                    result += f", 字典键: {list(conditions[0][0].keys())}"

    return result

def compare_conditions(actual_conditions, expected_conditions):
    """比对conditions列表"""
    if len(actual_conditions) != len(expected_conditions):
        # 添加结构信息，便于调试
        actual_info = get_structure_info(actual_conditions)
        expected_info = get_structure_info(expected_conditions)
        print(f"长度不匹配: 实际结果({actual_info}) vs 预期结果({expected_info})")
        return False

    # 将条件排序以便比对
    actual_sorted = sorted(actual_conditions, key=lambda x: json.dumps(x, sort_keys=True))
    expected_sorted = sorted(expected_conditions, key=lambda x: json.dumps(x, sort_keys=True))

    for actual_item, expected_item in zip(actual_sorted, expected_sorted):
        if not compare_items(actual_item, expected_item):
            return False

    return True

def extract_conditions_from_all(result):
    """从总控服务的返回结果中提取conditions"""
    if 'data' in result and 'conditions' in result['data']:
        return clean_conditions(result['data']['conditions'])
    return None

def extract_conditions_from_slots(result):
    """从槽位服务的返回结果中提取conditions

    槽位服务返回的结果中，data字段是一个list，每个item是一个字典，
    需要取其中condition字段作为一个condition数据
    """
    if 'data' in result and isinstance(result['data'], list):
        all_conditions = []
        for item in result['data']:
            if 'condition' in item and item['condition']:
                # 将每个item中的condition添加到结果中
                all_conditions.append(item['condition'])

        if all_conditions:
            # 将所有condition包装成与总控服务相同的格式
            return clean_conditions([all_conditions])

    return None

def process_excel_file(input_path, api_url, service_type='all'):
    """处理Excel文件，发送请求并比对结果

    Args:
        input_path: Excel文件路径
        api_url: API接口地址
        service_type: 服务类型，可选值为'all'(总控)或'slots'(槽位)
    """
    # 获取输出文件路径
    filename, ext = os.path.splitext(input_path)
    output_path = f"{filename}_output_{service_type}{ext}"

    # 读取Excel文件
    df = pd.read_excel(input_path)

    # 提取原始列名
    original_columns = list(df.columns)

    # 创建新的结果列
    df['预期结果（格式化）'] = ""
    df['实际结果（格式化）'] = ""
    df['结果比对'] = ""

    # 遍历每一行
    for index, row in tqdm(df.iterrows(), total=len(df), desc=f"处理测试案例 ({service_type})"):
        question = row['语料文本']
        expected_result = row['预期结果']

        # 构造请求参数
        payload = {
            "messageId": "1",
            "sessionId": "1",
            "userQuery": question,
            "question": question,
            "originSlots": {},
            }

        try:
            # 发送请求
            response = requests.post(api_url, json=payload)
            response.raise_for_status()

            # 解析返回结果
            result = response.json()

            # 根据服务类型选择不同的解析方法
            if service_type == 'all':
                actual_conditions = extract_conditions_from_all(result)
            elif service_type == 'slots':
                actual_conditions = extract_conditions_from_slots(result)
            else:
                raise ValueError(f"不支持的服务类型: {service_type}")

            if actual_conditions:
                # 存储格式化后的实际结果
                df.at[index, '实际结果（格式化）'] = json.dumps(actual_conditions, indent=1, ensure_ascii=False)

                # 处理预期结果
                try:
                    # 尝试将预期结果转换为Python对象，传入服务类型
                    expected_conditions = normalize_dict(expected_result, service_type)

                    # 清洗预期结果
                    expected_conditions = clean_conditions(expected_conditions)

                    # 存储格式化后的预期结果
                    df.at[index, '预期结果（格式化）'] = json.dumps(expected_conditions, indent=1, ensure_ascii=False)

                    # 比对结果
                    if compare_conditions(actual_conditions, expected_conditions):
                        df.at[index, '结果比对'] = "一致"
                    else:
                        # 获取结构信息，提供更详细的不匹配原因
                        actual_info = get_structure_info(actual_conditions)
                        expected_info = get_structure_info(expected_conditions)
                        df.at[index, '结果比对'] = f"不一致 [实际结果: {actual_info} | 预期结果: {expected_info}]"
                except Exception as e:
                    # 提供更详细的错误信息
                    error_msg = str(e)
                    error_type = type(e).__name__

                    # 记录原始预期结果，便于调试
                    df.at[index, '预期结果（格式化）'] = f"[错误] 原始文本: {expected_result[:100]}..." if len(expected_result) > 100 else expected_result

                    # 针对特定错误类型提供更友好的提示
                    if 'invalid non-printable character' in error_msg:
                        df.at[index, '结果比对'] = f"预期结果包含不可打印字符: {error_msg}"
                    elif 'malformed node or string' in error_msg:
                        df.at[index, '结果比对'] = f"预期结果格式错误(语法错误): {error_msg}"
                    elif 'invalid syntax' in error_msg:
                        df.at[index, '结果比对'] = f"预期结果语法错误: {error_msg}"
                    else:
                        df.at[index, '结果比对'] = f"预期结果格式错误({error_type}): {error_msg}"
            else:
                df.at[index, '结果比对'] = "API返回数据格式不正确"
        except Exception as e:
            df.at[index, '结果比对'] = f"请求错误: {str(e)}"

    # 重新排列列的顺序，确保结果列在原始列之后
    new_column_order = original_columns + ['预期结果（格式化）', '实际结果（格式化）', '结果比对']
    df = df[new_column_order]

    # 使用openpyxl保存结果，并设置单元格格式
    wb = Workbook()
    ws = wb.active

    # 添加表头
    headers = list(df.columns)
    ws.append(headers)

    # 添加数据行
    for _, row in df.iterrows():
        ws.append(row.tolist())

    # 设置所有单元格自动换行和垂直对齐方式
    for row in ws.iter_rows():
        for cell in row:
            cell.alignment = Alignment(wrap_text=True, vertical='top')

    # 自动调整列宽
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            if cell.value:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
        adjusted_width = min(max_length, 100)  # 限制最大宽度
        ws.column_dimensions[column_letter].width = adjusted_width

    # 保存结果
    wb.save(output_path)
    print(f"处理完成，结果已保存至: {output_path}")

if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='比对API返回结果与预期结果')

    # 添加命令行参数
    parser.add_argument('--input', '-i', type=str, default="test/测试案例含预期.xlsx",
                        help='输入Excel文件路径 (默认: test/测试案例含预期.xlsx)')

    parser.add_argument('--service', '-s', type=str, choices=['all', 'slots'], default='all',
                        help='服务类型: all(总控) 或 slots(槽位) (默认: all)')

    parser.add_argument('--url', '-u', type=str,
                        help='API接口地址 (如果不提供，将使用默认地址)')

    # 解析命令行参数
    args = parser.parse_args()

    # 根据服务类型设置默认API地址
    if args.url:
        api_url = args.url
    else:
        if args.service == 'all':
            api_url = "http://your-all-api-endpoint/path"  # 请替换为实际总控API地址
        else:  # slots
            api_url = "http://your-slots-api-endpoint/path"  # 请替换为实际槽位API地址

    print(f"使用服务类型: {args.service}")
    print(f"API地址: {api_url}")
    print(f"输入文件: {args.input}")

    # 处理Excel文件
    process_excel_file(args.input, api_url, args.service)