"""
槽位抽取服务测试脚本
"""

import json
import sys
import os
from typing import Dict, Any
import pandas as pd
import re
import traceback

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 导入槽位抽取服务
from app.services.slot_extractor_service import FinancialSlotExtractorService
from app.services.cache_service import cache_service

cache_service.start()

def run_test(text: str) -> Dict[str, Dict[str, Any]]:
    """
    运行槽位抽取测试
    
    Args:
        text (str): 要测试的文本
        
    Returns:
        Dict[str, Dict[str, Any]]: 抽取的槽位结果
    """
    # 创建槽位抽取服务实例
    extractor = FinancialSlotExtractorService()
    
    # 提取槽位
    results = extractor.extract_slots(text)
    
    return results

def format_result_text(text: str, results: Dict[str, list]) -> str:
    """
    格式化结果文本，将匹配到的文本替换为对应的值
    
    Args:
        text (str): 原始文本
        results (Dict[str, list]): 抽取结果，每个槽位对应一个结果列表
    
    Returns:
        str: 格式化后的文本
    """
    formatted_text = text
    
    if not results:
        return formatted_text
    
    # 按照匹配位置从后向前替换，避免位置偏移
    all_replacements = []
    for slot_name, slot_info_list in results.items():
        for slot_info in slot_info_list:
            if not slot_info.get('match'):
                continue
                
            match_text = slot_info['match']
            
            # 确定替换文本
            if 'minValue' in slot_info:
                # 如果有minValue，则处理为范围值
                min_value = slot_info['minValue']
                # 安全地获取maxValue，如果不存在则使用None
                max_value = slot_info.get('maxValue')
                replacement = f" <{min_value}~{max_value}> "
            elif 'value' in slot_info and slot_info['value']:
                # 如果有value且不为空，则使用value
                replacement = f" <{slot_info['value']}> "
            else:
                # 如果没有value，则使用slot_value
                replacement = f" <{slot_info.get('slot_value', '')}> "
                
            all_replacements.append((match_text, replacement))
    
    # 按照匹配文本长度降序排序，避免短文本替换影响长文本
    all_replacements.sort(key=lambda x: len(x[0]), reverse=True)
    
    for match_text, replacement in all_replacements:
        formatted_text = formatted_text.replace(match_text, replacement)
    
    return formatted_text

def save_results_to_csv(input_file: str, output_file: str):
    """
    从CSV文件读取问题，进行槽位抽取，并保存结果
    
    Args:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
    """
    # 读取输入CSV文件
    df = pd.read_csv(input_file)
    
    # 确保存在"问题"列
    if "问题" not in df.columns:
        raise ValueError("输入CSV文件必须包含'问题'列")
    
    # 处理每个问题并保存结果
    results = []
    for text in df["问题"]:
        # 添加调试输出
        print(f"处理问题: {text}")
        extracted_slots = run_test(text)
        
        # 打印详细的槽位信息，用于调试
        print("抽取的槽位信息:")
        for slot_name, slots_list in extracted_slots.items():
            print(f"  槽位: {slot_name}")
            for slot in slots_list:
                for key, value in slot.items():
                    print(f"    {key}: {value}")
        
        formatted_text = format_result_text(text, extracted_slots)
        results.append({
            "原问题": text,
            "槽位结果": json.dumps(extracted_slots, ensure_ascii=False, indent=2),
            "槽位模板": formatted_text
        })
    
    # 创建结果DataFrame并保存
    result_df = pd.DataFrame(results)
    result_df.to_csv(output_file, index=False, encoding='utf-8')

def main():
    """主函数，运行测试案例"""
    if len(sys.argv) != 3:
        print("使用方法: python test_slot_extractor.py input.csv output.csv")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        save_results_to_csv(input_file, output_file)
        print(f"测试完成，结果已保存至: {output_file}")
    except Exception as e:
        print(traceback.format_exc())
        print(f"处理过程中出现错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
