<mxfile host="65bd71144e">
    <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
        <mxGraphModel dx="1372" dy="757" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-0"/>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0"/>
                <mxCell id="es_service" value="ES服务&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(ESService)&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="200" y="200" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="cache_service" value="缓存服务&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(CacheService)&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="500" y="200" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="slot_extractor_service" value="槽位抽取服务&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(FinancialSlotExtractorService)&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#ffe6cc;strokeColor=#d79b00;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="800" y="200" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="elasticsearch" value="Elasticsearch&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(slot_extraction索引)&lt;/font&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontSize=14;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="200" y="400" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="cache_data" value="内存缓存&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(编译后的正则表达式)&lt;/font&gt;" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;fontSize=14;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="500" y="400" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="extractors" value="槽位抽取器&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(各类专用抽取器)&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="800" y="400" width="200" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="resource_files" value="资源文件&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(正则表达式、词库定义)&lt;/font&gt;" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fontSize=14;fillColor=#f8cecc;strokeColor=#b85450;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="200" y="600" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="api_interface" value="API接口&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(FastAPI路由)&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fillColor=#e1d5e7;strokeColor=#9673a6;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="500" y="600" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="user_request" value="用户请求&lt;br&gt;&lt;font style=&quot;font-size: 10px&quot;&gt;(文本输入)&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;fontSize=14;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;fontStyle=1" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="800" y="600" width="200" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="init_es_data" value="1. 初始化ES数据" style="endArrow=classic;html=1;fontSize=12;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="es_service" target="elasticsearch" edge="1">
                    <mxGeometry x="-0.2" y="20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="280" y="270" as="sourcePoint"/>
                        <mxPoint x="280" y="390" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="resource_to_es" value="2. 加载资源文件" style="endArrow=classic;html=1;fontSize=12;exitX=0.5;exitY=0;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="resource_files" target="elasticsearch" edge="1">
                    <mxGeometry x="-0.2" y="-20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="280" y="590" as="sourcePoint"/>
                        <mxPoint x="280" y="490" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="es_to_cache" value="3. 获取正则和词库数据" style="endArrow=classic;html=1;fontSize=12;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="es_service" target="cache_service" edge="1">
                    <mxGeometry x="-0.0667" y="20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="370" y="230" as="sourcePoint"/>
                        <mxPoint x="490" y="230" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cache_to_memory" value="4. 编译并缓存正则" style="endArrow=classic;html=1;fontSize=12;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="cache_service" target="cache_data" edge="1">
                    <mxGeometry x="-0.2" y="20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="580" y="270" as="sourcePoint"/>
                        <mxPoint x="580" y="390" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cache_to_extractor" value="5. 提供编译后的正则" style="endArrow=classic;html=1;fontSize=12;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="cache_service" target="slot_extractor_service" edge="1">
                    <mxGeometry x="-0.0714" y="20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="670" y="230" as="sourcePoint"/>
                        <mxPoint x="790" y="230" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="extractor_to_components" value="6. 初始化抽取器" style="endArrow=classic;html=1;fontSize=12;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="slot_extractor_service" target="extractors" edge="1">
                    <mxGeometry x="-0.2" y="20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="900" y="270" as="sourcePoint"/>
                        <mxPoint x="900" y="390" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="user_to_api" value="7. 发送文本" style="endArrow=classic;html=1;fontSize=12;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="user_request" target="api_interface" edge="1">
                    <mxGeometry x="-0.0714" y="-20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="790" y="640" as="sourcePoint"/>
                        <mxPoint x="670" y="640" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="api_to_extractor" value="8. 调用抽取服务" style="endArrow=classic;html=1;fontSize=12;exitX=1;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="api_interface" target="extractors" edge="1">
                    <mxGeometry x="0.0588" y="-30" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="670" y="590" as="sourcePoint"/>
                        <mxPoint x="790" y="490" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="extractor_to_cache" value="9. 获取缓存的正则" style="endArrow=classic;html=1;fontSize=12;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="extractors" target="cache_data" edge="1">
                    <mxGeometry x="-0.0714" y="-20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="790" y="440" as="sourcePoint"/>
                        <mxPoint x="670" y="440" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="api_response" value="10. 返回抽取结果" style="endArrow=classic;html=1;fontSize=12;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="extractors" target="user_request" edge="1">
                    <mxGeometry x="0.2" y="30" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="900" y="490" as="sourcePoint"/>
                        <mxPoint x="900" y="590" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="cache_update_timer" value="11. 定时更新缓存(5分钟)" style="endArrow=classic;html=1;fontSize=12;curved=1;dashed=1;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="cache_service" target="cache_service" edge="1">
                    <mxGeometry x="-0.2" y="-20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="480" y="180" as="sourcePoint"/>
                        <mxPoint x="480" y="180" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="460" y="150"/>
                        </Array>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="es_update" value="12. 更新ES数据" style="endArrow=classic;html=1;fontSize=12;curved=1;dashed=1;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="es_service" target="es_service" edge="1">
                    <mxGeometry x="-0.2" y="-20" width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="180" y="180" as="sourcePoint"/>
                        <mxPoint x="180" y="180" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="160" y="150"/>
                        </Array>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="data_types" value="ES数据类型:&#xa;- keywords: 关键词列表生成的正则表达式字符串&#xa;- regexs: 直接编写的正则表达式字符串&#xa;- tool_regexs: 工具类正则表达式字符串" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;align=left;spacingLeft=5;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="200" y="500" width="320" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="cache_types" value="缓存数据类型:&#xa;- _keywords_cache: 关键词正则表达式对象&#xa;- _regexs_cache: 直接编写的正则表达式对象&#xa;- _tool_regexs_cache: 工具类正则表达式对象" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;align=left;spacingLeft=5;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="540" y="500" width="320" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="extractor_types" value="抽取器类型:&#xa;- TimeExtractor: 时间抽取器&#xa;- AmountExtractor: 金额抽取器&#xa;- RateExtractor: 收益率抽取器&#xa;- RiskExtractor: 风险等级抽取器&#xa;- KeywordSlotExtractor: 关键词槽位抽取器" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fontSize=12;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;align=left;spacingLeft=5;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="864" y="491" width="280" height="90" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>